package com.xyy.wms.pda.contract.out.exception;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.exception.FillPick;
import com.xyy.wms.pda.bean.exception.PickUseStorageBean;
import com.xyy.wms.pda.bean.exception.PickUseStorageBeanNew;

import java.util.List;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 12/14/2019 11:54
 */
public interface SupplementPickUpContract {

    interface ISupplementPickUpModel extends IBaseModel {

        Observable<BaseResponseBean<List<PickUseStorageBean>>> getExceptionHandleUseStorage(String id);

        Observable<BaseResponseBean<BaseResponseBean>> commitPickUseStorage(PickUseStorageBeanNew pickUseStorageBean);
    }

    interface ISupplementPickUpView extends IBaseActivity {

        void getExceptionHandleUseStorageSuccess(BaseResponseBean<List<PickUseStorageBean>> baseResponseBean);

        void commitPickUseStorageSuccess(BaseResponseBean<BaseResponseBean> baseResponseBean);
    }

}
