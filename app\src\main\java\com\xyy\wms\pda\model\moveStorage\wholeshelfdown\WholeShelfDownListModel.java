package com.xyy.wms.pda.model.moveStorage.wholeshelfdown;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiMoveStorageService;
import com.xyy.wms.pda.api.ApiService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.common.MoveStorageFinishedBean;
import com.xyy.wms.pda.bean.common.RunningTaskResult;
import com.xyy.wms.pda.bean.instorage.shelf.InShelfResult;
import com.xyy.wms.pda.bean.moveStorage.AddTaskDetailBean;
import com.xyy.wms.pda.bean.moveStorage.PalletGetGoodsBean;
import com.xyy.wms.pda.bean.moveStorage.PalletGetGoodsResult;
import com.xyy.wms.pda.bean.moveStorage.ResourcePositionGoodsBean;
import com.xyy.wms.pda.bean.productCode.ProductCode;
import com.xyy.wms.pda.contract.movestorage.wholeshelf.WholeShelfDownListContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.List;

import io.reactivex.Observable;

/**
 * 整件下架-列表
 */
public class WholeShelfDownListModel extends BaseModel implements WholeShelfDownListContract.IWholeShelfDownListModel {

    public static WholeShelfDownListModel newInstance() {
        return new WholeShelfDownListModel();
    }

    /**
     * 查询商品明细
     */
    @Override
    public Observable<BaseResponseBean<List<PalletGetGoodsResult>>> getWholeShelfDownList(ResourcePositionGoodsBean resourcePositionGoodsBean) {
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).getProductsByAllocationList(resourcePositionGoodsBean)
                .compose(RxHelper.rxSchedulerHelper());
    }
    /**
     * 进行中任务
     */
    public Observable<BaseResponseBean<RunningTaskResult>> getRunningTask(){
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).getRunningTask()
            .compose(RxHelper.rxSchedulerHelper());
    }

    /**
     * 添加任务明细
     */
    public Observable<BaseResponseBean<Boolean>> addTaskDetail(AddTaskDetailBean addTaskDetailBean){
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).addTaskDetail(addTaskDetailBean)
            .compose(RxHelper.rxSchedulerHelper());
    }
    /**
     * 下架完成
     */
    public Observable<BaseResponseBean<Boolean>> setFinished(MoveStorageFinishedBean finishedBean){
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).setFinished(finishedBean)
            .compose(RxHelper.rxSchedulerHelper());
    }
}
