package com.xyy.wms.pda.model.moveStorage.car;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.api.ApiMoveStorageService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.common.CheckContainerCodeBean;
import com.xyy.wms.pda.bean.common.CheckContainerCodeResult;
import com.xyy.wms.pda.bean.common.LogicalRegionResult;
import com.xyy.wms.pda.bean.common.RunningTaskResult;
import com.xyy.wms.pda.bean.common.SourcePositionCodeBean;
import com.xyy.wms.pda.bean.instorage.shelf.CommitShelfResult;
import com.xyy.wms.pda.bean.instorage.shelf.ShelfDetailPost;
import com.xyy.wms.pda.bean.moveStorage.CarNumberBean;
import com.xyy.wms.pda.bean.moveStorage.CarNumberResult;
import com.xyy.wms.pda.bean.moveStorage.StartTaskBean;
import com.xyy.wms.pda.bean.moveStorage.StartTaskResult;
import com.xyy.wms.pda.contract.movestorage.car.CarDownContract;
import com.xyy.wms.pda.contract.movestorage.car.CarUpContract;
import com.xyy.wms.pda.contract.movestorage.scattered.ScatteredDownContract;
import com.xyy.wms.pda.contract.movestorage.wholeshelf.WholeShelfDownContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.List;
import java.util.Observer;

import io.reactivex.Observable;

/**
 * 上车 -  first step
 */
public class CarUpModel extends BaseModel implements CarUpContract.CarUpModel {
    public static CarUpModel newInstance() {
        return new CarUpModel();
    }
    /**
     * 开启任务
     */
    public Observable<BaseResponseBean<StartTaskResult>> startTask(StartTaskBean startTaskBean){
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).startTaskWholeShelfDown(startTaskBean)
            .compose(RxHelper.rxSchedulerHelper());
    }

    /**
     * 进行中任务
     */
    public Observable<BaseResponseBean<RunningTaskResult>> getRunningTask(){
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).getRunningTask()
            .compose(RxHelper.rxSchedulerHelper());
    }

    /**
     * 获取车牌
     */
    public Observable<BaseResponseBean<CarNumberResult>> getCarNumber(CarNumberBean carNumberBean){
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).getCarNumber(carNumberBean)
            .compose(RxHelper.rxSchedulerHelper());
    }
}
