package com.xyy.wms.pda.bean.purchaseexit;

import java.io.Serializable;

/**
 * Created by lwj on 2020-03-11.
 * <EMAIL>
 */
public class CacheAreaPdaBean implements Serializable {


    public String activityApplicationSum;//	;// string	@mock=0
    public String buildingCode;//		;// string	@mock=01
    public String busiType;//	;// string	@mock=9
    public String busiTypeName;//		;// string	@mock=购进退出
    public String cacheAreaCode;//	退货区货位	;// string	@mock=ZCX01-101
    public String cacheAreaName;//		;// string	@mock=ZCX01-101
    public String cacheAreaSendArea;//		;// string	@mock=
    public String cacheAreaSendDirection;//		;// string	@mock=
    public String cacheAreaSendDirectionBackup;//		;// string	@mock=
    public String cacheAreaType;// string	@mock=
    public String carrier;// string	@mock=

    public String createTime;// string	@mock=1575879574000
    public String createUser;// string	@mock=4908
    public String customerName;// string	@mock=
    public String deliveryRoute;// string	@mock=01
    public String deliveryType;// string	@mock=
    public String frameFlag;// string	@mock=0
    public String goodsAddressCode;// string	@mock=ZCX010000101
    public String goodsPositionCode;// string	@mock=ZCX01-101
    public String id;// string	@mock=3589
    public String islocked;// string	@mock=0
    public String lockedName;// string	@mock=否
    public String packageLimit;// string	@mock=0
    public String saleApplication;// string	@mock=
    public String sendType;// string	@mock=3
    public String sendTypeName;// string	@mock=自配
    public String takeGoodsType;// string	@mock=
    public String updateTime;// string	@mock=1583127690000
    public String updateUser;// string	@mock=4908
    public String warehouseCode;// string	@mock=WMS360000_1
    public String yn;// string	@mock=1

}
