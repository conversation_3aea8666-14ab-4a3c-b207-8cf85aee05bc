package com.xyy.wms.pda.contract.out.review

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.out.ReviewOutListBean
import com.xyy.wms.pda.bean.out.outsideReview.Building
import io.reactivex.Observable

/**
 * 出库按箱外复核
 */
interface ReviewOutBoxListContract {
  interface IReviewOutBoxListModel : IBaseModel {
    /**
     * 请求外复合任务列表
     */
    fun getReviewOutBoxList(map: Map<String, String?>): Observable<BaseResponseBean<ReviewOutListBean>>
    /**
     * 外复核 索取建筑物 列表
     */
    fun getAllBuildingCode(): Observable<BaseResponseBean<MutableList<Building>>>
  }

  interface IReviewOutBoxListView : IBaseActivity {
    /**
     * 获取外复合任务列表成功
     */
    fun getReviewOutListSuccess(bean: ReviewOutListBean?)
    /**
     * 显示网络错误
     */
    fun showNetworkError()
    /**
     * 外复核 索取建筑物 列表 成功
     */
    fun getAllBuildingCodeSuccess(list: MutableList<Building>?)
  }

}
