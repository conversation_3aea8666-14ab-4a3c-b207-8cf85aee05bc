package com.xyy.wms.pad.instorage.model.newinstorage

import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.api.ApiInAcceptServiceNew
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.ConfirmCodeScanOrderPost
import com.xyy.wms.pda.bean.instorage.checkaccept.FindPurchaseCodeScanOrderBean
import com.xyy.wms.pda.bean.instorage.checkaccept.FindPurchaseCodeScanOrderPost
import com.xyy.wms.pda.bean.instorage.checkaccept.UnlockCodeScanOrder
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckPermissionBystaffNumPost
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckPermissionBystaffNumBean
import com.xyy.wms.pda.contract.instorage.checkaccept.TracingCodeScanningContract
import com.xyy.wms.pda.net.RetrofitCreateHelper
import io.reactivex.Observable

class TracingCodeScanningModel : TracingCodeScanningContract.TracingCodeScanningContractModel {

    companion object {
        fun newInstance() : TracingCodeScanningModel {
            return TracingCodeScanningModel()
        }
    }

    override fun findPurchaseCodeScanOrder(findPurchaseCodeScanOrderPost: FindPurchaseCodeScanOrderPost): Observable<BaseResponseBean<FindPurchaseCodeScanOrderBean>> {
        return RetrofitCreateHelper.createApi(ApiInAcceptServiceNew::class.java).findPurchaseCodeScanOrder(findPurchaseCodeScanOrderPost)
            .compose(RxHelper.rxSchedulerHelper())
    }

    override fun confirmCodeScanOrder(confirmCodeScanOrderPost: ConfirmCodeScanOrderPost): Observable<BaseResponseBean<String>> {
        return RetrofitCreateHelper.createApi(ApiInAcceptServiceNew::class.java)
            .confirmCodeScanOrder(confirmCodeScanOrderPost)
            .compose(RxHelper.rxSchedulerHelper())
    }

    override fun checkPermissionBystaffNum(checkPermissionBystaffNumPost: CheckPermissionBystaffNumPost): Observable<BaseResponseBean<CheckPermissionBystaffNumBean>> {
        return RetrofitCreateHelper.createApi(ApiInAcceptServiceNew::class.java).checkPermissionBystaffNum(checkPermissionBystaffNumPost)
            .compose(RxHelper.rxSchedulerHelper())
    }

    override fun unlockCodeScanOrder(unlockCodeScanOrder: UnlockCodeScanOrder): Observable<BaseResponseBean<String>> {
        return RetrofitCreateHelper.createApi(ApiInAcceptServiceNew::class.java).unlockCodeScanOrder(unlockCodeScanOrder)
            .compose(RxHelper.rxSchedulerHelper())
    }

}
