package com.xyy.wms.pda.bean.out;

import java.io.Serializable;
import java.util.List;

/**
 * 出库——外复核 电子追溯码
 */
public class ReviewSupervisionCodeBean implements Serializable {

    /**
     * 总件数
     */
    private int totalNumber;
    /**
     * 需要扫描的监管码数量
     */
    private int pendingSupervisionCode;
    /**
     * 已扫描的监管码数量
     */
    private int scanSupervisionCode;
    /**
     * 出库单号
     */
    private String orderCode;
    /**
     * 监管码
     */
    private PageBean page;

    public int getTotalNumber() {
        return totalNumber;
    }

    public void setTotalNumber(int totalNumber) {
        this.totalNumber = totalNumber;
    }

    public int getPendingSupervisionCode() {
        return pendingSupervisionCode;
    }

    public void setPendingSupervisionCode(int pendingSupervisionCode) {
        this.pendingSupervisionCode = pendingSupervisionCode;
    }

    public int getScanSupervisionCode() {
        return scanSupervisionCode;
    }

    public void setScanSupervisionCode(int scanSupervisionCode) {
        this.scanSupervisionCode = scanSupervisionCode;
    }

    public String getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    public PageBean getPage() {
        return page;
    }

    public void setPage(PageBean page) {
        this.page = page;
    }

    public static class PageBean {

        private boolean isLastPage;

        private List<ReviewSupervisionCodeBean.PageBean.ListBean> list;

        public boolean isIsLastPage() {
            return isLastPage;
        }

        public void setIsLastPage(boolean isLastPage) {
            this.isLastPage = isLastPage;
        }

        public List<ReviewSupervisionCodeBean.PageBean.ListBean> getList() {
            return list;
        }

        public void setList(List<ReviewSupervisionCodeBean.PageBean.ListBean> list) {
            this.list = list;
        }

        public static class ListBean {
            /**
             * 监管码
             */
            private String code;
            /**
             * 商品编号
             */
            private String productCode;
            /**
             * 是否被选中
             */
            private boolean selected;

            public String getCode() {
                return code;
            }

            public void setCode(String code) {
                this.code = code;
            }

            public String getProductCode() {
                return productCode;
            }

            public void setProductCode(String productCode) {
                this.productCode = productCode;
            }

            public boolean isSelected() {
                return selected;
            }

            public void setSelected(boolean selected) {
                this.selected = selected;
            }
        }
    }
}
