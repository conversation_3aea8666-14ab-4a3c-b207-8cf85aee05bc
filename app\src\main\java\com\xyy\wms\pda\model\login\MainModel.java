package com.xyy.wms.pda.model.login;

import androidx.annotation.NonNull;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.home.HomeListBean;
import com.xyy.wms.pda.bean.update.VersionBeanInfo;
import com.xyy.wms.pda.bean.update.VersionInfo;
import com.xyy.wms.pda.contract.login.MainContract;
import com.xyy.wms.pda.model.ServiceModel;
import com.xyy.wms.pda.bean.home.HomeListBeanNew;

import java.util.List;

import io.reactivex.Observable;
import io.reactivex.schedulers.Schedulers;
import io.reactivex.subscribers.ResourceSubscriber;
import okhttp3.ResponseBody;

public class MainModel extends ServiceModel implements MainContract.IMainModel {

    @NonNull
    public static MainModel newInstance() {
        return new MainModel();
    }

    @Override
    public Observable<BaseResponseBean<HomeListBeanNew>> getModules() {
        return getApiService().getHomeList().compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<VersionInfo>> checkUpdate(VersionBeanInfo versionBeanInfo) {
        return getApiService().checkUpdate(versionBeanInfo).compose(RxHelper.rxSchedulerHelper());
    }

    public ResourceSubscriber<ResponseBody> downloadApk(String url, ResourceSubscriber<ResponseBody> subscriber) {
        return getApiService()
                .downloadApk(url)
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.io())
                .subscribeWith(subscriber);
    }
}
