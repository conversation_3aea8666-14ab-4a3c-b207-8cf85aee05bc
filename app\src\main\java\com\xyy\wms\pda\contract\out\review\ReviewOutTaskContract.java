package com.xyy.wms.pda.contract.out.review;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.out.ReviewOutScanBean;
import com.xyy.wms.pda.bean.out.ReviewScanElectronicBean;

import java.util.Map;

import io.reactivex.Observable;
/**
 * Created by XyyMvpSportTemplate on 02/18/2019 17:23
 */
public interface ReviewOutTaskContract {

    interface IReviewOutTaskModel extends IBaseModel {
        /**
         * 外复核扫描标签条码
         */
        Observable<BaseResponseBean<ReviewOutScanBean>> reviewOutScan(Map<String, String> map);

        /**
         * 外复核扫描电子追溯码
         */
        Observable<BaseResponseBean<ReviewScanElectronicBean>> reviewOutScanElectronic(Map<String, String> map);

        /**
         * 扫描电子追溯码失败时，强制复核
         */
        Observable<BaseResponseBean<ReviewScanElectronicBean>> forcedReview(Map<String, String> map);
    }

    interface IReviewOutTaskView extends IBaseActivity {
        /**
         * 外复核扫描标签条码成功
         */
        void reviewOutScanSuccess(ReviewOutScanBean bean);

        /**
         * 外复核扫描标签条码 失败
         */
        void reviewOutScanFailure(String msg);

        /**
         * 外复核扫描电子追溯码成功
         */
        void reviewOutScanElectronicSuccess(ReviewScanElectronicBean bean);

        /**
         * 外复核扫描电子追溯码失败
         */
        void reviewOutScanElectronicFailure(Map<String, String> map, String msg);

        /**
         * 强制复核成功
         */
        void onForcedReviewSuccess(ReviewScanElectronicBean bean);
    }

}
