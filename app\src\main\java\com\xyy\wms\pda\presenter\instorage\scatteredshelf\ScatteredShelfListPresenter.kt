package com.xyy.wms.pda.presenter.instorage.scatteredshelf

import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.shelf.InShelfResult
import com.xyy.wms.pda.bean.productCode.ProductCode
import com.xyy.wms.pda.contract.instorage.scatteredshelf.ScatteredShelfListContract
import com.xyy.wms.pda.helper.SimpleErrorConsumer
import com.xyy.wms.pda.helper.SimpleSuccessConsumer
import com.xyy.wms.pda.model.instorage.scatteredshelf.ScatteredShelfListModel
import com.xyy.wms.pda.presenter.productCode.GetProductCodePresenter

/**
 * 零散列表上架
 */
class ScatteredShelfListPresenter : ScatteredShelfListContract.ScatteredShelfListPresenter(), GetProductCodePresenter {

    override fun getModel(): ScatteredShelfListModel {
        return ScatteredShelfListModel.newInstance()
    }

    override fun getScatteredShelfList(containerCode: String, shelfType: Int) {
        mRxManager?.register(mIModel?.getScatteredShelfList(containerCode, shelfType)?.subscribe(object : SimpleSuccessConsumer<BaseResponseBean<InShelfResult>>(mIView) {
            override fun onSuccess(baseResponseBean: BaseResponseBean<InShelfResult>) {
                mIView?.getScatteredShelfListSuccess(baseResponseBean)
            }
        }, SimpleErrorConsumer(mIView)))
    }

    override fun getProductBarCode(ownerCode: String, packageBarCode: String) {
        mRxManager?.register(mIModel?.getProductBarCode(ownerCode, packageBarCode)?.subscribe(object : SimpleSuccessConsumer<BaseResponseBean<List<ProductCode>>>(mIView) {
            override fun onSuccess(baseResponseBean: BaseResponseBean<List<ProductCode>>) {
                mIView?.getProductCodeSuccess(baseResponseBean)
            }
        }, SimpleErrorConsumer(mIView)))
    }

    companion object {
        fun newInstance(): ScatteredShelfListPresenter {
            return ScatteredShelfListPresenter()
        }
    }
}
