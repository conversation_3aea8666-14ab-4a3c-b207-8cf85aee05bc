package com.xyy.wms.pda.model.out.exception;

import android.text.TextUtils;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.exception.ExceptionHandleListBean;
import com.xyy.wms.pda.bean.out.ExceptionBean;
import com.xyy.wms.pda.contract.out.exception.ExceptionHandleContract;
import com.xyy.wms.pda.model.ServiceModel;

import java.util.Map;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 04/10/2019 14:59
 */
public class ExceptionHandleModel extends ServiceModel implements ExceptionHandleContract.IExceptionHandleModel {

    public static ExceptionHandleModel newInstance() {
        return new ExceptionHandleModel();
    }

    @Override
    public Observable<BaseResponseBean<ExceptionHandleListBean>> queryExceptionTaskList(ExceptionBean exceptionBean) {
        return getApiOutManagerService().queryExceptionTaskList(exceptionBean).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<ExceptionHandleListBean>> queryExceptionTaskListByContainer(ExceptionBean exceptionBean) {
        return getApiOutManagerService().queryExceptionTaskListByContainerId(exceptionBean).compose(RxHelper.rxSchedulerHelper());
    }
}
