package com.xyy.wms.pda.model.out.purchaseexit;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitDoingBean;
import com.xyy.wms.pda.contract.out.purchaseexit.PurchaseExitOrderContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
/**
 *
 */
public class PurchaseExitOrderModel implements PurchaseExitOrderContract.IPurchaseExitOrderModel {
    public static PurchaseExitOrderModel newInstance() {
        return new PurchaseExitOrderModel();
    }

    @Override
    public Observable<BaseResponseBean<List<PurchaseExitDoingBean>>> queryPerformDocument(Map<String, Object> map) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).queryPerformDocument(map)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean> queryPackageBarValidity(Map<String, String> map) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).queryPackageBarValidity(map)
                .compose(RxHelper.rxSchedulerHelper());
    }

}
