package com.xyy.wms.pda.model.instorage.wholeshelf;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.common.DictParam;
import com.xyy.wms.pda.bean.common.DictParamResult;
import com.xyy.wms.pda.bean.instorage.BatchCodeResultBean;
import com.xyy.wms.pda.bean.instorage.shelf.CommitShelfResult;
import com.xyy.wms.pda.bean.instorage.shelf.ShelfDetailPost;
import com.xyy.wms.pda.contract.instorage.wholeshelf.WholeShelfProductContract;
import com.xyy.wms.pda.model.checkSelectGoodsPosition.in.CheckSelectGoodsPositionModel;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.List;

import io.reactivex.Observable;
/**
 * 整件商品上架
 */
public class WholeShelfProductModel extends BaseModel implements WholeShelfProductContract.IWholeShelfProductModel, CheckSelectGoodsPositionModel {
    public static WholeShelfProductModel newInstance() {

        return new WholeShelfProductModel();
    }
    /**
     * 单个明细提交
     */
    public Observable<BaseResponseBean<CommitShelfResult>> commitStorageOrderDetail(ShelfDetailPost shelfDetailPost) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).commitStorageOrderDetail(shelfDetailPost)
                .compose(RxHelper.rxSchedulerHelper());
    }
    public Observable<BaseResponseBean<List<BatchCodeResultBean>>>getBatchCodes(String productCode) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).getBatchCodes(productCode)
            .compose(RxHelper.rxSchedulerHelper());
    }

    /**
     * 批号开关
     */
    public Observable<BaseResponseBean<DictParamResult>>getDictParamList(DictParam dictParam) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).getDictParamList(dictParam)
            .compose(RxHelper.rxSchedulerHelper());
    }
}
