package com.xyy.wms.pda.model.inmanager.search

import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.inmanager.search.SearchProductBean
import com.xyy.wms.pda.contract.inmanager.search.SearchProductListContract.ISearchProductListModel
import com.xyy.wms.pda.model.ServiceModel
import io.reactivex.Observable

/**
 * Created by XyyMvpPdaTemplate on 04/19/2019 11:21
 */
class SearchProductListModel : ServiceModel(), ISearchProductListModel {

  override fun queryProductListByBarCode(barCode: String?): Observable<BaseResponseBean<List<SearchProductBean>>> {
    return apiInManagerService.queryProductListByBarCode(barCode).compose(RxHelper.rxSchedulerHelper())
  }

  companion object {
    @JvmStatic
    fun newInstance(): SearchProductListModel {
      return SearchProductListModel()
    }
  }
}
