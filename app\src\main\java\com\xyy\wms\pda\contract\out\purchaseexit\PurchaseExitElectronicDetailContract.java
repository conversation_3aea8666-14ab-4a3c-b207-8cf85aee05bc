package com.xyy.wms.pda.contract.out.purchaseexit;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.DrugRegulatoryCodeBean;

import java.util.HashMap;

import io.reactivex.Observable;

/**
 * Created by zcj on 2018/11/17 11
 */
public interface PurchaseExitElectronicDetailContract {

    interface IPurchaseExitElectronicDetailModel extends IBaseModel {
        /**
         * 解锁商品
         *
         * @param productCode      商品编号
         * @param productBatchCode 商品批号
         * @param refundOrderCode  购进退出单据号
         */
        Observable<BaseResponseBean> saveTheUnlock(String productBatchCode, String productCode, String refundOrderCode);

        /**
         * 单条监管码查询
         */
        Observable<BaseResponseBean<DrugRegulatoryCodeBean>> findDrugRegulatoryCode(HashMap<String, String> map);

        /**
         * 查询已扫描数量
         *
         * @param lineNumber 行号
         * @param orderCode  单据编号
         */
        Observable<BaseResponseBean<DrugRegulatoryCodeBean>> findScannedQuantity(String lineNumber, String orderCode);
    }

    interface IPurchaseExitElectronicDetailView extends IBaseActivity {

        void saveTheUnlockSuccess(BaseResponseBean bean);

        void findDrugRegulatoryCodeSuccess(BaseResponseBean<DrugRegulatoryCodeBean> baseBean);

        void findScannedQuantitySuccess(BaseResponseBean<DrugRegulatoryCodeBean> baseBean);

        void showNetWorkError();
    }

    abstract class PurchaseExitElectronicDetailPresenter extends BasePresenter<IPurchaseExitElectronicDetailModel, IPurchaseExitElectronicDetailView> {
        public abstract void saveTheUnlock(String productBatchCode, String productCode, String refundOrderCode);

        /**
         * 单条监管码查询
         *       变量名	           含义
         * * actualRefundCount	需扫描数量
         * * lineNumber	        单据明细行号
         * * productCode	    商品编号
         * * refundOrderCode	购进退出单据编号
         * * regulatoryCode	    电子追溯码
         */
        public abstract void findDrugRegulatoryCode(HashMap<String, String> map);

        /**
         * 查询已扫描数量
         */
        public abstract void findScannedQuantity(String lineNumber, String orderCode);
    }
}
