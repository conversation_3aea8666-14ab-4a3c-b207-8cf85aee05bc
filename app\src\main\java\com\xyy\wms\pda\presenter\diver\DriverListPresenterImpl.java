package com.xyy.wms.pda.presenter.diver;

import androidx.annotation.NonNull;

import com.xyy.common.util.ToastUtils;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.driver.DriverBean;
import com.xyy.wms.pda.bean.driver.DriverEditBean;
import com.xyy.wms.pda.bean.moveStorage.StartTaskResult;
import com.xyy.wms.pda.helper.SimpleErrorConsumer;
import com.xyy.wms.pda.helper.SimpleSuccessConsumer;
import com.xyy.wms.pda.model.driver.DriverModelImpl;
import com.xyy.wms.pda.ui.activity.driver.DriverMainActivity;

import java.util.List;

public class DriverListPresenterImpl extends BasePresenter<DriverModelImpl, DriverMainActivity> implements DiverListPresenter{

    public static DriverListPresenterImpl newInstance(){
        return new DriverListPresenterImpl();
    }
    @Override
    public void getDiverList(String carNo) {
        if (mRxManager == null || mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.getDriverList(carNo)
            .subscribe(new SimpleSuccessConsumer<BaseResponseBean<List<DriverBean>>>(mIView) {
                @Override
                public void onSuccess(BaseResponseBean<List<DriverBean>> requestBaseBean) {
                    mIView.getDriverList(requestBaseBean.getResult());
                }

                @Override
                public void onFailure(int code, String msg) {
                    super.onFailure(code, msg);
                    ToastUtils.showShortSafe(msg);
                }
            }, new SimpleErrorConsumer(mIView)));

    }

    @Override
    protected DriverModelImpl getModel() {
        return DriverModelImpl.newInstance();
    }


}
