package com.xyy.wms.pda.api;

import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.inmanager.AddPositionAdjustmentBean;
import com.xyy.wms.pda.bean.inmanager.ExceptionCountBean;
import com.xyy.wms.pda.bean.inmanager.ReviewExceptionListBean;
import com.xyy.wms.pda.bean.inmanager.check.AddCheckBean;
import com.xyy.wms.pda.bean.inmanager.check.CheckStorageAreaBean;
import com.xyy.wms.pda.bean.inmanager.check.CheckTaskListBean;
import com.xyy.wms.pda.bean.inmanager.check.CheckTaskNumBean;
import com.xyy.wms.pda.bean.inmanager.check.CheckUpBean;
import com.xyy.wms.pda.bean.inmanager.post.ReviewExceptionPostBean;
import com.xyy.wms.pda.bean.inmanager.search.SearchGoodPositionBean;
import com.xyy.wms.pda.bean.inmanager.search.SearchProductBean;

import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
import retrofit2.http.Body;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;

/**
 * 库内管理
 */
public interface ApiInManagerService {

    /**
     * 盘查
     */
    @POST("warehouse/warehouseCheck/checkUp")
    Observable<BaseResponseBean<List<CheckUpBean>>> checkUp(@Body Map<String,String> positionCode);

    /**
     * 盘点
     */
    @POST("warehouseCheck/addWarehouseCheck")
    Observable<BaseResponseBean> addWarehouseCheck(@Body AddCheckBean bean);

    /**
     * 添加盘点-获取盘点库别列表
     */
    @GET("weService/findStorageType")
    Observable<BaseResponseBean<List<CheckStorageAreaBean>>> findStorageType();

    /**
     * 添加盘点-获取盘点库区列表
     */
    @GET("weService/findStorageArea")
    Observable<BaseResponseBean<List<CheckStorageAreaBean>>> findStorageArea(@Query("storageTypeId") String storageTypeId);

    /**
     * 添加盘点-获取盘点订单号
     */
    @GET("warehouseCheck/getCheckNo")
    Observable<BaseResponseBean<String>> getCheckNo();

    /**
     * 盘点任务 获取任务数量
     */
    @GET("warehouseCheck/selectCheckTaskNums")
    Observable<BaseResponseBean<CheckTaskNumBean>> selectCheckTaskNums();

    /**
     * 盘点任务 领取盘点任务
     *
     * @param checkPlanType 盘点类型 1初盘2复盘3终盘
     */
    @GET("warehouseCheck/getCheckTask")
    Observable<BaseResponseBean<CheckTaskListBean>> getCheckTask(@Query("checkPlanType") String checkPlanType);

    /**
     * 盘点全部提交
     *
     * @param checkPlanType 盘点计划类型1初盘2复盘3终盘（必传）
     * @param checkTaskNo   盘点任务单号（必传）
     */
    @FormUrlEncoded
    @POST("warehouseCheck/taskComplete")
    Observable<BaseResponseBean> taskComplete(@Field("checkPlanType") String checkPlanType, @Field("checkTaskNo") String checkTaskNo);

    /**
     * 盘点提交
     *
     * @param checkPlanType   number	1初盘2复盘3终盘（必传）
     * @param firstCheckNum   number	初盘数量（当checkPlanType=1时的盘点数量）
     * @param id              number	盘点商品的id（必传）
     * @param secondCheckNum  number	复盘数量（当checkPlanType=2时的盘点数量）
     * @param thirdCheckNum   number	终盘数量（当checkPlanType=3时的盘点数量）
     * @param overflowingDesc number    损益原因
     * @param overflowingNum  number    差异数量
     */
    @FormUrlEncoded
    @POST("warehouseCheck/checkSingleGoods")
    Observable<BaseResponseBean> checkSingleGoods(@Field("checkPlanType") String checkPlanType,
                                                  @Field("firstCheckNum") String firstCheckNum,
                                                  @Field("id") String id,
                                                  @Field("secondCheckNum") String secondCheckNum,
                                                  @Field("thirdCheckNum") String thirdCheckNum,
                                                  @Field("overflowingDesc") String overflowingDesc,
                                                  @Field("overflowingNum") String overflowingNum);




    /**
     * 货位调整-获取货位调整单号
     */
    @POST("warehouse/locationMovement/getLocationMovementNo")
    Observable<BaseResponseBean<String>> getLocationMovementNo();

    /**
     * 货位调整-扫描货位编码获取商品信息
     */
    @POST("warehouse/locationMovement/getGoodsInfoByLocationCode")
    Observable<BaseResponseBean<List<AddPositionAdjustmentBean>>> getGoodsInfoByLocationCode(@Body Map<String, String> map);


    /**
     * 内复核异常-拣货 内复核 异常列表
     */
    @POST("stockout/exceptionPage")
    Observable<BaseResponseBean<ReviewExceptionListBean>> exceptionPage(@Body ReviewExceptionPostBean bean);

    /**
     * 内复核异常-更新异常表
     *
     * @param dealUserName 处理人	string
     * @param id           异常表主键	string
     * @param isAskFor     是否索取 （0 未索取 1 已索取）	number
     * @param status       处理状态 1 异常 2 异常处理完成	number
     */

    @GET("stockout/updateException")
    Observable<BaseResponseBean<String>> updateException(@Query("dealMode") String dealMode,
                                                         @Query("dealUserName") String dealUserName,
                                                         @Query("id") String id,
                                                         @Query("isAskFor") String isAskFor,
                                                         @Query("status") String status,
                                                         @Query("remark") String remark);

    /**
     * 拣货 内复核 异常统计
     */
    @GET("stockout/exceptionCount")
    Observable<BaseResponseBean<List<ExceptionCountBean>>> exceptionCount(@Query("exceptionType") String exceptionType);

    /**
     * 查询货位库存信息
     */
    @GET("warehouse/warehouseCheck/findGoodsPositionCode")
    Observable<BaseResponseBean<List<SearchGoodPositionBean>>> findGoodsPositionCode(@Query("batchNumber") String batchNumber,
                                                                                     @Query("productCode") String productCode);

    /**
     * 查询 商品列表通过 商品条码
     */
    @GET("warehouse/warehouseCheck/checkProductBarCode")
    Observable<BaseResponseBean<List<SearchProductBean>>> queryProductListByBarCode(@Query("productBarCode") String productBarCode);
}
