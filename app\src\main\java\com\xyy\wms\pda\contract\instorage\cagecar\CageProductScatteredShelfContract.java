package com.xyy.wms.pda.contract.instorage.cagecar;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.cageCar.CageCarOrderBean;
import com.xyy.wms.pda.bean.instorage.cageCar.CageCarShelfDetailPost;
import com.xyy.wms.pda.bean.instorage.cageCar.CommitCageCarShelfResult;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 02/25/2020 12:02
 */
public interface CageProductScatteredShelfContract {

    interface ICageProductScatteredShelfModel extends IBaseModel {
        Observable<BaseResponseBean<CageCarOrderBean>> getRollContainerShelfProductList(String pdaProductLineId, String storageOrderCode);

        Observable<BaseResponseBean<CommitCageCarShelfResult>> commitCageCarOrderDetail(CageCarShelfDetailPost cageCarShelfDetailPost);
    }

    interface ICageProductScatteredShelfView extends IBaseActivity {
        void getRollContainerShelfProductListSuccess(BaseResponseBean<CageCarOrderBean> baseResponseBean);
        void commitCageCarOrderDetailSuccess(BaseResponseBean<CommitCageCarShelfResult> baseResponseBean);
    }

}
