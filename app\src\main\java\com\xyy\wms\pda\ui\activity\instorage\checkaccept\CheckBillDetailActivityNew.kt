package com.xyy.wms.pda.ui.activity.instorage.checkaccept

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import androidx.lifecycle.Observer
import com.lwjfork.bus.LiveDataBus
import com.xyy.common.util.ToastUtils
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity
import com.xyy.utilslibrary.rxbus.RxBus
import com.xyy.wms.pda.R
import com.xyy.wms.pda.constant.BusCode
import com.xyy.wms.pda.contract.instorage.checkaccept.CheckAcceptBillDetailContractNew
import com.xyy.wms.pda.presenter.instorage.checkaccept.CheckAcceptBillDetailPresenterNew
import com.xyy.wms.pda.ui.adapter.instorage.checkaccept.CheckAcceptBillAdapterNew
import com.xyy.wms.pda.utils.CommonInfoUtils
import com.xyy.wms.pda.bean.instorage.BusinessInfo
import com.xyy.wms.pda.bean.instorage.PictureUploadResponse
import com.xyy.wms.pda.bean.instorage.PictureDeleteRequest
import com.xyy.wms.pda.ui.widget.ExceptionPictureDialog
import android.content.pm.PackageManager
import kotlinx.android.synthetic.main.activity_check_bill_detail_new.*
import com.xyy.wms.pda.bean.instorage.checkaccept.WaitCheckInfoBySupplierBean
import com.xyy.wms.pda.utils.PickerDialogManager
import com.xyy.wms.pda.bean.instorage.*
import com.xyy.wms.pda.listener.OptionsSelectCancelListener
import com.xyy.wms.pda.bean.common.DictListByNameAndType
// 新增：iData 扫描相关
import android.os.IScanListener
import android.util.Log
import com.example.iscandemo.iScanInterface
import kotlinx.android.synthetic.main.operate_layout.tv_examiner
import kotlinx.android.synthetic.main.operate_layout.tv_examiner_key
import kotlinx.android.synthetic.main.operate_layout.tv_reception_time
import kotlinx.android.synthetic.main.operate_layout.tv_reception_time_key
import android.text.Editable
import android.text.TextWatcher
import kotlinx.android.synthetic.main.activity_check_accept_bill_new.toolbar_check_accept
import kotlinx.android.synthetic.main.activity_check_accept_bill_new.tv_product_class
import com.xyy.wms.pda.utils.DialogUtils
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderDoSubmitPost
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderTurnDownBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderTurnDownPost
import com.xyy.wms.pda.bean.instorage.checkaccept.InitCheckPagePost
import com.xyy.wms.pda.bean.instorage.checkaccept.WaitCheckDetailListBean
import com.xyy.wms.pda.bean.instorage.SecondLoginPost
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderSavePost
import com.xyy.wms.pda.bean.instorage.checkaccept.SelectContainerBean
import com.xyy.wms.pda.bean.instorage.checkaccept.SelectContainerPost
import com.xyy.wms.pda.ext.reqFocusHideKeyboard
import com.xyy.wms.pda.listener.ExitListener
import com.xyy.wms.pda.listener.LoginConfirmListener
import com.xyy.wms.pda.listener.RejectionBillListener
import com.xyy.wms.pda.widget.dialog.RejectionBillDialog
import com.xyy.wms.pda.widget.dialog.SpecialDrugHint
import me.yokeyword.fragmentation.ISupportFragment


/**
 * 入库验收单-详情
 */
class CheckBillDetailActivityNew : BaseMVPCompatActivity<CheckAcceptBillDetailPresenterNew>(), CheckAcceptBillDetailContractNew.ICheckAcceptBillViewNew {
    private lateinit var checkAcceptBillAdapter: CheckAcceptBillAdapterNew
    private var checkAcceptBillList: MutableList<WaitCheckInfoBySupplierBean>? = ArrayList()
    private var currentWaitCheckInfo: WaitCheckInfoBySupplierBean? = null
    private val yesNoResultList: MutableList<PickerDialogManager.SelectItemModel<String>> = ArrayList()
    private var acceptResult: PickerDialogManager.SelectItemModel<AcceptResult>? = null// 验收结论     1, "合格"  2, "待处理" 3, "不合格" 4, "拒收"
    private val acceptResultList: MutableList<PickerDialogManager.SelectItemModel<AcceptResult>> = ArrayList()
    private var unqualifiedMatters: MutableList<PickerDialogManager.SelectItemModel<DictListByNameAndType>>? =null
    private var purchaseProductBatchCodeVoList: MutableList<PickerDialogManager.SelectItemModel<PurchaseProductBatchCodeVoBean>> = ArrayList()
    private var waitCheckDetailListBean: WaitCheckDetailListBean? = null
    private var packageBarCodeBean: PackageBarCodeBean? = null
    private var purchaseProductBatchCodeVoBean: PurchaseProductBatchCodeVoBean? = null // 当前选择批号
    private var isDialog = 1 //逻辑区域维护
    private var secondCheckUser = "" // 验收人2的oaID（当需要二次校验时必传）
    private var currentRejects = ""  // 是否当场拒收（当验收单明细包含拒收是必填）
    private var rejectRemark = ""    // 拒收备注
    private var imgUrlsList = listOf<String>() // 异常图片集合
    private var selectContainerBean: SelectContainerBean? = null
    private var exception_picture_index = 0  //异常图片数量
    private var currentExceptionPictureDialog: ExceptionPictureDialog? = null  // 当前异常图片弹窗
    // 新增：iData 扫描接口
    private var miScanInterface: iScanInterface? = null
    private var containerSearchRunnable: Runnable? = null
    override fun initPresenter(): BasePresenter<*, *> {
      return CheckAcceptBillDetailPresenterNew.newInstance()
    }
    override fun getLayoutId(): Int {
      return R.layout.activity_check_bill_detail_new
    }
    override fun initTransferData() {
        super.initTransferData()
        RxBus.get().register(this)
        currentWaitCheckInfo = intent.getSerializableExtra("extraWaitCheckInfo") as? WaitCheckInfoBySupplierBean
        createAcceptList()
    }
    //构建“验收结论”的候选列表
    private fun createAcceptList() {
      acceptResultList.add(
        PickerDialogManager.SelectItemModel(
          "合格", AcceptResult(
            AcceptOrderSpiltItem.QUALIFIED_ACCEPT_RESULT,
            "合格",
            "收货上架",
            AcceptOrderSpiltItem.IN_QUALIFIED,
            mutableMapOf(AcceptOrderSpiltItem.IN_QUALIFIED to "收货上架")
          )
        )
      )
      acceptResultList.add(
        PickerDialogManager.SelectItemModel(
          "待处理", AcceptResult(
            AcceptOrderSpiltItem.WAIT_ACCEPT_RESULT,
            "待处理",
            "复查",
            AcceptOrderSpiltItem.REVIEW,
            mutableMapOf(AcceptOrderSpiltItem.REVIEW to "复查")
          )
        )
      )
      acceptResultList.add(
        PickerDialogManager.SelectItemModel(
          "不合格", AcceptResult(
            AcceptOrderSpiltItem.UNQUALIFIED_ACCEPT_RESULT,
            "不合格",
            "入不合格库",
            AcceptOrderSpiltItem.IN_NOT_QUALIFIED,
            mutableMapOf(
              AcceptOrderSpiltItem.IN_NOT_QUALIFIED to "入不合格库",
              AcceptOrderSpiltItem.IN_RETURN to "入退货库"
            )
          )
        )
      )
      acceptResultList.add(
        PickerDialogManager.SelectItemModel(
          "拒收", AcceptResult(
            AcceptOrderSpiltItem.REFUSE_ACCEPT_RESULT,
            "拒收",
            "拒收",
            AcceptOrderSpiltItem.REJECTED,
            mutableMapOf(AcceptOrderSpiltItem.REJECTED to "拒收")
          )
        )
      )
//      yesNoResultList.add(PickerDialogManager.SelectItemModel("是", "是"))
//      yesNoResultList.add(PickerDialogManager.SelectItemModel("否", "否"))
    }



    override fun initView(savedInstanceState: Bundle?) {
        setupOperatorLayout()
        addListener()
        // 刷新页面
        LiveDataBus.get().with(BusCode.REFRESH_LIST).observe(this, Observer {
        })
        tv_supplier_name.text = currentWaitCheckInfo?.supplierName
        getData()
    }
    private fun setupOperatorLayout() {
      initTitleBar(toolbar_check_accept, "")
      CommonInfoUtils.setOperationName(tv_examiner_key, "操作员：", tv_examiner)
      CommonInfoUtils.setOperationTime(tv_reception_time_key, "操作时间：", tv_reception_time)
    }
    // 新增：初始化 iData 扫描
    private fun initScan() {
        try {
            Class.forName("com.idata.IDataManager")
            if (miScanInterface == null) {
                miScanInterface = iScanInterface(this)
                miScanInterface!!.registerScan(miScanListener)
            }
        } catch (e: ClassNotFoundException) {
            Log.i("iData", "IDataManager 类未找到，设备可能不支持 iData")
        } catch (e: NoClassDefFoundError) {
            Log.i("iData", "iData设备不可用: ${e.message}")
        } catch (e: Exception) {
            Log.i("iData", "iScanInterface 初始化失败: ${e.message}")
        }
    }

    // 新增：iData 回调监听
    private val miScanListener = IScanListener { data, type, decodeTime, keyDownTime, imagePath ->
        val result = data ?: ""
        if (result.isEmpty()) {
            showToast("扫码失败")
            return@IScanListener
        }
        runOnUiThread {
          // 仅当验收码输入框有焦点时处理扫码
          if (tv_container_code.hasFocus()) {
            tv_container_code.setText(result)
          }
        }
    }
    // 保留原有 Honeywell 扫描处理
    override fun scanData(data: String) {
        super.scanData(data)
        if (TextUtils.isEmpty(data)) {
            ToastUtils.showShortSafe("扫描结果为空")
            return
        }
        // 仅当验收码输入框有焦点时处理扫码
        if (tv_container_code.hasFocus()) {
          tv_container_code.setText(data)
        }
    }

    override fun onResume() {
        super.onResume()
        // 新增：进入页面或返回时尝试初始化 iData 扫描
        initScan()
    }

    override fun onDestroy() {
        super.onDestroy()
        RxBus.get().unRegister(this)
    }
  //查询是否有追溯码
  private fun getData() {
    mPresenter.initCheckPage(InitCheckPagePost(currentWaitCheckInfo!!.supplierCode, currentWaitCheckInfo!!.ownerCode,currentWaitCheckInfo!!.channelCode))
  }
  private fun addListener(){
    //选择商品
    tv_product_class_btn.setOnClickListener {
      val intent = Intent(mContext, CheckBillProductListActivityNew::class.java)
      intent.putExtra("extraWaitCheckInfo", currentWaitCheckInfo)
      startActivityForResult(intent, 1)
    }
    tv_product_class.reqFocusHideKeyboard()

    // 异常图片按钮点击事件
    btn_exception_handling_picture.setOnClickListener {
      if(packageBarCodeBean == null){
        showToast("无商品信息")
        return@setOnClickListener
      }
      showExceptionPictureDialog()
    }
    // 验收单号点击
    tv_checkOrderCode.setOnClickListener {
      val intent = Intent()
      intent.putExtra("checkOrderCode", tv_checkOrderCode.text.toString())
      intent.putExtra("channelCode", currentWaitCheckInfo?.channelCode)
      intent.setClass(mContext, DetailsOfAcceptedGoodsActivity::class.java)
      startActivityForResult(intent, 2)
    }
    // 验收结论选择
    tv_product_inspection_conclusion_content.setOnClickListener {
      acceptResultHint()
    }
    // 不合格事项选择
    tv_product_disqualification_content.setOnClickListener {
      showUnqualifiedMatters()
    }
    // 批号选择
    tv_product_batch_number_content.setOnClickListener {
      showProductBatchCodeList()
    }
    // 验收件数
    tv_product_num_inspection_piece_content.addTextChangedListener(object : TextWatcher {
      override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
      }

      override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
      }
      override fun afterTextChanged(s: Editable?) {
        if (s.toString().isNotEmpty()) {

          // 验收件数不能大于待验件数
          if (purchaseProductBatchCodeVoBean?.productCountBig?.isNotEmpty() == true) {
            var value = purchaseProductBatchCodeVoBean?.productCountBig?.toInt()
            if (s.toString().toInt() > value!!) {
              showToast("验收件数不能大于待验件数")
              tv_product_num_inspection_piece_content.setText("")
              return
            }
          }

          if (tv_product_inspection_scatter_num_content.text.toString().isNotEmpty()) {
            val price =
              if (purchaseProductBatchCodeVoBean?.productPackingBigNumber != null) {
                (s.toString()
                  .toInt() * purchaseProductBatchCodeVoBean?.productPackingBigNumber!!.toInt()) + tv_product_inspection_scatter_num_content.text.toString()
                  .toInt()
              } else {
                (tv_product_inspection_scatter_num_content.text.toString()
                  .toInt()) + s.toString().toInt()
              }
            tv_product_num_inspection_content.text = price.toString()
          } else {
            val price =
              if (purchaseProductBatchCodeVoBean?.productPackingBigNumber != null) {
                (s.toString()
                  .toInt() * purchaseProductBatchCodeVoBean?.productPackingBigNumber!!.toInt())
              } else {
                s.toString().toInt()
              }
            tv_product_num_inspection_content.text = price.toString()
          }
        } else if (tv_product_inspection_scatter_num_content.text.toString().isNotEmpty()) {
          val price = tv_product_inspection_scatter_num_content.text.toString().toInt()
          tv_product_num_inspection_content.text = price.toString()
        } else {
          tv_product_num_inspection_content.text = ""
        }
      }
    })
    // 验收零散数
    tv_product_inspection_scatter_num_content.addTextChangedListener(object : TextWatcher {
      override fun afterTextChanged(s: Editable?) {
        if (s.toString().isNotEmpty()) {

          // 验收零散数不能大于待验数量
          if (purchaseProductBatchCodeVoBean?.productCountSmall?.isNotEmpty() == true) {
            var value = purchaseProductBatchCodeVoBean?.productCountSmall?.toInt()
            if (s.toString().toInt() > value!!) {
              showToast("验收零散数不能大于待验数量")
              tv_product_inspection_scatter_num_content.setText("")
              return
            }
          }

          if (tv_product_num_inspection_piece_content.text.toString().isNotEmpty()) {
            val price =
              if (purchaseProductBatchCodeVoBean?.productPackingBigNumber != null) {
                (tv_product_num_inspection_piece_content.text.toString()
                  .toInt() * purchaseProductBatchCodeVoBean?.productPackingBigNumber!!.toInt()) + s.toString()
                  .toInt()
              } else {
                (tv_product_num_inspection_piece_content.text.toString()
                  .toInt()) + s.toString().toInt()
              }
            tv_product_num_inspection_content.text = price.toString()
          } else {
            val price = s.toString().toInt()
            tv_product_num_inspection_content.text = price.toString()
          }
        } else if (tv_product_num_inspection_piece_content.text.toString().isNotEmpty()) {
          val price = tv_product_num_inspection_piece_content.text.toString().toInt()
          tv_product_num_inspection_content.text = price.toString()
        } else {
          tv_product_num_inspection_content.text = ""
        }
      }

      override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
      }

      override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
      }
    })
    //容器号输入
    iv_check_barcode.setOnClickListener {
      val containerCode = tv_container_code.text.toString().trim()
      if (containerCode.isEmpty()) {
        showToast("容器编号不能为空")
        return@setOnClickListener
      }

      if (purchaseProductBatchCodeVoBean != null) {
        mPresenter.selectContainer(
          SelectContainerPost(
            currentWaitCheckInfo!!.supplierCode,
            containerCode,
            tv_checkOrderCode.text.toString()
          )
        )
      } else {
        showToast("商品无批号")
      }
    }

    // 驳回操作
    btn_rebut.setOnClickListener {
      rejectSureHint()
    }

    // 验收提交
    btn_checked.setOnClickListener {
      checkCommitData()
    }

    //保存操作
    btn_save.setOnClickListener {
      verifyCommitData()
    }
  }
  //接收通过 “startActivityForResult” 启动的页面在关闭时回传的结果数据
  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (data == null) {
      return
    }
    if (resultCode != ISupportFragment.RESULT_OK) {
      return
    }

    when (requestCode) {
      1 -> {   //选择待检明细/商品明细
        if (data.getSerializableExtra("data") != null) {
            var waitCheckDetailListBean = data.getSerializableExtra("data") as WaitCheckDetailListBean
            tv_product_class.setText(waitCheckDetailListBean.productCode)
            this.waitCheckDetailListBean = waitCheckDetailListBean
            mPresenter.selectProductCode(
              ProductCodePost(
                currentWaitCheckInfo?.supplierCode!!,
                tv_checkOrderCode.text.toString(),
                waitCheckDetailListBean.productCode,
                waitCheckDetailListBean.productBatchCode,
                waitCheckDetailListBean.productManufactureDate,
                waitCheckDetailListBean.productValidDate,
                waitCheckDetailListBean.productPackingBigNumber,
                waitCheckDetailListBean.ownerCode,
                waitCheckDetailListBean.buildingCode,
                waitCheckDetailListBean.channelCode,
                waitCheckDetailListBean.sterilizingBatchNumber,
                waitCheckDetailListBean.productPackingMiddleNumber
              )
            )
        }
      }

      // 商品维护回调
//      3 -> {
//        mPresenter.selectProductCode(
//          ProductCodePost(
//            waitCheckInfoBySupplierBean?.supplierCode!!,
//            tv_checkOrderCode.text.toString(),
//            waitCheckDetailListBean?.productCode!!,
//            waitCheckDetailListBean?.productBatchCode!!,
//            waitCheckDetailListBean?.productManufactureDate!!,
//            waitCheckDetailListBean?.productValidDate!!,
//            waitCheckDetailListBean?.productPackingBigNumber!!,
//            waitCheckDetailListBean?.ownerCode!!,
//            waitCheckDetailListBean?.buildingCode!!,
//            waitCheckDetailListBean?.channelCode!!,
//            waitCheckDetailListBean?.sterilizingBatchNumber!!,
//            waitCheckDetailListBean?.productPackingMiddleNumber!!
//          )
//        )
//      }
      //添加异常图片
//      addRequestCodeChoose -> {
//        compress(data) {
//          onAddImageResult(it[0], currentType)
//        }
//      }
//      //替换异常图片
//      replaceRequestCodeChoose -> {
//        compress(data) {
//          onReplaceImage(it[0], currentType)
//        }
//      }
    }
  }
  override fun selectProductCodeSuccess(result: BaseResponseBean<PackageBarCodeBean>) {
    if (result.code == 2001) {
//      showGoosPicture(result.result.productCode)
      fillingPageData(result.result)
      showCommodityMaintenanceDialog()
    } else if (result.code == 0) {
//      showGoosPicture(result.result.productCode)
      fillingPageData(result.result)
    } else {
      showToast(result.msg)
    }
  }
  fun fillingPageData(packageBarCodeBean: PackageBarCodeBean) {
    this.packageBarCodeBean = packageBarCodeBean
    if (packageBarCodeBean.purchaseProductBatchCodeVoList != null) {
      this.purchaseProductBatchCodeVoList.clear()
      packageBarCodeBean.purchaseProductBatchCodeVoList.forEach {
        run {
          val item: PickerDialogManager.SelectItemModel<PurchaseProductBatchCodeVoBean> =
            PickerDialogManager.SelectItemModel<PurchaseProductBatchCodeVoBean>(
              it.productBatchCode,
              it
            )
          this.purchaseProductBatchCodeVoList?.add(item)
        }
      }
      this.purchaseProductBatchCodeVoBean =
        packageBarCodeBean.purchaseProductBatchCodeVoList[0]
    }
    tv_product_detail_product_name_content.text = packageBarCodeBean.productName // 商品名称
    tv_product_detail_recipel_type_content.text =
      packageBarCodeBean.prescriptionClassification // 处方分类
    tv_product_detail_recipel_type_content_suffix.text =
      if(packageBarCodeBean.ephedrineFlag) "(含麻)" else ""
    tv_product_medium_packing_content.text = packageBarCodeBean.middlePackingNumber + packageBarCodeBean.packingUnit // 中包装
    tv_product_standard_content.text = packageBarCodeBean.specifications // 规格
    tv_product_detail_product_code_content.text = packageBarCodeBean.productCode // 商品编码
    tv_product_piece_packing_content.text =
      packageBarCodeBean.largePackingNumber + packageBarCodeBean.packingUnit // 件包装
    tv_approval_number_content.text = packageBarCodeBean.approvalNumbers // 批准文号
    if (purchaseProductBatchCodeVoBean != null) {
      tv_product_batch_number_content.text =
        this.purchaseProductBatchCodeVoBean!!.productBatchCode // 批号
      tv_product_production_date_content.text =
        this.purchaseProductBatchCodeVoBean!!.productManufactureDate // 生产日期
      tv_product_indate_content.text =
        this.purchaseProductBatchCodeVoBean!!.productValidDate // 有效期
      tv_prepare_product_num_inspection_piece_content.text =
        this.purchaseProductBatchCodeVoBean!!.productCountBig // 待验件数
      tv_prepare_product_inspection_scatter_num_content.text =
        this.purchaseProductBatchCodeVoBean!!.productCountScatter // 待验零散数
      tv_prepare_product_num_inspection_content.text =
        this.purchaseProductBatchCodeVoBean!!.productCountSmall // 待验数量
    }

    // 商品条码录入成功，焦点定位到容器号
    tv_container_code.requestFocus()

    //初始化异常图片
//    initExceptionPictureDialog()
    showAcceptResult(acceptResultList[0]) // 默认显示合格
  }
    /**
     * 选择“验收结论
     */
    private fun acceptResultHint() {
      PickerDialogManager.showSingleSelectPicker(
        mContext,
        acceptResultList,
        PickerDialogManager.OnOptionsSelectCallback2<PickerDialogManager.SelectItemModel<AcceptResult>> { _, t ->
          showAcceptResult(t)
        },
        null
      )
    }
    //根据选中的“验收结论”更新页面显示
    private fun showAcceptResult(selectItem: PickerDialogManager.SelectItemModel<AcceptResult>?) {
      var result: PickerDialogManager.SelectItemModel<AcceptResult>? = null
      if (selectItem != null) {
        result = PickerDialogManager.SelectItemModel<AcceptResult>(
          selectItem.key,
          AcceptResult(
            selectItem.value.checkResult,
            selectItem.value.checkResultDes,
            selectItem.value.handleDes,
            selectItem.value.handleDesType,
            selectItem.value.handleItems
          )
        )
        tv_product_inspection_conclusion_content.setText(selectItem.key)
        tv_product_treatment_content.text = selectItem.value.handleDes
        // showContainerCode(selectItem.value)
        if (selectItem.value.checkResult == AcceptOrderSpiltItem.UNQUALIFIED_ACCEPT_RESULT) {  // 当为不合格时，可以进行选择处理措施
          tv_product_treatment_content.text = selectItem.value.handleDes
          tv_product_treatment_content.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, R.drawable.icon_pull_down, 0)
          val selectItems = selectItem.value.handleItems.toList()
           ll_product_treatment_content.isEnabled = true
           ll_product_treatment_content.setOnClickListener {
             PickerDialogManager.showSingleSelectPicker(
               this,
               selectItems,
               0,
               PickerDialogManager.OnOptionsSelectCallback2<Pair<Int, String>> { _, t ->
                 tv_product_treatment_content.text = t.second
                 result.value.handleDes = t.second
                 result.value.handleDesType = t.first
               },
               PickerDialogManager.Adapter<Pair<Int, String>> { t -> t.second },
               object : OptionsSelectCancelListener {
                 override fun cancel() {
                 }
               }
             )
           }
         } else {  // 当为其他选项时，则不可以进行选择，直接赋值
           tv_product_treatment_content.text = selectItem.value.handleDes
           ll_product_treatment_content.isEnabled = false
           tv_product_treatment_content.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, 0, 0)
         }
         // 非不合格 非拒收
         if (selectItem.value.checkResult != AcceptOrderSpiltItem.UNQUALIFIED_ACCEPT_RESULT
                 && selectItem.value.checkResult != AcceptOrderSpiltItem.REFUSE_ACCEPT_RESULT) {
               unqualifiedMatters = mutableListOf()
               tv_product_disqualification_content.setText("")
               tv_product_disqualification_content.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, 0, 0)
          } else {
             tv_product_disqualification_content.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, R.drawable.icon_pull_down, 0)
          }
      }
      acceptResult = result
    }
    //点击不合格事项
    private fun showUnqualifiedMatters() {
      if (acceptResult == null) {
        showToast("请先选择验收结论")
        return
      }

      if (acceptResult?.key == "不合格" || acceptResult?.key == "拒收") {
        if (!unqualifiedMatters.isNullOrEmpty()) {
          showUnqualifiedMattersDialog()
        } else {
          mPresenter.getDictListByNameAndType("", "JSYY")
        }
      } else {
        showToast("当前选择的验收结论不正确，不能选择不合格事项")
        return
      }
    }
   // 不合格事项弹窗
    private fun showUnqualifiedMattersDialog() {
      PickerDialogManager.showSingleSelectPicker(
        mContext,
        unqualifiedMatters,
        PickerDialogManager.OnOptionsSelectCallback2<PickerDialogManager.SelectItemModel<DictListByNameAndType>> { index, t ->
          tv_product_disqualification_content.setText(t.value.dictName)
        },
        null
      )
    }
    // 验收结论成功
    override fun getDictListByNameAndTypeSuccess(requestBaseBean: BaseResponseBean<List<DictListByNameAndType>>) {
      if (requestBaseBean.isSuccess && !requestBaseBean.result.isNullOrEmpty()) {
        unqualifiedMatters = mutableListOf()
        requestBaseBean.result.forEach {
          val item: PickerDialogManager.SelectItemModel<DictListByNameAndType> =
            PickerDialogManager.SelectItemModel<DictListByNameAndType>(it.dictName, it)
           unqualifiedMatters?.add(item)
        }
        showUnqualifiedMattersDialog()
      }
    }
    /**
     * 显示批号选择
     */
    private fun showProductBatchCodeList() {
      if (this.purchaseProductBatchCodeVoList.size <= 0) {
        showToast("当前没有批号信息")
        return
      }

      PickerDialogManager.showSingleSelectPicker(
        mContext,
        this.purchaseProductBatchCodeVoList,
        PickerDialogManager.OnOptionsSelectCallback2<PickerDialogManager.SelectItemModel<PurchaseProductBatchCodeVoBean>> { _, t ->
          productBatchCodeResult(t)
        },
        null
      )
    }
   //根据批号返显
    private fun productBatchCodeResult(selectItem: PickerDialogManager.SelectItemModel<PurchaseProductBatchCodeVoBean>?) {
      if (selectItem != null) {
        this.purchaseProductBatchCodeVoBean = selectItem.value
        tv_product_batch_number_content.text =
          purchaseProductBatchCodeVoBean!!.productBatchCode // 批号
        tv_product_production_date_content.text =
          purchaseProductBatchCodeVoBean!!.productManufactureDate // 生产日期
        tv_product_indate_content.text =
          purchaseProductBatchCodeVoBean!!.productValidDate // 有效期
        tv_prepare_product_num_inspection_piece_content.text =
          purchaseProductBatchCodeVoBean!!.productCountBig // 待验件数
        tv_prepare_product_inspection_scatter_num_content.text =
          purchaseProductBatchCodeVoBean!!.productCountScatter // 待验零散数
        tv_prepare_product_num_inspection_content.text =
          purchaseProductBatchCodeVoBean!!.productCountSmall // 待验数量
      }
    }
    //是否存在追溯码
    override fun initCheckPageSuccess(result: BaseResponseBean<InitCheckPageBean>) {
      if (result.code == 0) {
        if (result.msg != null) {
          showToast(result.msg)
        }
        // 判断是否有追溯码
        if (result.result != null) {
          val initCheckPageBean = result.result
          if (initCheckPageBean.purchaseCheckOrderDetailVos != null) {
            DialogUtils.showConfirmDialog(
              this@CheckBillDetailActivityNew,
              "当前商品存在追溯码，是否前往追溯码界面？",
                object : ExitListener {
                  override fun cancel(tag: Int) {}
                  override fun sure(tag: Int) {
                    val intent = Intent()
                    intent.putExtra("initCheckPageBean", initCheckPageBean)
                    intent.setClass(mContext, TracingCodeScanningActivity::class.java)
                    startActivity(intent)
                  }
                },
                1
              )
          }
          // 补充验收单号
          tv_checkOrderCode.text = initCheckPageBean.checkOrderCode
        }
      } else {
        showToast(result.msg)
      }
    }
    /**
     * 驳回 确认提示
     */
    private fun rejectSureHint() {
      DialogUtils.showConfirmDialog(
        this@CheckBillDetailActivityNew,
        "是否将当前明细驳回？",
        object :
          ExitListener {
          override fun cancel(tag: Int) {
          }

          override fun sure(tag: Int) {
            verifyRejectData()
          }
        },
        1
      )
    }
    /**
     * 驳回 操作
     */
    private fun verifyRejectData() {
      if (packageBarCodeBean == null) {
        showToast("请先扫码商品条码操作")
        return
      }

      if (purchaseProductBatchCodeVoBean == null) {
        showToast("当前没有选择批号，无法进行驳回操作")
        return
      }

      mPresenter.turnDownCheckOrder(
        CheckOrderTurnDownPost(
          currentWaitCheckInfo?.supplierCode!!,
          purchaseProductBatchCodeVoBean?.productCode!!,
          purchaseProductBatchCodeVoBean?.productBatchCode!!,
          purchaseProductBatchCodeVoBean?.productManufactureDate!!,
          purchaseProductBatchCodeVoBean?.productValidDate!!,
          purchaseProductBatchCodeVoBean?.productPackingBigNumber!!,
          purchaseProductBatchCodeVoBean?.ownerCode!!,
          purchaseProductBatchCodeVoBean?.buildingCode!!,
          purchaseProductBatchCodeVoBean?.channelCode!!,
          purchaseProductBatchCodeVoBean?.sterilizingBatchNumber!!,
          purchaseProductBatchCodeVoBean?.productPackingMiddleNumber!!
        )
      )
    }
    //验收单驳回
    override fun turnDownCheckOrderSuccess(result: BaseResponseBean<CheckOrderTurnDownBean>) {
      if (result.code == 0) {
        // 驳回成功，清空页面，焦点定位到商品扫描文本框，显示当前验收员的验收单号，没有则不显示
        showToast(result.msg)
        resetPageData()
        tv_product_class.setText("")
        tv_product_class.requestFocus()
      } else {
        showToast(result.msg)
      }
    }
    fun resetPageData() {
      this.packageBarCodeBean = null

      tv_product_detail_product_name_content.text = "" // 商品名称
      tv_product_detail_recipel_type_content.text = "" // 处方分类
      tv_product_detail_recipel_type_content_suffix.text = ""
      tv_product_medium_packing_content.text = ""// 中包装
      tv_product_standard_content.text = "" // 规格
      tv_product_detail_product_code_content.text = "" // 商品编码
      tv_product_piece_packing_content.text = "" // 件包装
      tv_approval_number_content.text = "" // 批准文号

      // 下部分
      tv_product_batch_number_content.text = "" // 批号
      tv_product_production_date_content.text = "" // 生产日期
      tv_product_indate_content.text = "" // 有效期
      tv_prepare_product_num_inspection_piece_content.text = "" // 待验件数
      tv_prepare_product_inspection_scatter_num_content.text = "" // 待验零散数
      tv_prepare_product_num_inspection_content.text = "" // 待验数量
      tv_product_num_inspection_piece_content.setText("") // 验收件数
      tv_product_inspection_scatter_num_content.setText("") // 验收零散数
      tv_product_num_inspection_content.text = "" // 验收件数

      tv_product_inspection_conclusion_content.text = "" // 验收结论
      acceptResult = null
      tv_product_treatment_content.text = "" // 措施
      tv_product_disqualification_content.text = "" // 不合格事项
      tv_product_inspection_note_content.setText("") // 验收备注

      this.purchaseProductBatchCodeVoBean = null

      // 移除当前选择批号
      if (purchaseProductBatchCodeVoList.size > 0) {
        for (selectItemModel in this.purchaseProductBatchCodeVoList) {
          if (selectItemModel.value == this.purchaseProductBatchCodeVoBean) {
            this.purchaseProductBatchCodeVoList.remove(selectItemModel)
            return
          }
        }
      }

      // 清理异常图片集合
      imgUrlsList = listOf()
    }
    /**
     * 验收提交 操作
     */
    private fun checkCommitData() {
      // 先保存 再验收提交
      if (tv_checkOrderCode.text.toString() == "") {
        showToast("请先保存再验收提交操作！")
        return
      }
      mPresenter.doSubmitCheckOrder(
        CheckOrderDoSubmitPost(
          tv_checkOrderCode.text.toString(),
          secondCheckUser,
          currentRejects,
          rejectRemark
        )
      )
    }
    /**
     * 验收提交回调
     */
    override fun doSubmitCheckOrderSuccess(checkOrderDoSubmitBean: BaseResponseBean<String>) {
      // code=20002时，需要双人验收校验
      if (checkOrderDoSubmitBean.code == 20002) {
        showSpecialDrugHint()
      } else if (checkOrderDoSubmitBean.code == 20003) {  // code=20003时需要选择是否当场拒收
        showRejectionBillDialog()
      } else if (checkOrderDoSubmitBean.code == 1) {
        showToast(checkOrderDoSubmitBean.msg)
        return
      } else if (checkOrderDoSubmitBean.code == 0) {
        showToast(checkOrderDoSubmitBean.msg)
        // 保存成功后提示成功信息，清空页面，焦点定位到商品扫描文本框 批号不能选择
        tv_product_batch_number_content.isEnabled = false
        resetPageData()
        tv_product_class.requestFocus()
        finish()
        return
      } else if (checkOrderDoSubmitBean.code == 20005) { // 需要弹出追溯码扫描的框
        currentWaitCheckInfo?.let {
          mPresenter.initCheckPage(InitCheckPagePost(it.supplierCode!!, currentWaitCheckInfo?.ownerCode!!,currentWaitCheckInfo?.channelCode!!))
//          tv_product_name.text = it.supplierName
        }
      }
    }
    /**
     * 特管商品校验
     */
    private fun showSpecialDrugHint() {
      SpecialDrugHint(mContext, SpecialDrugHint.TYPE_ACCEPT, LoginConfirmListener { userName, password ->
        val secondLoginPost = SecondLoginPost(userName, password, "btn:wms:inStorageDoubleCheck")
        mPresenter.secondLogin(secondLoginPost)
        showWaitDialog("确认中...")
      }).show()
    }
    /**
     * 拒收提示框
     */
    private fun showRejectionBillDialog() {
      RejectionBillDialog(mContext, RejectionBillListener { isReject, rejectNote ->
        if (isReject) currentRejects = "1" else currentRejects = "2"
        rejectRemark = rejectNote
        mPresenter.doSubmitCheckOrder(  //提交验收单
          CheckOrderDoSubmitPost(
            tv_checkOrderCode.text.toString(),
            secondCheckUser,
            currentRejects,
            rejectRemark
          )
        )
      }).show()
    }
    override fun secondLoginSuccess(result: BaseResponseBean<SecondLoginResult>) {
      if (result.code == 0) {
        secondCheckUser = result.result.id.toString()
        mPresenter.doSubmitCheckOrder(
          CheckOrderDoSubmitPost(
            tv_checkOrderCode.text.toString(),
            secondCheckUser,
            currentRejects,
            rejectRemark
          )
        )
      } else {
        showToast(result.msg)
        return
      }
    }
    private fun verifyCommitData() {
      // 当验收结论选择“不合格”或“拒收”时，不合格事项必填；其他选项可为空。
      if (acceptResult?.key == "不合格" || acceptResult?.key == "拒收") {
        if (tv_product_disqualification_content.text.isEmpty()) {
          showToast("不合格事项不能为空")
          return
        }
      } else {
        if (tv_container_code.text.toString().isEmpty()) {
          showToast("请先扫码绑定容器号")
          return
        }
      }

      if (tv_product_num_inspection_content.text.toString()
          .isEmpty() || tv_product_num_inspection_content.text.toString().toInt() == 0
      ) {
        showToast("保存的验收数量不能为0")
        return
      }

      if (acceptResult == null) {
        showToast("请先进行验收结论选择")
        return
      }

      if (purchaseProductBatchCodeVoBean == null) {
        showToast("当前没有商品可以进行保存")
        return
      }

      // 如果验收件数大于零 则验收零散数必须为零 如果验收零散数大于零 则验收件数大于零
      var productNumValue = 0
      var productInspectionScatterNumValue = 0

      if (tv_product_num_inspection_piece_content.text.toString() != "") {
        productNumValue = tv_product_num_inspection_piece_content.text.toString().toInt()
      }
      if (tv_product_inspection_scatter_num_content.text.toString() != "") {
        productInspectionScatterNumValue = tv_product_inspection_scatter_num_content.text.toString().toInt()
      }

      if (productNumValue > 0) {
        if (productInspectionScatterNumValue > 0) {
          showToast("当验收件数大于零，验收零散数不能大于零！")
          return
        }
      }
      if (productInspectionScatterNumValue > 0) {
        if (productNumValue > 0) {
          showToast("当验收零散数大于零，验收件数不能大于零！")
          return
        }
      }

      mPresenter.saveCheckOrder(
        CheckOrderSavePost(
          tv_checkOrderCode.text.toString(),
          currentWaitCheckInfo?.supplierCode!!,
          purchaseProductBatchCodeVoBean?.productCode!!,
          purchaseProductBatchCodeVoBean?.productBatchCode!!,
          purchaseProductBatchCodeVoBean?.productManufactureDate!!,
          purchaseProductBatchCodeVoBean?.productValidDate!!,
          purchaseProductBatchCodeVoBean?.productPackingBigNumber!!,
          purchaseProductBatchCodeVoBean?.ownerCode!!,
          purchaseProductBatchCodeVoBean?.buildingCode!!,
          purchaseProductBatchCodeVoBean?.channelCode!!,
          purchaseProductBatchCodeVoBean?.sterilizingBatchNumber!!,
          purchaseProductBatchCodeVoBean?.productPackingMiddleNumber!!,
          tv_product_num_inspection_piece_content.text.toString(),
          tv_product_inspection_scatter_num_content.text.toString(),
          acceptResult?.value?.checkResult!!,
          acceptResult?.value?.handleDesType!!,
          tv_product_inspection_note_content.text.toString(),
          purchaseProductBatchCodeVoBean?.productType!!,
          tv_container_code.text.toString(),
          imgUrlsList,
          tv_product_disqualification_content.text.toString()
        )
      )
    }
    //验收保存
    override fun saveCheckOrderSuccess(result: BaseResponseBean<String>) {
      if (result.code == 2001) {
        showCommodityMaintenanceDialog()
      } else if (result.code == 0) {
        // 提交成功后提示成功信息，清空页面，焦点定位到商品扫描文本框，显示验收单号
        showToast(result.msg)
        resetPageData()
        tv_product_class.setText("")
        tv_product_class.requestFocus()
        tv_checkOrderCode.text = result.msg
        this.exception_picture_index = 0
        btn_exception_handling_picture.text = "异常图片"
        mPresenter.initCheckPage(InitCheckPagePost(currentWaitCheckInfo?.supplierCode!!, currentWaitCheckInfo?.ownerCode!!, currentWaitCheckInfo?.channelCode!!))
      } else {
        showToast(result.msg)
      }
    }
    /**
     * 扫描容器号成功回调
     */
    override fun selectContainerSuccess(result: BaseResponseBean<SelectContainerBean>) {
      if (result.code == 0) {
        if (result.result != null) {
          var bean = result.result
          this.selectContainerBean = bean

          // 先判断容器是否能正常使用的情况
          if (bean.containerStatus == 3) {
            // 再判断是否当前是整散合一还是整散分开的情况
            if (purchaseProductBatchCodeVoBean?.storageClassification == 0) {
              // 整散分开的情况
              // 扫描容器为托盘号时
              if (bean.containerType == 2) {
                // 当整件待验件数为0，提示：没有待验整件，请更换容器
                if (this.purchaseProductBatchCodeVoBean?.productCountBig?.toInt() == 0) {
                  DialogUtils.showConfirmDialog(
                    this@CheckBillDetailActivityNew,
                    "没有待验整件，请更换容器！",
                    object :
                      ExitListener {
                      override fun cancel(tag: Int) {}
                      override fun sure(tag: Int) {}
                    },
                    1
                  )
                } else if (this.purchaseProductBatchCodeVoBean?.productCountBig?.toInt()!! > 0) {
                  // 当整件待验件数大于0，扫描成功，焦点定位到验收件数，验收零散数设置为不能录入
                  tv_product_num_inspection_piece_content.requestFocus()
                  tv_product_inspection_scatter_num_content.isEnabled = false
                  tv_product_num_inspection_piece_content.isEnabled = true
                }
              } else if (bean.containerType == 1) {
                // 扫描容器为周转箱号时
                // 焦点定位到验收零散数，验收件数设置为不能录入
                tv_product_inspection_scatter_num_content.requestFocus()
                tv_product_num_inspection_piece_content.isEnabled = false
                tv_product_inspection_scatter_num_content.isEnabled = true
              }
            } else if (purchaseProductBatchCodeVoBean?.storageClassification == 1) {
              // 整散合一的情况
              tv_product_inspection_scatter_num_content.requestFocus()
              tv_product_num_inspection_piece_content.isEnabled = true
              tv_product_inspection_scatter_num_content.isEnabled = true
            }
            // 扫描容器，重置验收件数、零散数、数量为0
            tv_product_num_inspection_piece_content.setText("")
            tv_product_inspection_scatter_num_content.setText("")
            tv_product_num_inspection_content.text = "0"
          } else if (bean.containerStatus == 2) {
            showToast("当前扫描容器处于占用状态！")
          } else if (bean.containerStatus == 1) {
            showToast("当前扫描容器处于锁定状态！")
          }
        }
      } else {
        showToast(result.msg)
        tv_container_code.setText("")
      }
    }
    private fun showCommodityMaintenanceDialog() {
      // 此商品逻辑区域没有维护，请先进行商品信息维护！
      DialogUtils.showConfirmDialog(
        this@CheckBillDetailActivityNew,
        "此商品库房属性未维护，请先进行维护！",
        object : ExitListener {
          override fun cancel(tag: Int) {
          }

          override fun sure(tag: Int) {
            val intent = Intent()
            intent.putExtra("productCode", packageBarCodeBean?.productCode)
            intent.setClass(mContext, CommodityInformationMaintenanceActivity::class.java)
            startActivityForResult(intent, 3)
          }
        }, 1
      )
    }

    // 异常图片上传相关方法实现
    override fun uploadExceptionPicturesSuccess(response: BaseResponseBean<PictureUploadResponse>) {
        if (response.isSuccess()) {
            response.data?.let { uploadResponse ->
                if (uploadResponse.success) {
                    exception_picture_index = uploadResponse.pictureUrls.size
                    updateExceptionPictureButtonText()
                    showToast("图片上传成功")
                } else {
                    showToast("图片上传失败：${uploadResponse.message}")
                }
            }
        } else {
            showToast("图片上传失败：${response.msg}")
        }
    }

    override fun deleteExceptionPicturesSuccess(response: BaseResponseBean<Boolean>) {
        if (response.isSuccess()) {
            response.data?.let { success ->
                if (success) {
                    exception_picture_index = 0
                    updateExceptionPictureButtonText()
                    showToast("图片删除成功")
                } else {
                    showToast("图片删除失败")
                }
            }
        } else {
            showToast("图片删除失败：${response.msg}")
        }
    }

    override fun getExceptionPicturesSuccess(response: BaseResponseBean<List<String>>) {
        if (response.isSuccess()) {
            response.data?.let { pictureUrls ->
                exception_picture_index = pictureUrls.size
                updateExceptionPictureButtonText()
            }
        }
    }

    /**
     * 显示异常图片上传弹窗
     */
    private fun showExceptionPictureDialog() {
        // 创建业务信息
        val businessInfo = BusinessInfo(
            businessId = currentWaitCheckInfo?.checkOrderCode ?: "",
            businessType = "CHECK_ACCEPT",
            businessCode = currentWaitCheckInfo?.checkOrderCode ?: "",
            businessName = "入库验收单",
            relatedInfo = mapOf(
                "商品编码" to (packageBarCodeBean?.productCode ?: ""),
                "商品名称" to (packageBarCodeBean?.productName ?: ""),
                "供应商" to (currentWaitCheckInfo?.supplierName ?: "")
            )
        )

        // 创建并显示弹窗
        currentExceptionPictureDialog = ExceptionPictureDialog(this)
        currentExceptionPictureDialog?.let { dialog ->
            dialog.businessInfo = businessInfo
            dialog.onPictureUploadListener = { count ->
                exception_picture_index = count
                updateExceptionPictureButtonText()
            }
            dialog.show()
        }
    }

    /**
     * 更新异常图片按钮文本
     */
    private fun updateExceptionPictureButtonText() {
        btn_exception_handling_picture.text = if (exception_picture_index > 0) {
            "异常图片($exception_picture_index)"
        } else {
            "异常图片"
        }
    }

    /**
     * 处理权限请求结果
     */
    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        currentExceptionPictureDialog?.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

    /**
     * 处理Activity结果
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        // 处理异常图片相关的Activity结果
        when (requestCode) {
            2001 -> { // 拍照结果
                if (resultCode == RESULT_OK) {
                    currentExceptionPictureDialog?.handleCameraResult()
                }
            }
            2002 -> { // 相册选择结果
                if (resultCode == RESULT_OK) {
                    currentExceptionPictureDialog?.handleGalleryResult(data)
                }
            }
        }
    }

}


