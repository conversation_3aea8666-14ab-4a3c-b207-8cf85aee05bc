package com.xyy.wms.pda.bean.moveStorage;

import com.xyy.wms.pda.bean.inmanager.MovementListBean;

import java.io.Serializable;
import java.util.List;

public class PalletGetGoodsResult implements Serializable {
    Number gaId;
    String productCode;
    String productName;
    String sourceAllocation;
    String batchNumber;
    String logicalRegionName;
    Number amount;

    public void setGaId(Number gaId){this.gaId = gaId;}
    public Number getGaId(){return gaId;}
    public void setProductCode(String productCode){this.productCode = productCode;}
    public String getProductCode(){return productCode;}
    public void setProductName(String productName){this.productName=productName;}
    public String getProductName(){return productName;}
    public void setBatchNumber(String batchNumber){this.batchNumber = batchNumber;}
    public String getBatchNumber(){return batchNumber;}
    public void setLogicalRegionName(String logicalRegionName){this.logicalRegionName = logicalRegionName;}
    public String getLogicalRegionName(){return logicalRegionName;}
    public void setAmount(Number amount){this.amount = amount;}
    public Number getAmount(){return amount;}
    public void setSourceAllocation(String sourceAllocation){this.sourceAllocation = sourceAllocation;}
    public String getSourceAllocation(){return sourceAllocation;}
}


