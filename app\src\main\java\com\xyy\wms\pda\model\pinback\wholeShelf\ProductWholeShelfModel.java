package com.xyy.wms.pda.model.pinback.wholeShelf;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiPinBackService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.pinback.PostShelfListBean;
import com.xyy.wms.pda.bean.pinback.ProductPosition;
import com.xyy.wms.pda.bean.pinback.ProductPositionListBean;
import com.xyy.wms.pda.bean.pinback.ShelfResult;
import com.xyy.wms.pda.contract.pinback.wholeShelf.ProductWholeShelfContract;
import com.xyy.wms.pda.model.checkSelectGoodsPosition.pinback.PinBackCheckGoodsPositionModel;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * Created by lx on 2018/11/10 14
 * E-Mail：<EMAIL>
 * 销退商品整体上架
 */
public class ProductWholeShelfModel implements ProductWholeShelfContract.IProductWholeShelfModel, PinBackCheckGoodsPositionModel {

    public static ProductWholeShelfModel newInstance() {
        return new ProductWholeShelfModel();
    }

    @Override
    public Observable<BaseResponseBean> checkSelectGoodsPosition(ProductPositionListBean<ProductPosition> productPositionListBean) {
        return RetrofitCreateHelper.createApi(ApiPinBackService.class).checkSelectGoodsPosition(productPositionListBean).
                compose(RxHelper.rxSchedulerHelper());
    }
    @Override
    public Observable<BaseResponseBean<ShelfResult>> commitWholeShelfList(PostShelfListBean wholeShelfListBean) {
        return RetrofitCreateHelper.createApi(ApiPinBackService.class).commitShelfList(wholeShelfListBean)
                .compose(RxHelper.rxSchedulerHelper());
    }
}
