package com.xyy.wms.pda.bean.inmanager.check;

/**
 * 盘点任务 首页获取数量
 */
public class CheckTaskNumBean {

    /**
     * firstCheckNum number	@mock=0 初盘任务数
     * secondCheckNum number @mock=0 复盘任务数
     * thirdCheckNum number	@mock=0终盘任务数*/

    /**
     * 初盘数量
     */
    private String firstCheckNum;
    /**
     * 复盘数量
     */
    private String secondCheckNum;
    /**
     * 终盘数量
     */
    private String thirdCheckNum;
    /**
     * 当前是否有已经领取的任务
     * 0没有任务1初盘有任务2复盘有任务3终盘有任务
     */
    private int checkPlanType;

    public String getFirstCheckNum() {
        return firstCheckNum;
    }

    public void setFirstCheckNum(String firstCheckNum) {
        this.firstCheckNum = firstCheckNum;
    }

    public String getSecondCheckNum() {
        return secondCheckNum;
    }

    public void setSecondCheckNum(String secondCheckNum) {
        this.secondCheckNum = secondCheckNum;
    }

    public String getThirdCheckNum() {
        return thirdCheckNum;
    }

    public void setThirdCheckNum(String thirdCheckNum) {
        this.thirdCheckNum = thirdCheckNum;
    }

    public int getCheckPlanType() {
        return checkPlanType;
    }

    public void setCheckPlanType(int checkPlanType) {
        this.checkPlanType = checkPlanType;
    }

    @Override
    public String toString() {
        return "CheckTaskNumBean{" +
                "firstCheckNum='" + firstCheckNum + '\'' +
                ", secondCheckNum='" + secondCheckNum + '\'' +
                ", thirdCheckNum='" + thirdCheckNum + '\'' +
                ", checkPlanType=" + checkPlanType +
                '}';
    }
}
