package com.xyy.wms.pda.model.out.purchaseexit;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.DrugRegulatoryCodeBean;
import com.xyy.wms.pda.contract.out.purchaseexit.PurchaseExitElectronicDetailContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.HashMap;

import io.reactivex.Observable;

/**
 * Created by zcj on 2018/11/19 21
 */
public class PurchaseExitElectronicDetailModel implements PurchaseExitElectronicDetailContract.IPurchaseExitElectronicDetailModel {
    public static PurchaseExitElectronicDetailModel newInstance() {
        return new PurchaseExitElectronicDetailModel();
    }

    @Override
    public Observable<BaseResponseBean> saveTheUnlock(String productBatchCode, String productCode, String refundOrderCode) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).saveTheUnlock(productBatchCode, productCode, refundOrderCode)
                .compose(RxHelper.<BaseResponseBean>rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<DrugRegulatoryCodeBean>> findDrugRegulatoryCode(HashMap<String, String> map) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).findDrugElectronicSuperVisionCode(map)
                .compose(RxHelper.<BaseResponseBean<DrugRegulatoryCodeBean>>rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<DrugRegulatoryCodeBean>> findScannedQuantity(String lineNumber, String orderCode) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).findScannedQuantity(lineNumber, orderCode)
                .compose(RxHelper.<BaseResponseBean<DrugRegulatoryCodeBean>>rxSchedulerHelper());
    }
}
