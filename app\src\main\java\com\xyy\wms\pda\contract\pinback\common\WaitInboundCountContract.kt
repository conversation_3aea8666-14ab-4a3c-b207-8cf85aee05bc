package com.xyy.wms.pda.contract.pinback.common

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.api.ApiInExitService
import com.xyy.wms.pda.api.ApiPinBackService
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.shelf.CommitShelfResult
import com.xyy.wms.pda.helper.SimpleErrorConsumer
import com.xyy.wms.pda.helper.SimpleSuccessConsumer
import com.xyy.wms.pda.net.RetrofitCreateHelper

import io.reactivex.Observable;

/**
 * 根据上架类型获取待上架任务数
 */
interface WaitInboundCountContract {
    interface IWaitInboundCountModel : IBaseModel {
        fun getWaitInboundCount(shelfType: String): Observable<BaseResponseBean<Int>>
    }

    interface IWaitInboundCountView : IBaseActivity {
        fun getWaitInboundCountSuccess(requestBaseBean: BaseResponseBean<Int>)
    }

    abstract class IScatteredShelfListPresenter : BasePresenter<IWaitInboundCountModel, IWaitInboundCountView>() {
        abstract fun getWaitInboundCount(shelfType: String)
    }
}

class WaitInboundCountModel : WaitInboundCountContract.IWaitInboundCountModel {
    override fun getWaitInboundCount(shelfType: String): Observable<BaseResponseBean<Int>> {
        return RetrofitCreateHelper.createApi(ApiPinBackService::class.java).waitInboundCount(shelfType)
                .compose(RxHelper.rxSchedulerHelper())
    }
}

class WaitInboundCountPresenter : WaitInboundCountContract.IScatteredShelfListPresenter() {

    override fun getModel(): WaitInboundCountContract.IWaitInboundCountModel {
        return WaitInboundCountModel()
    }

    override fun getWaitInboundCount(shelfType: String) {
        mRxManager.register(mIModel.getWaitInboundCount(shelfType).subscribe(object : SimpleSuccessConsumer<BaseResponseBean<Int>>(mIView) {
            override fun onSuccess(t: BaseResponseBean<Int>) {
                mIView.getWaitInboundCountSuccess(t)
            }
        }, SimpleErrorConsumer(mIView)))
    }
}