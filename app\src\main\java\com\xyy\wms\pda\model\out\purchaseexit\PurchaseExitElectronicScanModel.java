package com.xyy.wms.pda.model.out.purchaseexit;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseCodeScanResultBean;
import com.xyy.wms.pda.contract.out.purchaseexit.PurchaseExitElectronicScanContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * Created by lwj on 2020-02-28.
 * <EMAIL>
 */
public class PurchaseExitElectronicScanModel implements PurchaseExitElectronicScanContract.IPurchaseExitElectronicScanModel {

    public static PurchaseExitElectronicScanModel newInstance() {
        return new PurchaseExitElectronicScanModel();
    }

    @Override
    public Observable<BaseResponseBean<PurchaseCodeScanResultBean>> findDrugRegulatoryCode(String actualRefundCount, String codeLevel, String lineNumber, String pickUpOrder, String productCode, String regulatoryCode) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).findDrugRegulatoryCode(actualRefundCount, codeLevel, lineNumber, pickUpOrder, productCode, regulatoryCode)
                .compose(RxHelper.rxSchedulerHelper());
    }
}
