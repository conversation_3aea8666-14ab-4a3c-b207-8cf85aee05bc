package com.xyy.wms.pda.constant

/**
 * 页面通信key常量
 */
object KeyConstant {

  /**
   * 入库 - 验收相关
   */
  const val INTENT_CHECK_ORDER_DETAIL = "checkOrderDetail"
  const val INTENT_CHECK_ORDER_CODE = "checkOrderCode"
  const val INTENT_CHECK_CONTAINER_CODE = "containerCodes"

  /**
   * 库内 - 盘查
   */
  const val INTENT_KEY_CHECK_UP = "intent_key_check_up"

  /**
   * 库内 - 货位调整-bean
   */
  const val INTENT_KEY_ADD_PA = "intent_key_add_pa"

  /**
   * 库内 - 货位调整-no
   */
  const val INTENT_KEY_ADD_NO = "intent_key_add_no"

  /**
   * 库内 - 进入补货上架/补货下架
   */
  const val INTENT_KEY_REPLENISHMENT_TYPE = "intent_key_replenishment_type"

  /**
   * 库内 - 业务类型调整相关
   */
  const val INVENT_KEY_MOVE_COMMON_OWNER = "invent_key_move_common_owner"
  const val INVENT_KEY_MOVE_COMMON_BUILDING = "invent_key_move_common_building"
  const val INTENT_KEY_MOVE_ORDER_NO = "intent_key_move_order_no"
  const val INTENT_KEY_CHANNEL_MOVE_EXECUTE_ORDER_ID = "intent_key_channel_move_execute_order_id"
  const val INTENT_KEY_CHANNEL_MOVE_EXECUTE_ORDER_PRODUCT_INFO_ID = "intent_key_channel_move_execute_order_product_info_id"

  /**
   * 出库 - 外复核出库单
   */
  const val INTENT_KEY_REVIEW_OUT_ORDER = "intent_key_review_out_order"

  /**
   * 出库 - 外复核  出库单信息
   */
  const val INTENT_KEY_REVIEW_OUT_BOUND_ORDER = "intent_key_review_out_bound_order"

  /**
   * 出库 - 外复核  已复核件数
   */
  const val INTENT_KEY_REVIEW_OUT_REVIEW_NUMBER = "intent_key_review_out_review_number"

  /**
   * 出库 - 拆零拣货单
   */
  const val INTENT_KEY_SCATTERED_PICKING_ORDER = "intent_key_scattered_picking_order"

  /**
   * 出库 - 拆零拣货当前订单位置
   */
  const val INTENT_KEY_SCATTERED_PICKING_POSITION = "intent_key_scattered_picking_position"

  /**
   * 出库 - 拆零拣货列表集合大小
   */
  const val INTENT_KEY_SCATTERED_PICKING_SIZE = "intent_key_scattered_picking_size"

  /**
   * 出库 - 拆零拣货列表批拣单号
   */
  const val INTENT_KEY_BATCH_INSPECTION_CODE = "intent_key_batch_inspection_code"

  /**
   * 购进退出执行单
   */
  const val INTENT_KEY_PURCHASE_EXIT_ORDER = "intent_key_purchase_exit_order"
  const val INTENT_KEY_PURCHASE_EXIT_ORDER_LIST = "intent_key_purchase_exit_list"
  const val INTENT_KEY_PURCHASE_GOODS_LIST = "intent_key_purchase_goods_list"
  const val INTENT_KEY_PURCHASE_PRIV_USERS = "intent_key_purchase_priv_users"
  const val INTENT_KEY_PURCHASE_REVIEW_LAST_GOODS = "intent_key_purchase_review_last_goods"
  const val INTENT_KEY_PURCHASE_GOODS = "intent_key_purchase_goods"
  const val INTENT_KEY_PURCHASE_EXIT_CODE = "intent_key_purchase_exit_code"
  const val INTENT_KEY_PURCHASE_EXIT_ORDER_POSITION = "intent_key_purchase_exit_order_position"
  const val TYPE = "type"
  const val INTENT_KEY_PICK_ORDER = "pickUpOrder"
  const val INTENT_KEY_BUILD_NAME = "buildName"
  const val INTENT_KEY_PURCHASE_EXIT_BILL_STATE = "intent_key_purchaseExitBillState"
}
