package com.xyy.wms.pda.bean.instorage.checkaccept

import java.io.Serializable

/**
 * 验收整单提交 postBean
 */
class AcceptDetailListPost : Serializable {
    var checkOrderCode: String? = null//验收单号	string	@mock=YSD202003030071
    var checkOrderDetailVos: ArrayList<AcceptCommitBean>? = null//	验收单明细对应的拆分行	array<object>
    var currentRejects: String? = null//	是否当场拒收 (是"1" 否"2")	string	@mock=1
    var receiveOrderCode: String? = null//收货单号	string	@mock=
    var receiveUser: String? = null//收货人	string	@mock=
    var rejectRemark: String? = null//	拒收备注	string	@mock=拒收原因
    var secondCheckUser: String? = null//		string	@mock=拒收原因
}