package com.xyy.wms.pda.bean.driver

import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.View
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.xyy.utilslibrary.base.activity.BaseCompatActivity
import com.xyy.wms.pda.R
import com.xyy.wms.pda.utils.ConstantUtil
import kotlinx.android.synthetic.main.activity_driver_home.toolbar_whole_shelf
import kotlinx.android.synthetic.main.activity_photo_preview.photo_view

class PhotoPreActivity:BaseCompatActivity() {
  override fun initView(savedInstanceState: Bundle?) {
    toolbar_whole_shelf.setNavigationOnClickListener(View.OnClickListener {
      finish()
    })

    var path =intent.getStringExtra(ConstantUtil.KEY_PHOTO_PATH)
    Glide.with(this).load(path).into(object : SimpleTarget<Drawable>(){
      override fun onResourceReady(resource: Drawable, transition: Transition<in Drawable>?) {
        photo_view.setImageDrawable(resource)
      }

    });


  }

  override fun getLayoutId() = R.layout.activity_photo_preview
}
