package com.xyy.wms.pda.presenter.inmanager;

import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.inmanager.ReviewExceptionListBean;
import com.xyy.wms.pda.bean.inmanager.post.ReviewExceptionPostBean;
import com.xyy.wms.pda.contract.inmanager.ReviewExceptionListContract;
import com.xyy.wms.pda.helper.SimpleErrorConsumer;
import com.xyy.wms.pda.helper.SimpleSuccessConsumer;
import com.xyy.wms.pda.model.inmanager.ReviewExceptionListModel;

/**
 * Created by zcj on 2018/11/14 11
 */
public class ReviewExceptionListPresenter extends ReviewExceptionListContract.ReviewExceptionListPresenter {
    public static ReviewExceptionListPresenter newInstance() {

        return new ReviewExceptionListPresenter();
    }

    int pageSize = 10;
    int pageNo = 1;

    @Override
    public void exceptionPage(final boolean refresh, ReviewExceptionPostBean bean) {
        if (mIView == null || mIModel == null) return;
        if (refresh) {
            pageNo = 1;
            mIView.enableLoadMore(true);
        }
        bean.setPageNum(String.valueOf(pageNo));
        bean.setPageSize(String.valueOf(pageSize));
        mRxManager.register(mIModel.exceptionPage(bean).subscribe(new SimpleSuccessConsumer<BaseResponseBean<ReviewExceptionListBean>>(mIView) {
            @Override
            public void onSuccess(BaseResponseBean<ReviewExceptionListBean> bean) {
                if (mIView == null) return;
                if (bean.getResult() != null) {
                    if (bean.getResult().isLastPage()) {
                        mIView.loadMoreComplete();
                    }
                    mIView.exceptionPageSuccess(refresh, bean);
                }
                pageNo++;
            }
        }, new SimpleErrorConsumer(mIView)));
    }

    @Override
    public void updateException(String dealMode,
                                String dealUserName,
                                String id,
                                String isAskFor,
                                String status,
                                String remarkstr) {
        if (mIModel == null || mIView == null) return;
        mRxManager.register(mIModel.updateException(dealMode, dealUserName, id, isAskFor, status, remarkstr).subscribe(new SimpleSuccessConsumer<BaseResponseBean>(mIView) {
            @Override
            public void onSuccess(BaseResponseBean bean) {
                if (mIView == null) return;
                mIView.updateExceptionSuccess(bean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }

    @Override
    protected ReviewExceptionListContract.IReviewExceptionListModel getModel() {
        return ReviewExceptionListModel.newInstance();
    }
}
