package com.xyy.wms.pda.model.inmanager

import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.inmanager.AddPositionAdjustmentBean
import com.xyy.wms.pda.contract.inmanager.AddPositionAdjustmentContract.IAddPositionAdjustmentModel
import com.xyy.wms.pda.model.ServiceModel
import io.reactivex.Observable

class AddPositionAdjustmentModel : ServiceModel(), IAddPositionAdjustmentModel {

  override fun getLocationMovementNo(): Observable<BaseResponseBean<String>> {
    return apiInManagerService.locationMovementNo.compose(RxHelper.rxSchedulerHelper())
  }

  override fun getGoodsInfoByLocationCode(map: Map<String, String>): Observable<BaseResponseBean<List<AddPositionAdjustmentBean>>> {
    return apiInManagerService.getGoodsInfoByLocationCode(map).compose(RxHelper.rxSchedulerHelper())
  }

  companion object {
    @JvmStatic
    fun newInstance(): AddPositionAdjustmentModel {
      return AddPositionAdjustmentModel()
    }
  }
}
