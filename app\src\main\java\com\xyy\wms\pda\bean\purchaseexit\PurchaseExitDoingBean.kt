package com.xyy.wms.pda.bean.purchaseexit

import java.io.Serializable

/**
 * 购进退出单 - 商品列表
 */
class PurchaseExitDoingBean : Serializable {

    var refundOrderStatus: Int = 0  // 退出单状态
    var productCode: String? = null  // 商品编码
    var productName: String? = null  // 商品名称
    var pickUpOrder: String? = null  // 拣货单号
    var buildingName: String? = null // 建筑物
    var ownerName: String? = null    // 业主名称
    var refundOrderCode: String? = null // 购进退出单号
    var supplierName: String? = null    // 供应商
    var manufacturer: String? = null    // 生产厂家
    var packingUnit: String? = null     // 单位
    var specifications: String? = null  // 规格
    var refundCount: String? = null     // 数量
    var quantityCompletion: Int = 0  // 完成数量(进度分子)
    var size: Int = 0  // 总数量(进度分母)

}
