package com.xyy.wms.pda.bean.instorage.checkaccept

import java.io.Serializable

/**
 *  created by  liang<PERSON>  on 2020-03-17.
 */
class AcceptCommitBean : Serializable {
    var checkOrderCode: String? = null //验收单号	string	@mock=$order('YSD202003030071','YSD202003030071')
    var checkOrderDetailSplitVoList: ArrayList<AcceptCommitSplitItemBean> = ArrayList()
    var containerCode: String? = null //    容器编号    string    @mock =
    var lineNumber: Int? = null //   行号    number    @mock = 1
    var productCode: String? = null //   商品编号    string    @mock =
    var productName: String? = null //   商品名称    string    @mock =
    var sortNumber: Int? = null //  明细界面 排序后的序号 用来提示错误行号    number    @mock = 0
}