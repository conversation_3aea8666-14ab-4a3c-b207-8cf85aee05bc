package com.xyy.wms.pda.model.out.purchaseexit;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.CacheAreaPdaBean;
import com.xyy.wms.pda.bean.purchaseexit.UpdateReviewStatusBean;
import com.xyy.wms.pda.contract.out.purchaseexit.PurchaseExitElectronicReViewUpdateStatusContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * Created by lwj on 2020-03-03.
 * <EMAIL>
 */
public class PurchaseExitElectronicReViewUpdateStatusModel implements PurchaseExitElectronicReViewUpdateStatusContract.IPurchaseExitElectronicReViewUpdateStatusModel {


    public static PurchaseExitElectronicReViewUpdateStatusModel newInstance() {
        return new PurchaseExitElectronicReViewUpdateStatusModel();
    }


    @Override
    public Observable<BaseResponseBean<UpdateReviewStatusBean>> updateReviewStatus(String refundOrderCode, String modifyOrder, String pickUpOrder, String refundShelfTemporary, String secondRecheckUser) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).updateReviewStatus(refundOrderCode, modifyOrder, pickUpOrder, refundShelfTemporary, secondRecheckUser)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<CacheAreaPdaBean>> getCacheAreaPda() {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).getCacheAreaPda().compose(RxHelper.rxSchedulerHelper());
    }
}

