package com.xyy.wms.pda.presenter.inmanager.check;


import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.inmanager.check.CheckTaskNumBean;
import com.xyy.wms.pda.contract.inmanager.check.CheckTaskContract;
import com.xyy.wms.pda.helper.SimpleErrorConsumer;
import com.xyy.wms.pda.helper.SimpleSuccessConsumer;
import com.xyy.wms.pda.model.inmanager.check.CheckTaskModel;

public class CheckTaskPresenter extends CheckTaskContract.CheckTaskPresenter {
    public static CheckTaskPresenter newInstance() {
        return new CheckTaskPresenter();
    }

    @Override
    protected CheckTaskContract.ICheckPersenterModel getModel() {
        return CheckTaskModel.newInstance();
    }

    @Override
    public void selectCheckTaskNums() {
        if (mIView == null || mIModel == null)
            return;
        mRxManager.register(mIModel.selectCheckTaskNums().subscribe(new SimpleSuccessConsumer<BaseResponseBean<CheckTaskNumBean>>(mIView) {
            @Override
            public void onSuccess(BaseResponseBean<CheckTaskNumBean> bean) {
                if (mIView == null)
                    return;
                mIView.selectCheckTaskNumsSuccess(bean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }
}
