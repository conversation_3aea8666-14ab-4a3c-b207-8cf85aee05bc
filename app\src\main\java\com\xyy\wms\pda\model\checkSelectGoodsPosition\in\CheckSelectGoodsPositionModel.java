package com.xyy.wms.pda.model.checkSelectGoodsPosition.in;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.shelf.CheckSelectGoodsPositionBean;
import com.xyy.wms.pda.net.RetrofitCreateHelper;
import com.xyy.wms.pda.bean.req.CheckGoodsPositionReq;

import io.reactivex.Observable;

/**
 * 校验货位
 */
public interface CheckSelectGoodsPositionModel {
    default Observable<BaseResponseBean<CheckSelectGoodsPositionBean>> checkSelectGoodsPosition(CheckGoodsPositionReq req) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).checkSelectGoodsPosition(req.getGetParams())
                .compose(RxHelper.rxSchedulerHelper());
    }
}
