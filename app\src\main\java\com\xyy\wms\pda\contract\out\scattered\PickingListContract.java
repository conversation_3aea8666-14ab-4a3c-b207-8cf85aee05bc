//package com.xyy.wms.pda.contract.out.scattered;
//
//import com.xyy.utilslibrary.base.IBaseActivity;
//import com.xyy.utilslibrary.base.IBaseModel;
//import com.xyy.wms.pda.bean.base.BaseResponseBean;
//import com.xyy.wms.pda.bean.out.PickingTaskListConfirmBean;
//import com.xyy.wms.pda.bean.out.SubmitResult;
//
//import io.reactivex.Observable;
//
///**
// * 拆零拣货 - 拣货列表
// */
//public interface PickingListContract {
//
//    interface IPickingListModel extends IBaseModel {
//        /**
//         * 提交拆零拣货列表
//         *
//         * @param bean
//         * @return
//         */
//        Observable<BaseResponseBean<SubmitResult>> submitPickingList(PickingTaskListConfirmBean bean);
//    }
//
//    interface IPickingListView extends IBaseActivity {
//        /**
//         * 提交拆零拣货列表成功
//         */
//        void submitPickingListSuccess(SubmitResult submitResult);
//
//        /**
//         * 显示网络错误
//         */
//        void showNetworkError();
//    }
//
//}
