package com.xyy.wms.pda.bean.inmanager.post

/**
 * 货位调整-提交
 */
class PositionAdjustmentPostBean {
  var allocationId:Int?=null
  var batchNumber: String? = null//商品批号	string
  var sterilizingBatchNumber: String? = null//灭菌批号	string
  var inGoodsPositionCode: String? = null//移入货位编码	string
  var locationMovementNo: String? = null//调整单号	string
  var movePackNum: String? = null//移库件数	number	库别为整件库
  var moveScatteredNum: String? = null//移库零散数	number	库别为零散或整散合一
  var outGoodsPositionCode: String? = null//移出货位编码	string
  var productCode: String? = null//商品编码	string
  var buildingCode: String? = null//建筑物编码	string
  var channelCode: String? = null//业务类型编码	string
  var orgCode: String? = null//机构码	string
  var ownerCode: String? = null//	业主编码	string
  var storageAreaCode: String? = null//	库区编码	string
  var storageRoomCode: String? = null//库房编码	string
  var warehouseCode: String? = null//仓库编码	string
  var storageTypeCode: String? = null//库别编码
  var produceTime: String? = null//生产日期
  var validDate: String? = null//有效期
}
