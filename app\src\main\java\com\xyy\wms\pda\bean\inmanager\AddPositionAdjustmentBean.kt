package com.xyy.wms.pda.bean.inmanager

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

/**
 * 新增货位调整详情
 */
@Parcelize
data class AddPositionAdjustmentBean(
  var id:Int?=null,
  var batchNumber: String? = null,//批号
  //var sterilizingBatchNumber: String? = null,//灭菌批号
  var commonName: String? = null,
  var inGoodsPositionCode: String? = null,//货位编码
  var manufacturer: String? = null,//厂家名称
  var moveNum: Int = 0,//移动零散数/移动件数
  var convertPackNum: Int? = 0,//折合件数
  var convertScatteredNum: Int? = 0,//折合零散数
  var moveTotalNum: String? = null,//总数量
  var produceTime: String? = null,//生产日期
  var productCode: String? = null,//商品编码
  var productName: String? = null,//商品名称
  var standard: String? = null,//规格
  var storageTypeCode: String? = null,//库别编码
  var storageTypeName: String? = null,//库别名称
  var unit: String? = null,//单位
  var validDate: String? = null,//有效期
  var outGoodsPositionCode: String? = null,//货位编码
  var packageNum: Int? = null,//包装规格
  var ownerName: String? = null,
  //var buildingCode: String? = null,//建筑物编码	string
  var channelCode: String? = null,//业务类型编码	string
  var channelName: String? = null,//  业务类型名称
  var orgCode: String? = null,//机构码	string
  var ownerCode: String? = null,//	业主编码	string
  var storageAreaCode: String? = null,//	库区编码	string
  var storageRoomCode: String? = null,//库房编码	string
  var warehouseCode: String? = null//仓库编码	string
) : Parcelable
