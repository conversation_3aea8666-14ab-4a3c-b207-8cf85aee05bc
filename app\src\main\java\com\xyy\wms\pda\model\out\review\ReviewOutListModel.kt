package com.xyy.wms.pda.model.out.review

import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.out.ReviewOutListBean
import com.xyy.wms.pda.bean.out.ReviewOutOrderBean
import com.xyy.wms.pda.bean.out.outsideReview.Building
import com.xyy.wms.pda.contract.out.review.ReviewOutListContract
import com.xyy.wms.pda.model.ServiceModel
import io.reactivex.Observable

class ReviewOutListModel : ServiceModel(), ReviewOutListContract.IReviewOutListModel {

  override fun getReviewOutList(map: Map<String, String?>): Observable<BaseResponseBean<ReviewOutListBean>> {
    return apiOutManagerService.getReviewOutList(map).compose(RxHelper.rxSchedulerHelper())
  }

  override fun cancelOutReceiveOrder(map: Map<String, String?>): Observable<BaseResponseBean<*>> {
    return apiOutManagerService.cancelOutReceiveOrder(map).compose(RxHelper.rxSchedulerHelper())
  }

  override fun requestReviewTask(map: Map<String, String?>): Observable<BaseResponseBean<ReviewOutOrderBean>> {
    return apiOutManagerService.reviewOutOrder(map).compose(RxHelper.rxSchedulerHelper())
  }

  override fun getAllBuildingCode(): Observable<BaseResponseBean<MutableList<Building>>> {
    return apiOutManagerService.allBuildingCode.compose(RxHelper.rxSchedulerHelper())
  }

  companion object {
    fun newInstance(): ReviewOutListModel {
      return ReviewOutListModel()
    }
  }
}
