package com.xyy.wms.pda.model.instorage.checkaccept

import com.xyy.utilslibrary.base.BaseModel
import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.api.ApiInAcceptService
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckContainerPost
import com.xyy.wms.pda.bean.instorage.checkaccept.TurnDownPost
import com.xyy.wms.pda.contract.instorage.checkaccept.CheckAcceptProductDetailContract
import com.xyy.wms.pda.net.RetrofitCreateHelper
import io.reactivex.Observable

/**
 * Created by XyyMvpPdaTemplate on 03/04/2020 16:11
 */
class CheckAcceptProductDetailModel : BaseModel(), CheckAcceptProductDetailContract.ICheckAcceptProductDetailModel {

    companion object {
        fun newInstance(): CheckAcceptProductDetailModel {
            return CheckAcceptProductDetailModel()
        }
    }

    override fun checkContainer(checkContainerPost: CheckContainerPost): Observable<BaseResponseBean<Int>> {
        return RetrofitCreateHelper.createApi(ApiInAcceptService::class.java).checkContainer(checkContainerPost)
                .compose(RxHelper.rxSchedulerHelper())
    }

    override fun turnDown(turnDownPost: TurnDownPost): Observable<BaseResponseBean<Any>> {
        return RetrofitCreateHelper.createApi(ApiInAcceptService::class.java).turnDown(turnDownPost)
                .compose(RxHelper.rxSchedulerHelper())
    }
}
