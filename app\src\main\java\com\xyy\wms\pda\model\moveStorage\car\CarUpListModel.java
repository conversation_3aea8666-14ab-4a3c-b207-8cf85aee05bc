package com.xyy.wms.pda.model.moveStorage.car;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiMoveStorageService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.common.CancelTaskBean;
import com.xyy.wms.pda.bean.common.MoveStorageFinishedBean;
import com.xyy.wms.pda.bean.common.RunningTaskResult;
import com.xyy.wms.pda.bean.moveStorage.AddTaskCarDownDetailBean;
import com.xyy.wms.pda.contract.movestorage.car.CarUpListContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * 零货下架-列表
 */
public class CarUpListModel implements CarUpListContract.CarUpListModel{

    public static CarUpListModel newInstance() {
        return new CarUpListModel();
    }

    /**
     * 进行中任务
     */
    public Observable<BaseResponseBean<RunningTaskResult>> getRunningTask(){
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).getRunningTask()
            .compose(RxHelper.rxSchedulerHelper());
    }

    /**
     * 添加任务明细
     */
    public Observable<BaseResponseBean<Boolean>> addTaskDetail(AddTaskCarDownDetailBean addTaskDetailBean){
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).addTaskCarDownDetail(addTaskDetailBean)
            .compose(RxHelper.rxSchedulerHelper());
    }
    /**
     * 移除任务
     */
    public Observable<BaseResponseBean<Boolean>> cancelTask(CancelTaskBean cancelTaskBean){
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).canelTask(cancelTaskBean)
            .compose(RxHelper.rxSchedulerHelper());
    }
    /**
     * 完成
     */
    public Observable<BaseResponseBean<Boolean>> setFinished(MoveStorageFinishedBean finishedBean){
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).setFinished(finishedBean)
            .compose(RxHelper.rxSchedulerHelper());
    }
}
