package com.xyy.wms.pda.contract.mine;



import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.mine.WorkloadRankingBean;

import java.util.List;
import java.util.Map;

import io.reactivex.Observable;


public interface WorkloadRankingContract {

    interface IWorkloadRankingModel extends IBaseModel {
        /**
         * 请求工作量排行榜
         */
        Observable<BaseResponseBean<List<WorkloadRankingBean>>> getWorkloadRankingList(Map<String, Object> map);
    }

    interface IWorkloadRankingView extends IBaseActivity {

        void getWorkloadRankingSuccess(List<WorkloadRankingBean> list);

        void showNetWorkError();
    }
}
