package com.xyy.wms.pda.helper;

import android.app.Activity;
import android.content.Intent;

import com.scwang.smartrefresh.layout.constant.RefreshState;
import com.xyy.common.util.ToastUtils;
import com.xyy.utilslibrary.AppManager;
import com.xyy.utilslibrary.base.IBaseView;
import com.xyy.utilslibrary.base.activity.BaseCompatActivity;
import com.xyy.utilslibrary.base.fragment.BaseCompatFragment;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.ui.activity.main.LoginActivity;

import io.reactivex.functions.Consumer;

public abstract class SimpleSuccessConsumer<T extends BaseResponseBean> implements Consumer<T> {

    /**
     * 是否是退出登录
     */
    private boolean isLogout = false;
    private IBaseView iBaseView;


    /**
     * @param iBaseView
     * @param waitMsg   如果为“”或者null，显示等待Dialog为默认提示（如正在加载中...），否则显示等待提示waitMsg的值
     */
    public SimpleSuccessConsumer(IBaseView iBaseView, String waitMsg) {
        this.iBaseView = iBaseView;
        if ("logout".equals(waitMsg)) {
            isLogout = true;
        } else {
            isLogout = false;
            iBaseView.showWaitDialog(waitMsg);
        }
    }

    /**
     * 不显示等待提示Dilaog
     *
     * @param iBaseView
     */
    public SimpleSuccessConsumer(IBaseView iBaseView) {
        this.iBaseView = iBaseView;
    }


    /**
     * fix 当 code 不是 0 时，但是我们也需要作为成功的数据来进行判断的时候
     *
     * @param code
     * @return
     */
    protected boolean isSuccess(int code) {
        return code == 0;
    }

    @Override
    public void accept(T baseBean) throws Exception {
        if (iBaseView == null) return;
        iBaseView.hideWaitDialog();
        if (baseBean == null) {
            iBaseView.showToast("请求无数据");
            return;
        }
        if (isSuccess(baseBean.getCode())) {
            onSuccess(baseBean);
            handlerRefreshLayout(true);
            return;
        }

        if (baseBean.getCode() == 1000 && !isLogout) {
            // Token失效
            if (iBaseView instanceof BaseCompatActivity) {
                BaseCompatActivity ba = (BaseCompatActivity) iBaseView;
                loginAgain(ba);
            } else if (iBaseView instanceof BaseCompatFragment) {
                BaseCompatFragment bf = (BaseCompatFragment) iBaseView;
                loginAgain(bf.getActivity());
            }
            return;
        }

        onFailure(baseBean.getCode());
        onFailure(baseBean.getCode(), baseBean.getMsg());
        handlerRefreshLayout(false);
        if (isShowSystemMsg() && baseBean.getCode() != 0) {
            iBaseView.showToast(baseBean.getMsg());
        }

    }

    /**
     * 是否显示系统提示
     *
     * @return
     */
    public boolean isShowSystemMsg() {
        return true;
    }

    public abstract void onSuccess(T t);

    public void onFailure(int code) {

    }

    public void onFailure(int code, String msg) {

    }

    private void handlerRefreshLayout(boolean isSuccess) {
        if (iBaseView instanceof BaseCompatFragment) {
            BaseCompatFragment bf = (BaseCompatFragment) iBaseView;
            if (bf.refreshLayout != null) {
                if (bf.refreshLayout.getState() == RefreshState.Refreshing) {
                    bf.refreshLayout.finishRefresh(isSuccess);
                } else if (bf.refreshLayout.getState() == RefreshState.Loading) {
                    bf.refreshLayout.finishLoadMore(isSuccess);
                }
            }
        } else if (iBaseView instanceof BaseCompatActivity) {
            BaseCompatActivity ba = (BaseCompatActivity) iBaseView;
            if (ba.refreshLayout != null) {
                if (ba.refreshLayout.getState() == RefreshState.Refreshing) {
                    ba.refreshLayout.finishRefresh(isSuccess);
                } else if (ba.refreshLayout.getState() == RefreshState.Loading) {
                    ba.refreshLayout.finishLoadMore(isSuccess);
                }
            }
        }
    }

    /**
     * 重新登录
     *
     * @param activity
     */
    private void loginAgain(Activity activity) {
        AppManager.getAppManager().finishAllActivity();
        Intent intent = new Intent(activity, LoginActivity.class);
        activity.startActivity(intent);
        ToastUtils.showShortSafe("您的账号已过期，请重新登录");
    }

}
