package com.xyy.wms.pda.model.inmanager.check

import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.inmanager.check.CheckUpBean
import com.xyy.wms.pda.contract.inmanager.check.CheckUpContract.ICheckUpModel
import com.xyy.wms.pda.model.ServiceModel
import io.reactivex.Observable

class CheckUpModel : ServiceModel(), ICheckUpModel {

  override fun checkUp(positionCode: Map<String,String?>): Observable<BaseResponseBean<List<CheckUpBean>>> {
    return apiInManagerService.checkUp(positionCode).compose(RxHelper.rxSchedulerHelper())
  }

  companion object {
    fun newInstance(): CheckUpModel {
      return CheckUpModel()
    }
  }
}
