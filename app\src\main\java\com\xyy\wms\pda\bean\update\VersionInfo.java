package com.xyy.wms.pda.bean.update;

import java.io.Serializable;

/**
 * 版本更新信息
 */
public class VersionInfo implements Serializable {

    /**
     * 更新说明
     */
    private String editionRemark;
    /**
     * 是否强制更新 1--强制更新；2--手动更新
     */
    private int updateType;
    /**
     * 更新链接
     */
    private String updateUrl;
    /**
     * 版本号
     */
    private String editionNo;

    public String getEditionRemark() {
        return editionRemark;
    }

    public void setEditionRemark(String editionRemark) {
        this.editionRemark = editionRemark;
    }

    public int getUpdateType() {
        return updateType;
    }

    public void setUpdateType(int updateType) {
        this.updateType = updateType;
    }

    public String getUpdateUrl() {
        return updateUrl;
    }

    public void setUpdateUrl(String updateUrl) {
        this.updateUrl = updateUrl;
    }

    public String getEditionNo() {
        return editionNo;
    }

    public void setEditionNo(String editionNo) {
        this.editionNo = editionNo;
    }
}