package com.xyy.wms.pda.contract.pinback.wholeShelf;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.pinback.PostShelfListBean;
import com.xyy.wms.pda.bean.pinback.ShelfResult;
import com.xyy.wms.pda.model.pinback.wholeShelf.ProductWholeShelfModel;

import io.reactivex.Observable;

/**
 * 商品整件上架
 */
public interface ProductWholeShelfContract {

    interface IProductWholeShelfModel extends IBaseModel {

        Observable<BaseResponseBean<ShelfResult>> commitWholeShelfList(PostShelfListBean wholeShelfListBean);
    }

    interface IProductWholeShelfView extends IBaseActivity {

        void checkSelectGoodsPositionSuccess(BaseResponseBean baseResponseBean);

        void commitWholeShelfListSuccess(BaseResponseBean<ShelfResult> listBaseResponseBean);

        void commitWholeShelfListFail(BaseResponseBean<ShelfResult> baseResponseBean);
    }
    abstract class ProductWholeShelfPresenter extends BasePresenter<ProductWholeShelfModel, IProductWholeShelfView> {
        public abstract void commitWholeShelfList(PostShelfListBean wholeShelfListBean);
    }

}
