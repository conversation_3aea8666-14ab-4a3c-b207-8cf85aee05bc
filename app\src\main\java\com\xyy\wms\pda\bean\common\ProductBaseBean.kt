package com.xyy.wms.pda.bean.common

import java.io.Serializable

/**
 * 原货位获取逻辑区（result）数据对象
 */
class ProductBaseBean : Serializable {
  var pageNo: Int? = 1
  var pageSize: Int? = 100
  var productCode: String ?= null  //商品名称/编码/助记码
  var approvalNumbers: String ?= null //批准文号
  var startTime: String ?= null //同步开始时间
  var endTime: String ?= null //同步结束时间
  var manufacturer: String ?= null
  var largeCategoryCode: String ?= null //商品大类
  var diving: String ?= null //ABC分类
  var storageAttribute: String ?= null //存放属性
}
