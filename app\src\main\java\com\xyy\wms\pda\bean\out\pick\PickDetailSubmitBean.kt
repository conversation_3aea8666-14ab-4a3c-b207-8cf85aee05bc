package com.xyy.wms.pda.bean.out.pick

/**
 * 拣货明细提交参数
 */
data class PickDetailSubmitBean(
        var batchInspectionCode: String?,// 批拣单号
        var batchNumber: String?,// 批号
        var sterilizingBatchNumber: String?,// 批号
        var buildingCode: String?,// 建筑物编码
        var caseCodeList: List<String>?,// 周转箱格号
        var channelCode: String?,// 业务类型编码
        var number: Int,// 计划总数量
        var orderCode: String?,// 出库单号
        var ownerCode: String?,// 业主编码
        var productCode: String?,// 商品编码
        var productionDate: String?,// 生产日期
        var realPickingNumber: Int,// 实际拣货数量
        var soldOut: String?,// 货位编码
        var storageAreaCode: String?,// 库区编码
        var storageRoomCode: String?,// 库房编码
        var storageTypeCode: String?,// 库别编码
        var validityDate: String?,// 有效期至
        var warehouseCode: String?,// 仓库编码
        var finishedStatus: Int = 0,// 1--表示最后一个完成 0---没完成
        var filllogId: Long = 0,// 补拣日志表id
        var pid: Long = 0,// 任务明细表id
        var flag: Long = 0,// 补拣不能提交异常标识
        var jobNumber: String?, // 员工编号
        var pickType:String?,// 原接口增加字段pickType  pickType=1 货位拣货 pickType=2 单据墙拣货
        var erpOrderCode:String?//销售单号
)
