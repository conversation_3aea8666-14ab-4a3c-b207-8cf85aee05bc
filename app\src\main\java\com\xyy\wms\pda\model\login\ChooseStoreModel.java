package com.xyy.wms.pda.model.login;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.user.UserInfoBean;
import com.xyy.wms.pda.bean.user.WarehouseResult;
import com.xyy.wms.pda.contract.login.ChooseStoreContract;
import com.xyy.wms.pda.model.ServiceModel;
import com.xyy.wms.pda.bean.user.PostUserChooseStoreBean;
import java.util.List;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 12/04/2019 15:51
 */
public class ChooseStoreModel extends ServiceModel implements ChooseStoreContract.IChooseStoreModel {

    public static ChooseStoreModel newInstance() {
        return new ChooseStoreModel();
    }

    @Override
    public Observable<BaseResponseBean<List<WarehouseResult>>> getChooseStoreList() {
        return getApiService().getChooseStoreList().compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<Object>> commitChooseStore(String warehouseCode) {
        return getApiService().commitChooseStoreSuccess(new PostUserChooseStoreBean(warehouseCode)).compose(RxHelper.rxSchedulerHelper());
    }
}
