package com.xyy.wms.pda.bean.inmanager.search

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

/**
 * 移动单
 *
 * @property id String?                 移库单 ID
 * @property createTime String?         移库单创建时间
 * @property inStorageTypeCode String?  入库库别编码
 * @property billState String?          移库单状态：0，初始状态；1，待下架；2，待上架；3，已上架
 * @property inStorageTypeName String?  入库库别名称
 * @property movementNo String?         移库单号
 * @property outStorageTypeCode String? 出库库别编码
 * @property outStorageTypeName String? 出库库别名称
 * @property ownerName String?          业主名称
 * @property buildingName String?       建筑物名称
 * @property productCnt:String?         品种数
 * @constructor
 */
@Parcelize
data class MovementListRequestBean(
    var pageNo: Int? = 1,
    var pageSize: Int? = 10,
    var movementNo: String? = null,
    var billState: Int ?= null,
    var ownerCode: String ?= null
) : Parcelable
