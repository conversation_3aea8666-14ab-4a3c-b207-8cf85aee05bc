package com.xyy.wms.pda.model.instorage.checkaccept

import com.xyy.utilslibrary.base.BaseModel
import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.api.ApiInAcceptService
import com.xyy.wms.pda.api.ApiService
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.*
import com.xyy.wms.pda.bean.productCode.ProductCode
import com.xyy.wms.pda.contract.instorage.checkaccept.CheckAcceptGoodsDetailListContract
import com.xyy.wms.pda.model.productCode.GetProductCodeModel
import com.xyy.wms.pda.net.RetrofitCreateHelper

import io.reactivex.Observable

/**
 * Created by XyyMvpPdaTemplate on 03/04/2020 16:01
 */
class CheckAcceptGoodsDetailListModel : BaseModel(), CheckAcceptGoodsDetailListContract.ICheckAcceptGoodsDetailListModel, GetProductCodeModel {

    override fun getProductBarCode(ownerCode: String?, packageBarCode: String?): Observable<BaseResponseBean<List<ProductCode>>> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).selectProductPda(ownerCode, packageBarCode)
                .compose(RxHelper.rxSchedulerHelper())
    }

    override fun getDetailsByOrderCode(checkOrderCode: String): Observable<BaseResponseBean<CheckAcceptGoodsResult>> {
        return RetrofitCreateHelper.createApi(ApiInAcceptService::class.java).getDetailsByOrderCode(checkOrderCode)
                .compose(RxHelper.rxSchedulerHelper())
    }

    override fun checkOrderSubmit(acceptDetailListPost: AcceptDetailListPost?): Observable<BaseResponseBean<List<AcceptSubmitResult>>> {
        return RetrofitCreateHelper.createApi(ApiInAcceptService::class.java).checkOrderSubmit(acceptDetailListPost)
                .compose(RxHelper.rxSchedulerHelper())
    }

    override fun secondLogin(secondLoginPost: SecondLoginPost): Observable<BaseResponseBean<SecondLoginResult>> {
        return RetrofitCreateHelper.createApi(ApiInAcceptService::class.java).secondLogin(secondLoginPost)
                .compose(RxHelper.rxSchedulerHelper())
    }

    companion object {
        fun newInstance(): CheckAcceptGoodsDetailListModel {
            return CheckAcceptGoodsDetailListModel()
        }
    }
}