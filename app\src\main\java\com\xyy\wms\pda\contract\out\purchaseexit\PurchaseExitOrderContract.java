package com.xyy.wms.pda.contract.out.purchaseexit;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitDoingBean;

import java.util.List;
import java.util.Map;

import io.reactivex.Observable;

/**
 * 购进退出执行
 */
public interface PurchaseExitOrderContract {

    interface IPurchaseExitOrderModel extends IBaseModel {
        Observable<BaseResponseBean<List<PurchaseExitDoingBean>>> queryPerformDocument(Map<String, Object> map);
        Observable<BaseResponseBean> queryPackageBarValidity(Map<String, String> map);
    }

    interface IPurchaseExitOrderView extends IBaseActivity {
        void queryPerformDocumentSuccess(List<PurchaseExitDoingBean> bean);
        void queryPackageBarValiditySuccess(String packageBarCode);
    }

}
