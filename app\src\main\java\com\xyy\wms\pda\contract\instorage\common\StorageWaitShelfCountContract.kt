package com.xyy.wms.pda.contract.instorage.common

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.api.ApiInExitService
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.shelf.CommitShelfResult
import com.xyy.wms.pda.helper.SimpleErrorConsumer
import com.xyy.wms.pda.helper.SimpleSuccessConsumer
import com.xyy.wms.pda.net.RetrofitCreateHelper

import io.reactivex.Observable;

/**
 * 根据上架类型获取待上架任务数
 */
interface StorageWaitShelfCountContract {
    interface IStorageWaitShelfCountModel : IBaseModel {
        fun getStorageWaitShelfCount(shelfType: String): Observable<BaseResponseBean<Int>>
    }

    interface IStorageWaitShelfCountView : IBaseActivity {
        fun getStorageWaitShelfCountSuccess(requestBaseBean: BaseResponseBean<Int>)
    }

    abstract class IScatteredShelfPresenter : BasePresenter<IStorageWaitShelfCountModel, IStorageWaitShelfCountView>() {
        abstract fun getStorageWaitShelfCount(shelfType: String)
    }
}

class StorageWaitShelfCountModel : StorageWaitShelfCountContract.IStorageWaitShelfCountModel {
    override fun getStorageWaitShelfCount(shelfType: String): Observable<BaseResponseBean<Int>> {
        return RetrofitCreateHelper.createApi(ApiInExitService::class.java).getStorageWaitShelfCount(shelfType)
                .compose(RxHelper.rxSchedulerHelper())
    }
}

class ScatteredShelfPresenter : StorageWaitShelfCountContract.IScatteredShelfPresenter() {

    override fun getModel(): StorageWaitShelfCountContract.IStorageWaitShelfCountModel {
        return StorageWaitShelfCountModel()
    }

    override fun getStorageWaitShelfCount(shelfType: String) {
        mRxManager.register(mIModel.getStorageWaitShelfCount(shelfType).subscribe(object : SimpleSuccessConsumer<BaseResponseBean<Int>>(mIView) {
          override fun onSuccess(t: BaseResponseBean<Int>) {
            mIView.getStorageWaitShelfCountSuccess(t)
          }
        }, SimpleErrorConsumer(mIView)))
    }
}
