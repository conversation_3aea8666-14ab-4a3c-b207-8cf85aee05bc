package com.xyy.wms.pda.global

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import com.alibaba.android.arouter.launcher.ARouter
import com.scwang.smartrefresh.layout.SmartRefreshLayout
import com.scwang.smartrefresh.layout.constant.SpinnerStyle
import com.scwang.smartrefresh.layout.footer.ClassicsFooter
import com.scwang.smartrefresh.layout.header.ClassicsHeader
//import com.umeng.commonsdk.UMConfigure
import com.xyy.common.util.Abase
import com.xyy.utilslibrary.global.GlobalApplication
import com.xyy.wms.pda.R
import com.xyy.wms.pda.utils.BuglyUtil
import com.xyy.wms.pda.net.RetrofitCreateHelper

/**
 * 全局的Application
 */
class MyApplication : GlobalApplication() {

  override fun onCreate() {
    super.onCreate()
    Abase.initialize(this)
    // 错误日志收集
    BuglyUtil.init(this)
    initARouter()
    // 初始化刷新样式
    initSmartRefreshLayout()
    // 初始化友盟
    initYouMeng()
    initNetworkMonitor()
  }

  private fun initYouMeng() {
    //初始化组件化基础库, 所有友盟业务SDK都必须调用此初始化接口。
    //建议在宿主App的Application.onCreate函数中调用基础组件库初始化函数。
//    val appKey = if (!BuildConfig.DEBUG && BuildConfig.FLAVOR == "prod") {
//      "5fe2e15bd1c1ad39854e4ddd"
//    } else {
//      // 打开统计SDK调试模式
//      UMConfigure.setLogEnabled(false)
//      "5fe2f99e1283d52b40b34dc8"
//    }
//    UMConfigure.init(this, appKey, "Umeng", UMConfigure.DEVICE_TYPE_PHONE, "");
  }

  /**
   * 初始化 ARouter
   */
  private fun initARouter() {
//    if (BuildConfig.DEBUG) {           // 这两行必须写在init之前，否则这些配置在init过程中将无效
//      ARouter.openLog() // 打印日志
//      ARouter.openDebug() // 开启调试模式(如果在InstantRun模式下运行，必须开启调试模式！线上版本需要关闭,否则有安全风险)
//    }
    ARouter.init(this) // 尽可能早，推荐在Application中初始化
  }

  override fun attachBaseContext(base: Context?) {
    super.attachBaseContext(base)
  }

  companion object {
    /**
     * 设置刷新 默认的header和footer
     */
    private fun initSmartRefreshLayout() {
      //设置全局的Header构建器
      SmartRefreshLayout.setDefaultRefreshHeaderCreator { context, layout ->
//    CustomRefreshHeader header = new CustomRefreshHeader(context);
//                return header;
        ClassicsHeader(context) //指定为经典Header，默认是 贝塞尔雷达Header
      }
      //设置全局的Footer构建器
      SmartRefreshLayout.setDefaultRefreshFooterCreator { context, layout ->
        layout.setEnableLoadMoreWhenContentNotFull(true) //内容不满一页时候启用加载更多
        val footer = ClassicsFooter(context)
        footer.setBackgroundResource(R.color.white)
        footer.spinnerStyle = SpinnerStyle.Scale //设置为拉伸模式
        footer //指定为经典Footer，默认是 BallPulseFooter
      }
    }
  }

  private fun initNetworkMonitor() {
    // 注册屏幕状态广播接收器
    val filter = IntentFilter().apply {
      addAction(Intent.ACTION_SCREEN_ON)
      addAction(Intent.ACTION_SCREEN_OFF)
    }
    
    registerReceiver(object : BroadcastReceiver() {
      override fun onReceive(context: Context?, intent: Intent?) {
        when (intent?.action) {
          Intent.ACTION_SCREEN_ON -> {
            // 屏幕点亮时重置网络连接
            RetrofitCreateHelper.resetOkHttpClient()
          }
        }
      }
    }, filter)
  }

}
