package com.xyy.wms.pda.contract.out.purchaseexit;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitReViewPrivUser;

import java.util.List;

import io.reactivex.Observable;

/**
 * Created by lwj on 2020-02-24.
 * <EMAIL>
 * 购进退出复核单-商品复核选择复核员2
 */
public interface PurchaseExitElectronicReViewPrivUserContract {


    interface IPurchaseExitElectronicReViewPrivUserModel extends IBaseModel {


        Observable<BaseResponseBean<List<PurchaseExitReViewPrivUser>>> selectListSysPrivUser(String userName);


    }


    interface IPurchaseExitElectronicReViewPrivUserView extends IBaseActivity {


        void selectListSysPrivUserSuccess(BaseResponseBean<List<PurchaseExitReViewPrivUser>> bean);
    }

    abstract class IPurchaseExitElectronicReViewPrivUserPresenter extends BasePresenter<IPurchaseExitElectronicReViewPrivUserModel, IPurchaseExitElectronicReViewPrivUserView> {


        public abstract void selectListSysPrivUser(String userName);

    }


}
