package com.xyy.wms.pda.contract.instorage.container

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.api.ApiInExitService
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.container.ScanContainerQueryResult
import com.xyy.wms.pda.helper.SimpleErrorConsumer
import com.xyy.wms.pda.helper.SimpleSuccessConsumer
import com.xyy.wms.pda.net.RetrofitCreateHelper
import io.reactivex.Observable

/**
 * Created by lwj on 2020/4/28
 * <EMAIL>
 */


interface IScanContainerModel : IBaseModel {

    fun queryContainerStatus(containerCode: String): Observable<BaseResponseBean<ScanContainerQueryResult>>

}

interface IScanContainerView : IBaseActivity {
    fun queryContainerStatusSuccess(result: ScanContainerQueryResult)

}

class ScanContainerModel : IScanContainerModel {
    override fun queryContainerStatus(containerCode: String): Observable<BaseResponseBean<ScanContainerQueryResult>> {
        return RetrofitCreateHelper.createApi(ApiInExitService::class.java).queryContainerStatus(containerCode).compose(RxHelper.rxSchedulerHelper())
    }
}

abstract class IScanContainerPresenter : BasePresenter<IScanContainerModel, IScanContainerView>() {

    abstract fun queryContainerStatus(containerCode: String)


}

class ScanContainerPresenter : IScanContainerPresenter() {

    override fun queryContainerStatus(containerCode: String) {
        if (mIView == null || mIModel == null) return
        mRxManager.register(mIModel.queryContainerStatus(containerCode).subscribe(
                object : SimpleSuccessConsumer<BaseResponseBean<ScanContainerQueryResult>>(mIView, "加载中...") {
                    override fun onSuccess(bean: BaseResponseBean<ScanContainerQueryResult>) {
                        mIView.queryContainerStatusSuccess(bean.result)
                    }
                }, SimpleErrorConsumer(mIView)))
    }

    override fun getModel(): ScanContainerModel {
        return ScanContainerModel()
    }
}
