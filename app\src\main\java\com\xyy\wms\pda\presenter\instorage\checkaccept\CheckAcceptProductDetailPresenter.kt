package com.xyy.wms.pda.presenter.instorage.checkaccept

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckContainerPost
import com.xyy.wms.pda.bean.instorage.checkaccept.TurnDownPost
import com.xyy.wms.pda.contract.instorage.checkaccept.CheckAcceptProductDetailContract
import com.xyy.wms.pda.helper.SimpleErrorConsumer
import com.xyy.wms.pda.helper.SimpleSuccessConsumer
import com.xyy.wms.pda.model.instorage.checkaccept.CheckAcceptProductDetailModel

/**
 * Created by XyyMvpPdaTemplate on 03/04/2020 16:11
 */
class CheckAcceptProductDetailPresenter : BasePresenter<CheckAcceptProductDetailContract.ICheckAcceptProductDetailModel, CheckAcceptProductDetailContract.ICheckAcceptProductDetailView>() {

    override fun getModel(): CheckAcceptProductDetailModel {
        return CheckAcceptProductDetailModel.newInstance()
    }

    companion object {
        fun newInstance(): CheckAcceptProductDetailPresenter {
            return CheckAcceptProductDetailPresenter()
        }
    }

    fun turnDown(turnDownPost: TurnDownPost) {
        mRxManager.register(mIModel.turnDown(turnDownPost).subscribe(object : SimpleSuccessConsumer<BaseResponseBean<Any>>(mIView) {
            override fun onSuccess(baseResponseBean: BaseResponseBean<Any>) {
                mIView.turnDownSuccess(baseResponseBean)
            }
        }, SimpleErrorConsumer(mIView)))
    }

    fun checkContainer(checkContainerPost: CheckContainerPost) {
        mRxManager.register(mIModel.checkContainer(checkContainerPost).subscribe(object : SimpleSuccessConsumer<BaseResponseBean<Int>>(mIView) {
            override fun onSuccess(baseResponseBean: BaseResponseBean<Int>) {
                mIView.checkContainerSuccess(baseResponseBean)
            }
        }, SimpleErrorConsumer(mIView)))
    }

}
