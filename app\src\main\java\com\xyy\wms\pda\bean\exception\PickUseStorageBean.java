package com.xyy.wms.pda.bean.exception;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * created by  liangxing  on 2019-12-18.
 */
public class PickUseStorageBean implements Serializable, Cloneable {

    private String id;

    private String passBoxCode;//容器编号

    private String orderCode;

    private String productCode;

    private String allocationCode;

    private Integer commitUserId;

    private String commitNode;

    private Integer dealMode;

    private String exceptionTaskCode;

    // 异常 类型
    private Integer exceptionCause;

    //差异数量
    private Integer exceptionNumber;

    private String batchNumber;

    // 显示货位
    private String goodsAllocation;

    private String reviewStage;

    private String remark;

    private Integer isAskFor;

    private String status;

    private String jobNumber;
    private Integer yn;
    private String productName;//商品名称
    private String specifications;//规格
    private String packingUnit;//包装单位
    private String commonName;//通用名
    private String manufacturer;//生产厂家
    private String ownerNumber;//业主编号
    private List<Integer> statusList;
    private String ownerCode;//业主编号'
    private String commitUserName;
    private String exceptionType;// 3整件 ，1零散，2内复核
    private String tagCode;//标签条码
    private BigDecimal pieceNumber;//件包装数量
    private String productionTime;//生产日期
    private String produceTime; //生产日期-新
    private String valiperiodValidity;//有效期至
    private String productOrigin;//产地

    private String dealUserName; //处理人

    private String createUser;
    private String updateUser;

    private int pickNumber=0;//补拣数量
    private int rushNumber;//冲红数量
    private int rushCause;//冲红原因

    private String orgCode;//机构编码',
    private String channelCode;//'业务类型编号',
    private String warehouseCode;//'仓库编码',
    private String buildingCode;//建筑物编码',
    private String storageAreaCode;//库区编码
    private String storageRoomCode;//库房编码
    private String storageTypeCode;//库别编码

    private BigDecimal amountUse;//可用库存（可出库数量）

    private int occupyAmount;//占用数量(补拣数量)

    private String originalHw;//是否原货位。true：是，false：否

    //编辑的 补拣 数量
    private int pickUpNumber = 0;

    private int storageId = 0;

    private String validDate;

    private String sterilizingBatchNumber;

    public String getSterilizingBatchNumber() {
        return sterilizingBatchNumber;
    }
    public void setSterilizingBatchNumber(String sterilizingBatchNumber){
        this.sterilizingBatchNumber = sterilizingBatchNumber;
    }

    public void setValidDate(String validDate){
        this.validDate = validDate;
    }
    public String getValidDate() {
        return validDate;
    }
    public String getProduceTime() {
        return produceTime;
    }
    public void setProduceTime(String produceTime){
        this.produceTime = produceTime;
    }

    public void setStorageId(int storageId) {
        this.storageId = storageId;
    }

    public int getStorageId() {
        return storageId;
    }

    public int getPickUpNumber() {
        return pickUpNumber;
    }

    public void setPickUpNumber(int pickUpNumber) {
        this.pickUpNumber = pickUpNumber;
    }

    public String getProductionTime() {
        return productionTime;
    }

    public void setProductionTime(String productionTime) {
        this.productionTime = productionTime;
    }

    public String getValiperiodValidity() {
        return valiperiodValidity;
    }

    public void setValiperiodValidity(String valiperiodValidity) {
        this.valiperiodValidity = valiperiodValidity;
    }

    public String getProductOrigin() {
        return productOrigin;
    }

    public void setProductOrigin(String productOrigin) {
        this.productOrigin = productOrigin;
    }

    public String getOriginalHw() {
        return originalHw;
    }

    public void setOriginalHw(String originalHw) {
        this.originalHw = originalHw;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getBuildingCode() {
        return buildingCode;
    }

    public void setBuildingCode(String buildingCode) {
        this.buildingCode = buildingCode;
    }

    public String getStorageAreaCode() {
        return storageAreaCode;
    }

    public void setStorageAreaCode(String storageAreaCode) {
        this.storageAreaCode = storageAreaCode;
    }

    public String getStorageRoomCode() {
        return storageRoomCode;
    }

    public void setStorageRoomCode(String storageRoomCode) {
        this.storageRoomCode = storageRoomCode;
    }

    public String getStorageTypeCode() {
        return storageTypeCode;
    }

    public void setStorageTypeCode(String storageTypeCode) {
        this.storageTypeCode = storageTypeCode;
    }

    public BigDecimal getAmountUse() {
        return amountUse;
    }

    public void setAmountUse(BigDecimal amountUse) {
        this.amountUse = amountUse;
    }

    public int getOccupyAmount() {
        return occupyAmount;
    }

    public void setOccupyAmount(int occupyAmount) {
        this.occupyAmount = occupyAmount;
    }

    public int getPickNumber() {
        return pickNumber;
    }

    public void setPickNumber(int pickNumber) {
        this.pickNumber = pickNumber;
    }

    public int getRushNumber() {
        return rushNumber;
    }

    public void setRushNumber(int rushNumber) {
        this.rushNumber = rushNumber;
    }

    public int getRushCause() {
        return rushCause;
    }

    public void setRushCause(int rushCause) {
        this.rushCause = rushCause;
    }

    public Integer getDealMode() {
        return dealMode;
    }

    public void setDealMode(Integer dealMode) {
        this.dealMode = dealMode;
    }



    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getDealUserName() {
        return dealUserName;
    }

    public void setDealUserName(String dealUserName) {
        this.dealUserName = dealUserName;
    }

    public BigDecimal getPieceNumber() {
        return pieceNumber;
    }

    public void setPieceNumber(BigDecimal pieceNumber) {
        this.pieceNumber = pieceNumber;
    }

    public String getTagCode() {
        return tagCode;
    }

    public void setTagCode(String tagCode) {
        this.tagCode = tagCode;
    }

    public String getExceptionType() {
        return exceptionType;
    }

    public void setExceptionType(String exceptionType) {
        this.exceptionType = exceptionType;
    }

    public String getCommitUserName() {
        return commitUserName;
    }

    public void setCommitUserName(String commitUserName) {
        this.commitUserName = commitUserName;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public List<Integer> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getPackingUnit() {
        return packingUnit;
    }

    public void setPackingUnit(String packingUnit) {
        this.packingUnit = packingUnit;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPassBoxCode() {
        return passBoxCode;
    }

    public void setPassBoxCode(String passBoxCode) {
        this.passBoxCode = passBoxCode;
    }

    public String getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getAllocationCode() {
        return allocationCode;
    }

    public void setAllocationCode(String allocationCode) {
        this.allocationCode = allocationCode;
    }

    public Integer getCommitUserId() {
        return commitUserId;
    }

    public void setCommitUserId(Integer commitUserId) {
        this.commitUserId = commitUserId;
    }

    public String getCommitNode() {
        return commitNode;
    }

    public void setCommitNode(String commitNode) {
        this.commitNode = commitNode;
    }

    public String getExceptionTaskCode() {
        return exceptionTaskCode;
    }

    public void setExceptionTaskCode(String exceptionTaskCode) {
        this.exceptionTaskCode = exceptionTaskCode;
    }

    public Integer getExceptionCause() {
        return exceptionCause;
    }

    public void setExceptionCause(Integer exceptionCause) {
        this.exceptionCause = exceptionCause;
    }

    public Integer getExceptionNumber() {
        return exceptionNumber;
    }

    public void setExceptionNumber(Integer exceptionNumber) {
        this.exceptionNumber = exceptionNumber;
    }

    public String getBatchNumber() {
        return batchNumber;
    }

    public void setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
    }

    public String getGoodsAllocation() {
        return goodsAllocation;
    }

    public void setGoodsAllocation(String goodsAllocation) {
        this.goodsAllocation = goodsAllocation;
    }

    public String getReviewStage() {
        return reviewStage;
    }

    public void setReviewStage(String reviewStage) {
        this.reviewStage = reviewStage;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getIsAskFor() {
        return isAskFor;
    }

    public void setIsAskFor(Integer isAskFor) {
        this.isAskFor = isAskFor;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public Integer getYn() {
        return yn;
    }

    public void setYn(Integer yn) {
        this.yn = yn;
    }

    public String getOwnerNumber() {
        return ownerNumber;
    }

    public void setOwnerNumber(String ownerNumber) {
        this.ownerNumber = ownerNumber;
    }

    @Override
    public PickUseStorageBean clone() {
        PickUseStorageBean pickUseStorageBean = null;
        try {
            pickUseStorageBean = (PickUseStorageBean) super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return pickUseStorageBean;
    }
}
