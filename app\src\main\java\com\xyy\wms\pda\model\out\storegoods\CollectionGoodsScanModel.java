package com.xyy.wms.pda.model.out.storegoods;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.out.storegoods.StoreGoodsInfo;
import com.xyy.wms.pda.bean.out.storegoods.StoreGoodsPost;
import com.xyy.wms.pda.contract.out.storegoods.CollectionGoodsScanContract;
import com.xyy.wms.pda.model.ServiceModel;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 11/18/2019 14:16
 */
public class CollectionGoodsScanModel extends ServiceModel implements CollectionGoodsScanContract.ICollectionGoodsScanModel {

    public static CollectionGoodsScanModel newInstance() {
        return new CollectionGoodsScanModel();
    }

    @Override
    public Observable<BaseResponseBean<StoreGoodsInfo>> getStoreGoodsInfo(String tagCode) {
        return getApiOutManagerService().getStoreGoodsInfo(tagCode).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean> storeGoodsConfirmCommit(StoreGoodsPost storeGoodsPost) {
        return getApiOutManagerService().storeGoodsConfirmCommit(storeGoodsPost).compose(RxHelper.rxSchedulerHelper());
    }
}
