package com.xyy.wms.pda.contract.out.scattered


import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.out.pick.GoodsStockBean
import io.reactivex.Observable

interface GoodsStockContract {

    interface IGoodsStockModel : IBaseModel {
        fun getGoodsStock(map: Map<String, Any?>): Observable<BaseResponseBean<List<GoodsStockBean>>>
    }

    interface IGoodsStockView : IBaseActivity {
        fun getGoodsStockSuccess(bean: List<GoodsStockBean>)
    }
}
