package com.xyy.wms.pda.model.inmanager.check;


import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.inmanager.check.CheckTaskListBean;
import com.xyy.wms.pda.contract.inmanager.check.CheckTaskListContract;
import com.xyy.wms.pda.model.ServiceModel;

import io.reactivex.Observable;

public class CheckListModel extends ServiceModel implements CheckTaskListContract.ICheckTaskListModel {

    public static CheckListModel newInstance() {
        return new CheckListModel();
    }

    @Override
    public Observable<BaseResponseBean<CheckTaskListBean>> getCheckTask(String checkPlanType) {
        return getApiInManagerService().getCheckTask(checkPlanType).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean> taskComplete(String checkTaskType, String checkTaskNo) {
        return getApiInManagerService().taskComplete(checkTaskType,checkTaskNo).compose(RxHelper.rxSchedulerHelper());
    }
}
