package com.xyy.wms.pda.model.inmanager.check;


import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.inmanager.check.CheckTaskNumBean;
import com.xyy.wms.pda.contract.inmanager.check.CheckTaskContract;
import com.xyy.wms.pda.model.ServiceModel;

import io.reactivex.Observable;

public class CheckTaskModel extends ServiceModel implements CheckTaskContract.ICheckPersenterModel {
    public static CheckTaskModel newInstance() {
        return new CheckTaskModel();
    }

    @Override
    public Observable<BaseResponseBean<CheckTaskNumBean>> selectCheckTaskNums() {
        return getApiInManagerService().selectCheckTaskNums().compose(RxHelper.rxSchedulerHelper());
    }
}
