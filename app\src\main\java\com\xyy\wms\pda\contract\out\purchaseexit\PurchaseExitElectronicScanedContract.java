package com.xyy.wms.pda.contract.out.purchaseexit;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseFragment;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseCodeScannedResultBean;

import java.util.List;

import io.reactivex.Observable;

/**
 * Created by lwj on 2020-02-24.
 * <EMAIL>
 * 电子监管码已扫描
 */
public interface PurchaseExitElectronicScanedContract {


    interface IPurchaseExitElectronicScannedModel extends IBaseModel {

        Observable<BaseResponseBean<List<PurchaseCodeScannedResultBean>>> queryDrugregulatorycode(
                String lineNumber,
                String pickUpOrder);

        Observable<BaseResponseBean> deleteDrugregulatorycode(
                String lineNumber,
                String pickUpOrder,
                String regulatoryCode
        );
    }


    interface IPurchaseExitElectronicScannedView extends IBaseFragment {

        void queryDrugregulatorycodeSuccess(BaseResponseBean<List<PurchaseCodeScannedResultBean>> bean);

        void deleteDrugregulatorycodeSuccess(BaseResponseBean bean);

    }

    abstract class IPurchaseExitElectronicScannedPresenter extends BasePresenter<IPurchaseExitElectronicScannedModel, IPurchaseExitElectronicScannedView> {

        /**
         * 电子监管码扫描单
         *
         * @param pickUpOrder 单据编号
         */
        public abstract void queryDrugregulatorycode(
                String lineNumber,
                String pickUpOrder);

        public abstract void deleteDrugregulatorycode(
                String lineNumber,
                String pickUpOrder,
                String regulatoryCode
        );
    }


}
