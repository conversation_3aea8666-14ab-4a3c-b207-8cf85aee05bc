package com.xyy.wms.pda.bean.instorage.checkaccept

import java.io.Serializable

/**
 * 验收单商品列表
 * <AUTHOR>
 * @Description
 * @Date 2022/5/16
 */
data class WaitCheckDetailListBean (
    var productCode : String,                   // 商品编码
    var productName : String,                   // 商品名称
    var manufacturer : String,                  // 生产厂家
    var specifications : String,                // 商品规格
    var productPackingBigNumber : Int,          // 件包装数量
    var packingUnit : String,                   // 包装单位
    var productBatchCode : String,              // 商品批号
    var productManufactureDate : String,        // 生产日期
    var productValidDate : String,              // 有效期至
    var productCountBig : String,               // 件数
    var productCountScatter : String,           // 零散数
    var productCountSmall : String,             // 数量
    var ownerCode : String,                     // 业主编号
    var buildingCode : String,                  // 建筑物编号
    var channelCode : String,                   // 渠道编号
    var sterilizingBatchNumber : String,        // 灭菌批号
    var productPackingMiddleNumber : Int       // 中包装数量
) : Serializable
