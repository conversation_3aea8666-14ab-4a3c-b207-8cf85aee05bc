package com.xyy.wms.pda.net.req

import okhttp3.MediaType
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * Created by lwj on 2020-03-26.
 * <EMAIL>
 */
abstract class JsonReqBody : IReqBody {

    override fun buildMediaType(): MediaType {
        return IReqBody.JSON_MEDIA_TYPE
    }


    protected open fun getParams(): String {
        return IReqBody.gson.toJson(this)
    }

    override fun buildReqBody(): RequestBody {
        return getParams().toRequestBody(buildMediaType())
    }
}