package com.xyy.wms.pda.bean.instorage.checkaccept;

import com.xyy.wms.pda.utils.PickerDialogManager;

import java.io.Serializable;
import java.util.HashMap;

/**
 * Created by lwj on 2020-03-20.
 * <EMAIL>
 * 验收单拆分
 */
public class AcceptOrderSpiltItem implements Serializable {

    // 类型  （件、零散、整散合一）
    public String acceptType;

    // 验收的件包装数量
    public int acceptPackingNum = 0;

    // 验收的零散数量
    public int acceptScatteredNum = 0;

    // 验收的数量
    public int acceptNum = 0;

    // 容器号
    public String containerCode;

    // 验收结论
    public PickerDialogManager.SelectItemModel<AcceptResult> acceptResult = new PickerDialogManager.SelectItemModel<>("", new AcceptResult(NONE_ACCEPT_RESULT, "", "", -1, new HashMap<Integer, String>())); // 默认未选择

    public String unqualified = ""; //不合格事项

    public String acceptNote = "";// 验收备注
    public String productManufactureDate = "";// 生产日期
    public String productValidDate = "";// 有效期
    public String productBatchCode = "";// 批号



    // 验收状态  1. 验收完成、2 未验收
    public int acceptStatus = UNACCEPT_STATUS;

    public static final int NONE_ACCEPT_RESULT = Integer.MIN_VALUE;
    public static final int QUALIFIED_ACCEPT_RESULT = 1;
    public static final int UNQUALIFIED_ACCEPT_RESULT = 4;
    public static final int REFUSE_ACCEPT_RESULT = 2;
    public static final int WAIT_ACCEPT_RESULT = 3;


    public static final String ZJK_ACCEPT_TYPE = "ZJK";
    public static final String LHK_ACCEPT_TYPE = "LHK";
    public static final String ZSHY_ACCEPT_TYPE = "ZSHY";


    public static final int ACCEPT_STATUS = 1;
    public static final int UNACCEPT_STATUS = 2;
    public static final int CONTAINER_USED = 3;
    public static final int IN_QUALIFIED = 1; // 入合格库
    public static final int REJECTED = 2; // 拒收
    public static final int REVIEW = 3; // 复查
    public static final int IN_NOT_QUALIFIED = 4; // 入不合格库
    public static final int IN_RETURN = 5; // 入退货库
    public int typeTag;
}
