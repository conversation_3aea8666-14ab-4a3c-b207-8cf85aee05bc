package com.xyy.wms.pda.bean.purchaseexit

import java.io.Serializable

/**
 * created by  liangxing  on 2020-01-14.
 */
class PurchaseExitDetail : Serializable, Cloneable {

    var productCode: String? = null // 商品编号
    var productName: String? = null // 商品名称
    var commonName: String? = null  // 通用名称
    var manufacturer: String? = null// 厂家名称
    var packingUnit: String? = null // 单位（规格）
    var lineNumber: Int = 0         // 行号（必传）
    var refundScatteredCount: Int = 0     // 零散退出数量
    var refundWholeCount: Int = 0         // 整件退出数量
    var actualScatteredCount: Int = 0     // 实际零散退出数量（必传）
    var actualWholeCount: Int = 0         // 实际整件退出数量（必传）
    var actualRefundCount: Int = 0        // 实际退货总数（必传）
    var largePackingNumber: String? = null // 件包装规格
    var middlePackingNumber: String? = null// 中包装规格
    var shelfLocationCode: String? = null  // 退出货位
    var productProduceDate: String? = null // 生产日期
    var productValidDate: String? = null   // 有效期至
    var refundOrderCode: String? = null    // 采购退出单编号（必传）
    var specifications: String? = null  // 规格
    var refundOrderStatus: Int = 0   // 购退状态 4.已退出
    var pickUpOrder: String? = null  // 拣货单号
    var channelName: String? = null  // 业务类型

    var supplierName: String? = null  // 供应商名称
    var productBatchCode: String? = null  // 商品批号
    var sterilizingBatchNumber: String? = null  // 灭菌批号
    var shelfGoodsAmount: String? = null  // 货位库存

    public override fun clone(): PurchaseExitDetail {
        var purchaseExitDetail: PurchaseExitDetail? = null
        try {
            purchaseExitDetail = super.clone() as PurchaseExitDetail
        } catch (e: CloneNotSupportedException) {
            e.printStackTrace()
        }
        return purchaseExitDetail!!
    }
}