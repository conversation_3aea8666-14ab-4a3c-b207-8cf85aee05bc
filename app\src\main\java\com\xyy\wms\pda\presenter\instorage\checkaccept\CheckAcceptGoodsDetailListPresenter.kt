package com.xyy.wms.pda.presenter.instorage.checkaccept

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.*
import com.xyy.wms.pda.bean.productCode.ProductCode
import com.xyy.wms.pda.helper.SimpleErrorConsumer
import com.xyy.wms.pda.helper.SimpleSuccessConsumer
import com.xyy.wms.pda.model.instorage.checkaccept.CheckAcceptGoodsDetailListModel
import com.xyy.wms.pda.presenter.productCode.GetProductCodePresenter
import com.xyy.wms.pda.ui.activity.instorage.checkaccept.CheckAcceptGoodsListActivity

/**
 * Created by XyyMvpPdaTemplate on 03/04/2020 16:01
 */
class CheckAcceptGoodsDetailListPresenter : BasePresenter<CheckAcceptGoodsDetailListModel, CheckAcceptGoodsListActivity>(), GetProductCodePresenter {

    override fun getModel(): CheckAcceptGoodsDetailListModel {
        return CheckAcceptGoodsDetailListModel.newInstance()
    }

    companion object {
        fun newInstance(): CheckAcceptGoodsDetailListPresenter {
            return CheckAcceptGoodsDetailListPresenter()
        }
    }

    fun getDetailsByOrderCode(checkOrderCode: String) {
        mRxManager.register(mIModel.getDetailsByOrderCode(checkOrderCode).subscribe(object : SimpleSuccessConsumer<BaseResponseBean<CheckAcceptGoodsResult>>(mIView) {
            override fun onSuccess(baseResponseBean: BaseResponseBean<CheckAcceptGoodsResult>) {
                mIView.getDetailsByOrderCodeSuccess(baseResponseBean.result)
            }
        }, SimpleErrorConsumer(mIView)))
    }

    fun checkOrderSubmit(acceptDetailListPost: AcceptDetailListPost?) {
        mRxManager.register(mIModel.checkOrderSubmit(acceptDetailListPost).subscribe(object : SimpleSuccessConsumer<BaseResponseBean<List<AcceptSubmitResult>>>(mIView) {
            override fun onSuccess(baseResponseBean: BaseResponseBean<List<AcceptSubmitResult>>) {
                if (baseResponseBean.isSuccess) {
                    mIView.checkOrderSubmitSuccess()
                } else if (baseResponseBean.code == 1 ) { // 容器号已占用
                    mIView.checkOrderSubmitFailure(baseResponseBean.result,baseResponseBean.msg)
                }
            }

            override fun isSuccess(code: Int): Boolean {
                return code == 0 || code == 1
            }
        }, SimpleErrorConsumer(mIView)))


    }

    override fun getProductBarCode(ownerCode: String, packageBarCode: String) {
        mRxManager.register(mIModel.getProductBarCode(ownerCode, packageBarCode).subscribe(object : SimpleSuccessConsumer<BaseResponseBean<List<ProductCode>>>(mIView) {
            override fun onSuccess(baseResponseBean: BaseResponseBean<List<ProductCode>>) {
                mIView.getProductCodeSuccess(baseResponseBean)
            }
        }, SimpleErrorConsumer(mIView)))
    }


    /**
     * 二次登陆确认
     */
    fun secondLogin(secondLoginPost: SecondLoginPost, postData: AcceptDetailListPost, hasReject: Boolean) {
        mRxManager.register(mIModel.secondLogin(secondLoginPost).subscribe(object : SimpleSuccessConsumer<BaseResponseBean<SecondLoginResult>>(mIView) {
            override fun onSuccess(baseResponseBean: BaseResponseBean<SecondLoginResult>) {
                mIView.secondLoginSuccess(baseResponseBean, postData, hasReject)
            }

            override fun isSuccess(code: Int): Boolean {
                return code == 1 || code == 0
            }
        }, SimpleErrorConsumer(mIView)))
    }

}
