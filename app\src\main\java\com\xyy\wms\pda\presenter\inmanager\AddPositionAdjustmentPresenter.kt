package com.xyy.wms.pda.presenter.inmanager

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.inmanager.AddPositionAdjustmentBean
import com.xyy.wms.pda.contract.inmanager.AddPositionAdjustmentContract.IAddPositionAdjustmentModel
import com.xyy.wms.pda.contract.inmanager.AddPositionAdjustmentContract.IAddPositionAdjustmentView
import com.xyy.wms.pda.helper.SimpleErrorConsumer
import com.xyy.wms.pda.helper.SimpleSuccessConsumer
import com.xyy.wms.pda.model.inmanager.AddPositionAdjustmentModel

class AddPositionAdjustmentPresenter : BasePresenter<IAddPositionAdjustmentModel, IAddPositionAdjustmentView>() {

  fun getAddPositionAdjustmentList(map: Map<String, String>) {
    if (mIView == null || mIModel == null) return
    mRxManager.register(mIModel!!.getGoodsInfoByLocationCode(map).subscribe(
      object : SimpleSuccessConsumer<BaseResponseBean<List<AddPositionAdjustmentBean>>>(mIView) {
        override fun onSuccess(listBaseResponseBean: BaseResponseBean<List<AddPositionAdjustmentBean>>) {
          if (mIView == null) {
            return
          }
          mIView!!.getAddPositionAdjustmentListSuccess(listBaseResponseBean.result)
        }
      }, object : SimpleErrorConsumer(mIView) {
      override fun onError(throwable: Throwable, msg: String) {
        super.onError(throwable, msg)
        if (mIView == null) return
        mIView!!.showEmpty()
      }
    }))
  }

  fun getLocationMovementNo() {
    if (mIView == null || mIModel == null) return
    mRxManager.register(mIModel!!.getLocationMovementNo().subscribe(
      object : SimpleSuccessConsumer<BaseResponseBean<String>>(mIView) {
        override fun onSuccess(bean: BaseResponseBean<String>) {
          if (mIView == null) {
            return
          }
          mIView!!.getLocationMovementNoSuccess(bean.result)
        }
      }, SimpleErrorConsumer(mIView)))
  }

  override fun getModel(): IAddPositionAdjustmentModel {
    return AddPositionAdjustmentModel.newInstance()
  }

  companion object {
    @JvmStatic
    fun newInstance(): AddPositionAdjustmentPresenter {
      return AddPositionAdjustmentPresenter()
    }
  }
}
