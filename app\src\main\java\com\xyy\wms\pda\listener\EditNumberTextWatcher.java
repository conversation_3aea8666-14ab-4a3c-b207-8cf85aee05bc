package com.xyy.wms.pda.listener;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.widget.EditText;

/**
 * Created by lx on 2018/11/19 15
 * E-Mail：<EMAIL>
 */
public class EditNumberTextWatcher implements TextWatcher {

    private EditText editText;

    public EditNumberTextWatcher(EditText editText) {
        this.editText = editText;
    }

    @Override
    public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

    }

    @Override
    public void afterTextChanged(Editable editable) {
        judgeNumber(editable,editText);
    }

    @Override
    public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

    }

    /**
     * 金额输入框中的内容限制（最大：小数点前五位，小数点后2位）
     *
     * @param edt
     */
    private   void judgeNumber(Editable edt, EditText editText) {

        String temp = edt.toString();
        if(!TextUtils.isEmpty(temp)){
            int length=temp.length();
            if(length>6){
                edt.delete(length-1, length);//删除光标前的字符
                return;
            }
        }
    }

}
