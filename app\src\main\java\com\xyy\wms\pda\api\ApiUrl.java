package com.xyy.wms.pda.api;

import android.text.TextUtils;

import com.meituan.android.walle.WalleChannelReader;
import com.xyy.utilslibrary.global.GlobalApplication;
import com.xyy.wms.pda.BuildConfig;
import com.xyy.wms.pda.utils.SharedPrefManager;

public class ApiUrl {
    /**
     * 基础接口
     */
    public static String API_URL = getApiUrl();
    public static final String BASE_URL = API_URL + "/";

    private static String getApiUrl(){
        String hostUrl = SharedPrefManager.getInstance().getHostUrl();
        if(BuildConfig.DEBUG && !TextUtils.isEmpty(hostUrl)){
            return hostUrl;
        }
        else{
            String channel = WalleChannelReader.getChannel(GlobalApplication.getContext());
            hostUrl = ConfigKt.getGUrlMap().get(channel);
            if(!TextUtils.isEmpty(hostUrl)){
                return hostUrl;
            }
        }
        return ConfigKt.getGUrlMap().get("wms-test-hb1"); //无有效地址的debug和release统一进入默认的地址
    }
}
