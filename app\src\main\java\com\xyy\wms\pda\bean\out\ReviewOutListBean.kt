package com.xyy.wms.pda.bean.out

/**
 * 出库——外复合任务列表
 */
class ReviewOutListBean {
  var isLastPage = false // 是否最后一页
  var list: List<OutboundOrder>? = null // 出库外复合列表

  /**
   * 出库单
   */
  class OutboundOrder {
    var isCancel: String? = null // 是否为“取消订单”  1-是 0-否
    var orderCode: String? = null// 出库单号
    var erpOrderCode: String? = null// 销售单号
    var orderStatus: String? = null// 作业状态  500-待外复核；510-外复核中；520-外复核完成
    var userTask: String? = null // 是否当前登录用户领取的任务（获取任务列表时）  1-是 0-否
    var workingAreaBegin: String? = null// 开始暂存区
    var workingAreaEnd: String? = null// 终止暂存区
    var totalNum = 0 // totalNum	实际总箱数	number	整件件数+零货拼箱数量（二期新增）
    var buildingCode: String? = null // 建筑物编码
    var taskOutReviewCode: String? = null // 外复核任务单
    var totalNumber: String? = null // 外复核（按箱）总箱数
    var wholeNum: String? = null // 外复核（按箱）整箱数量
    var retailNum: String? = null // 外复核（按箱）拼箱数量
    var reviewsNumber: String? = null // 外复核（按箱）已复核箱数

  }
}
