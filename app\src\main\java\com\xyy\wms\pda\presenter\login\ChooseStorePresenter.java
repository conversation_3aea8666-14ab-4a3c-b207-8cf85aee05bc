package com.xyy.wms.pda.presenter.login;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.user.UserInfoBean;
import com.xyy.wms.pda.bean.user.WarehouseResult;
import com.xyy.wms.pda.contract.login.ChooseStoreContract;
import com.xyy.wms.pda.helper.SimpleErrorConsumer;
import com.xyy.wms.pda.helper.SimpleSuccessConsumer;
import com.xyy.wms.pda.model.login.ChooseStoreModel;
import java.util.List;
/**
 * Created by XyyMvpPdaTemplate on 12/04/2019 15:51
 */
public class ChooseStorePresenter extends BasePresenter<ChooseStoreContract.IChooseStoreModel, ChooseStoreContract.IChooseStoreView> {

    public static ChooseStorePresenter newInstance() {
        return new ChooseStorePresenter();
    }

    @Override
    protected ChooseStoreModel getModel() {
        return ChooseStoreModel.newInstance();
    }
    public void getChooseStoreList() {
        if (mIView == null || mIModel == null)
            return;
        mRxManager.register(mIModel.getChooseStoreList().subscribe(
            new SimpleSuccessConsumer<BaseResponseBean<List<WarehouseResult>>>(mIView) {
                    @Override
                    public void onSuccess(BaseResponseBean<List<WarehouseResult>> baseResponseBean) {
                        mIView.getChooseStoreListSuccess(baseResponseBean.getResult());
                    }
                }, new SimpleErrorConsumer(mIView)));
    }

    public void commitChooseStore(String storageCode) {
        if (mIView == null || mIModel == null)
            return;
        mRxManager.register(mIModel.commitChooseStore(storageCode).subscribe(
            new SimpleSuccessConsumer<BaseResponseBean<Object>>(mIView) {
                    @Override
                    public void onSuccess(BaseResponseBean<Object> baseResponseBean) {
                        mIView.commitChooseStoreSuccess(baseResponseBean);
                    }
                }, new SimpleErrorConsumer(mIView)));
    }

}
