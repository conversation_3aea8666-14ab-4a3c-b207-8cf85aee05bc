package com.xyy.wms.pda.model.out.purchaseexit;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitReViewGoods;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitReViewOrderBean;
import com.xyy.wms.pda.contract.out.purchaseexit.PurchaseExitElectronicReViewGoodsListContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.List;

import io.reactivex.Observable;

/**
 * Created by lwj on 2020-03-03.
 * <EMAIL>
 */
public class PurchaseExitElectronicReViewGoodsListModel implements PurchaseExitElectronicReViewGoodsListContract.IPurchaseExitElectronicReViewGoodsListModel {

    public static PurchaseExitElectronicReViewGoodsListModel newInstance() {
        return new PurchaseExitElectronicReViewGoodsListModel();
    }


    @Override
    public Observable<BaseResponseBean<List<PurchaseExitReViewGoods>>> queryPerformTheDocuments(String packageBarCode, String productCode, String pickUpOrder) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).queryPerformTheDocuments(packageBarCode, productCode, pickUpOrder)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<List<PurchaseExitReViewOrderBean>>> queryReviewDetail(String pickUpOrder) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).queryReviewDetail(pickUpOrder)
                .compose(RxHelper.rxSchedulerHelper());
    }
}


