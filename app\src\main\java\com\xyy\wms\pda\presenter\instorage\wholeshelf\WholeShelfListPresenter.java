package com.xyy.wms.pda.presenter.instorage.wholeshelf;

import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.shelf.InShelfResult;
import com.xyy.wms.pda.bean.productCode.ProductCode;
import com.xyy.wms.pda.contract.instorage.wholeshelf.WholeShelfListContract;
import com.xyy.wms.pda.helper.SimpleErrorConsumer;
import com.xyy.wms.pda.helper.SimpleSuccessConsumer;
import com.xyy.wms.pda.model.instorage.wholeshelf.WholeShelfListModel;
import com.xyy.wms.pda.presenter.productCode.GetProductCodePresenter;

import org.jetbrains.annotations.NotNull;

import java.util.List;
/**
 * 整件商品列表上架
 */
public class WholeShelfListPresenter extends WholeShelfListContract.WholeShelfListPresenter implements GetProductCodePresenter {
    public static WholeShelfListPresenter newInstance() {
        return new WholeShelfListPresenter();
    }
    @Override
    protected WholeShelfListModel getModel() {
        return WholeShelfListModel.newInstance();
    }
    @Override
    public void getWholeShelfList(String containerCode, int containerType) {
        if (mRxManager == null || mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.getWholeShelfList(containerCode, containerType)
                .subscribe(new SimpleSuccessConsumer<BaseResponseBean<InShelfResult>>(mIView) {
            @Override
            public void onSuccess(BaseResponseBean<InShelfResult> requestBaseBean) {
                mIView.getWholeShelfListSuccess(requestBaseBean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }





    @Override
    public void getProductBarCode(@NotNull String ownerCode, @NotNull String packageBarCode) {
        if (mRxManager == null || mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.getProductBarCode(ownerCode, packageBarCode).subscribe(new SimpleSuccessConsumer<BaseResponseBean<List<ProductCode>>>(mIView) {

            @Override
            public void onSuccess(BaseResponseBean<List<ProductCode>> requestBaseBean) {
                mIView.getProductCodeSuccess(requestBaseBean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }
}
