package com.xyy.wms.pda.model.moveStorage.scattered;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.api.ApiMoveStorageService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.common.CheckContainerCodeBean;
import com.xyy.wms.pda.bean.common.CheckContainerCodeResult;
import com.xyy.wms.pda.bean.common.LogicalRegionResult;
import com.xyy.wms.pda.bean.common.RunningTaskResult;
import com.xyy.wms.pda.bean.common.SourcePositionCodeBean;
import com.xyy.wms.pda.bean.instorage.shelf.CommitShelfResult;
import com.xyy.wms.pda.bean.instorage.shelf.ShelfDetailPost;
import com.xyy.wms.pda.bean.moveStorage.StartTaskBean;
import com.xyy.wms.pda.bean.moveStorage.StartTaskResult;
import com.xyy.wms.pda.contract.movestorage.scattered.ScatteredDownContract;
import com.xyy.wms.pda.contract.movestorage.wholeshelf.WholeShelfDownContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.List;
import java.util.Observer;

import io.reactivex.Observable;

/**
 * 零货下架 -  first step
 */
public class ScatteredDownModel extends BaseModel implements ScatteredDownContract.ScatteredDownModel {
    public static ScatteredDownModel newInstance() {
        return new ScatteredDownModel();
    }

    /**
     * 通过原货位获取逻辑区
     */
    @Override
    public Observable<BaseResponseBean<LogicalRegionResult>> getLogicalRegion(SourcePositionCodeBean goodsAllocation) {
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).getLogicalRegion(goodsAllocation)
            .compose(RxHelper.rxSchedulerHelper());
    }
    /**
     * 单个明细提交
     */
    public Observable<BaseResponseBean<CommitShelfResult>> commitStorageOrderDetail(ShelfDetailPost shelfDetailPost) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).commitStorageOrderDetail(shelfDetailPost)
            .compose(RxHelper.rxSchedulerHelper());
    }

    /**
     * 校验托盘位
     */
    @Override
    public Observable<BaseResponseBean<Boolean>> checkContainerCode(CheckContainerCodeBean containerCode) {
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).checkContainerCode(containerCode)
            .compose(RxHelper.rxSchedulerHelper());
    }

    /**
     * 开启任务
     */
    public Observable<BaseResponseBean<StartTaskResult>> startTask(StartTaskBean startTaskBean){
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).startTaskWholeShelfDown(startTaskBean)
            .compose(RxHelper.rxSchedulerHelper());
    }

    /**
     * 进行中任务
     */
    public Observable<BaseResponseBean<RunningTaskResult>> getRunningTask(){
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).getRunningTask()
            .compose(RxHelper.rxSchedulerHelper());
    }
}
