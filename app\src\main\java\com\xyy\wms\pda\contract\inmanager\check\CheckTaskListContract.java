package com.xyy.wms.pda.contract.inmanager.check;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.inmanager.check.CheckTaskListBean;

import io.reactivex.Observable;

public interface CheckTaskListContract {
    abstract class CheckTaskListPresenter extends BasePresenter<ICheckTaskListModel, ICheckTaskListView> {
        /**
         * 扫描容器编号，后台校验并返回商品列表
         *
         * @param checkPlanType 容器编号
         */
        public abstract void getCheckTask(String checkPlanType);

        public abstract void taskComplete(String checkTaskType,String checkTaskNo);
    }

    interface ICheckTaskListModel extends IBaseModel {
        /**
         * 请求商品列表
         *
         * @param checkPlanType
         * @return
         */
        Observable<BaseResponseBean<CheckTaskListBean>> getCheckTask(String checkPlanType);

        Observable<BaseResponseBean> taskComplete(String checkTaskType, String checkTaskNo);
    }

    interface ICheckTaskListView extends IBaseActivity {
        /**
         * 获取列表成功
         */
        void getCheckTaskSuccess(BaseResponseBean<CheckTaskListBean> bean);

        void taskCompleteSuccess(BaseResponseBean bean);

        /**
         * 显示网络错误
         */
        void showNetworkError();

    }
}
