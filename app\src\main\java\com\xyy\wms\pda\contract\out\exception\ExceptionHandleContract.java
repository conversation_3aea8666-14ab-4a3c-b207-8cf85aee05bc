package com.xyy.wms.pda.contract.out.exception;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.exception.ExceptionHandleListBean;
import com.xyy.wms.pda.bean.out.ExceptionBean;

import java.util.Map;

import io.reactivex.Observable;
/**
 * Created by XyyMvpPdaTemplate on 04/10/2019 14:59
 */
public interface ExceptionHandleContract {

    interface IExceptionHandleModel extends IBaseModel {
        Observable<BaseResponseBean<ExceptionHandleListBean>> queryExceptionTaskList(ExceptionBean params);
        Observable<BaseResponseBean<ExceptionHandleListBean>> queryExceptionTaskListByContainer(ExceptionBean params);
    }

    interface IExceptionHandleView extends IBaseActivity {
        void queryExceptionTaskListSuccess(BaseResponseBean<ExceptionHandleListBean> baseResponseBean);

        void queryExceptionTaskListByContainerSuccess(BaseResponseBean<ExceptionHandleListBean> baseResponseBean);
    }

}
