package com.xyy.wms.pad.instorage.presenter.newinspection

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.wms.pad.instorage.model.newinstorage.DetailsOfAcceptedGoodsModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderDetailListBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderDetailListPost
import com.xyy.wms.pda.contract.instorage.checkaccept.DetailsOfAcceptedGoodsContract
import com.xyy.wms.pda.helper.SimpleErrorConsumer
import com.xyy.wms.pda.helper.SimpleSuccessConsumer

/**
 * 已验收商品列表
 * @Description
 * @Date 2022/4/11
 */
class DetailsOfAcceptedGoodsPresenter : BasePresenter<DetailsOfAcceptedGoodsModel, DetailsOfAcceptedGoodsContract.DetailsOfAcceptedGoodsContractView>() {

    override fun getModel(): DetailsOfAcceptedGoodsModel {
        return DetailsOfAcceptedGoodsModel.newInstance()
    }

    fun getCheckOrderDetailList(checkOrderDetailListPost: CheckOrderDetailListPost) {
        mRxManager.register(mIModel.getCheckOrderDetailList(checkOrderDetailListPost)
            .subscribe(object : SimpleSuccessConsumer<BaseResponseBean<List<CheckOrderDetailListBean>>>(mIView,"加载中") {
                override fun onSuccess(t: BaseResponseBean<List<CheckOrderDetailListBean>>) {
                    mIView.getCheckOrderDetailListSuccess(t)
                }
            }, SimpleErrorConsumer(mIView)))
    }

}
