package com.xyy.wms.pda.contract.instorage.checkaccept

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.utilslibrary.base.IBaseModel
import io.reactivex.Observable
import com.xyy.wms.pda.bean.instorage.checkaccept.WaitCheckInfoBySupplierPost
import com.xyy.wms.pda.bean.instorage.checkaccept.WaitCheckInfoBySupplierBean

/**
 * 入库验收单（New）合约，定义 View 与 Model（与旧版解耦）
 */
interface CheckAcceptBillContractNew {
    interface ICheckAcceptBillModelNew : IBaseModel {
        // 扫描供应商 - 查询待验信息
        fun getWaitCheckInfoBySupplier(post: WaitCheckInfoBySupplierPost): Observable<BaseResponseBean<MutableList<WaitCheckInfoBySupplierBean>>>
    }

    interface ICheckAcceptBillViewNew : IBaseActivity {
        // 扫描供应商 - 返回待验信息
        fun getWaitCheckInfoBySupplierSuccess(baseResponseBean: BaseResponseBean<MutableList<WaitCheckInfoBySupplierBean>>)
    }
}
