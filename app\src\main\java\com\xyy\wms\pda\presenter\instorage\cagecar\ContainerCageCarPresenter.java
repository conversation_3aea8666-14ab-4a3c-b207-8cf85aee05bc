package com.xyy.wms.pda.presenter.instorage.cagecar;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.cageCar.BindingForRollPost;
import com.xyy.wms.pda.bean.instorage.cageCar.ContainerCageCar;
import com.xyy.wms.pda.contract.instorage.cagecar.ContainerCageCarContract;
import com.xyy.wms.pda.helper.SimpleErrorConsumer;
import com.xyy.wms.pda.helper.SimpleSuccessConsumer;
import com.xyy.wms.pda.model.instorage.cagecar.ContainerCageCarModel;

/**
 * Created by XyyMvpPdaTemplate on 02/18/2020 13:55
 * 容器绑定笼车
 */
public class ContainerCageCarPresenter extends BasePresenter<ContainerCageCarContract.IContainerCageCarModel, ContainerCageCarContract.IContainerCageCarView> {

    public static ContainerCageCarPresenter newInstance() {
        return new ContainerCageCarPresenter();
    }

    @Override
    protected ContainerCageCarModel getModel() {
        return ContainerCageCarModel.newInstance();
    }
    public void getStorageContainersInfo(String containerCode, String rollContainerCode) {
        mRxManager.register(mIModel.getStorageContainersInfo(containerCode, rollContainerCode).subscribe(new SimpleSuccessConsumer<BaseResponseBean<ContainerCageCar>>(mIView) {

            @Override
            public void onSuccess(BaseResponseBean<ContainerCageCar> baseResponseBean) {
                mIView.getStorageContainersInfoSuccess(baseResponseBean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }

    public void doBindingForRoll(BindingForRollPost bindingForRollPost) {
        mRxManager.register(mIModel.doBindingForRoll(bindingForRollPost).subscribe(new SimpleSuccessConsumer<BaseResponseBean>(mIView) {
            @Override
            public void onSuccess(BaseResponseBean baseResponseBean) {
                mIView.doBindingForRollSuccess(baseResponseBean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }

}
