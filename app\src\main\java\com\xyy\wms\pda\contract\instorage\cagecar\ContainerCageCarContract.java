package com.xyy.wms.pda.contract.instorage.cagecar;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.cageCar.BindingForRollPost;
import com.xyy.wms.pda.bean.instorage.cageCar.ContainerCageCar;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 02/18/2020 13:55
 * 容器绑定笼车
 */
public interface ContainerCageCarContract {

    interface IContainerCageCarModel extends IBaseModel {
        Observable<BaseResponseBean<ContainerCageCar>> getStorageContainersInfo(String containerCode, String rollContainerCode);

        Observable<BaseResponseBean> doBindingForRoll(BindingForRollPost bindingForRollPost);
    }

    interface IContainerCageCarView extends IBaseActivity {
        void getStorageContainersInfoSuccess(BaseResponseBean<ContainerCageCar> baseResponseBean);

        void doBindingForRollSuccess(BaseResponseBean baseResponseBean);
    }

}
