package com.xyy.wms.pda.presenter.inmanager.search

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.wms.pda.contract.inmanager.search.SearchContract.ISearchActivityModel
import com.xyy.wms.pda.contract.inmanager.search.SearchContract.ISearchActivityView
import com.xyy.wms.pda.model.inmanager.search.SearchModel

/**
 * Created by XyyMvpSportTemplate on 03/26/2019 14:02
 */
class SearchPresenter : BasePresenter<ISearchActivityModel?, ISearchActivityView?>() {

  override fun getModel(): SearchModel {
    return SearchModel.newInstance()
  }

  companion object {
    fun newInstance(): SearchPresenter {
      return SearchPresenter()
    }
  }
}
