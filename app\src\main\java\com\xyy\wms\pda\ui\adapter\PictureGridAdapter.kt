package com.xyy.wms.pda.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.ProgressBar
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.xyy.wms.pda.R
import com.xyy.wms.pda.bean.instorage.PictureInfo
import com.xyy.wms.pda.bean.instorage.UploadStatus
import com.xyy.wms.pda.config.ExceptionPictureConfig

/**
 * 图片网格适配器
 * 用于异常图片上传功能的图片展示
 */
class PictureGridAdapter : RecyclerView.Adapter<PictureGridAdapter.PictureViewHolder>() {
    
    private val pictureList: MutableList<PictureInfo> = mutableListOf()
    
    // 点击事件监听器
    var onItemClickListener: ((position: Int) -> Unit)? = null
    var onDeleteClickListener: ((position: Int) -> Unit)? = null
    
    init {
        // 初始化空白占位符
        initEmptyPlaceholders()
    }
    
    /**
     * 初始化空白占位符
     */
    private fun initEmptyPlaceholders() {
        pictureList.clear()
        repeat(ExceptionPictureConfig.MAX_PICTURE_COUNT) {
            pictureList.add(PictureInfo(isPlaceholder = true))
        }
    }
    
    /**
     * 更新图片数据
     */
    fun updatePictureData(pictures: List<PictureInfo>) {
        pictureList.clear()
        pictureList.addAll(pictures)
        
        // 补充占位符到最大数量
        while (pictureList.size < ExceptionPictureConfig.MAX_PICTURE_COUNT) {
            pictureList.add(PictureInfo(isPlaceholder = true))
        }
        
        notifyDataSetChanged()
    }
    
    /**
     * 添加图片
     */
    fun addPicture(pictureInfo: PictureInfo) {
        val firstPlaceholderIndex = pictureList.indexOfFirst { it.isPlaceholder }
        if (firstPlaceholderIndex != -1) {
            pictureList[firstPlaceholderIndex] = pictureInfo
            notifyItemChanged(firstPlaceholderIndex)
        }
    }
    
    /**
     * 删除图片
     */
    fun removePicture(position: Int) {
        if (position < pictureList.size && !pictureList[position].isPlaceholder) {
            pictureList[position] = PictureInfo(isPlaceholder = true)
            notifyItemChanged(position)
        }
    }
    
    /**
     * 获取实际图片列表（不包含占位符）
     */
    fun getActualPictures(): List<PictureInfo> {
        return pictureList.filter { !it.isPlaceholder }
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PictureViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_picture_grid, parent, false)
        return PictureViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: PictureViewHolder, position: Int) {
        holder.bind(pictureList[position], position)
    }
    
    override fun getItemCount(): Int = pictureList.size
    
    inner class PictureViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val ivPicture: ImageView = itemView.findViewById(R.id.iv_picture)
        private val ivAddIcon: ImageView = itemView.findViewById(R.id.iv_add_icon)
        private val ivDelete: ImageView = itemView.findViewById(R.id.iv_delete)
        private val pbUpload: ProgressBar = itemView.findViewById(R.id.pb_upload)
        private val ivUploadStatus: ImageView = itemView.findViewById(R.id.iv_upload_status)
        
        fun bind(pictureInfo: PictureInfo, position: Int) {
            if (pictureInfo.isPlaceholder) {
                // 显示占位符状态
                ivAddIcon.visibility = View.VISIBLE
                ivDelete.visibility = View.GONE
                pbUpload.visibility = View.GONE
                ivUploadStatus.visibility = View.GONE
                ivPicture.setImageResource(0)
                
                // 设置点击事件
                itemView.setOnClickListener {
                    onItemClickListener?.invoke(position)
                }
            } else {
                // 显示图片
                ivAddIcon.visibility = View.GONE
                ivDelete.visibility = View.VISIBLE
                
                // 加载图片
                if (pictureInfo.localPath.isNotEmpty()) {
                    Glide.with(itemView.context)
                        .load(pictureInfo.localPath)
                        .centerCrop()
                        .into(ivPicture)
                } else if (pictureInfo.serverUrl.isNotEmpty()) {
                    Glide.with(itemView.context)
                        .load(pictureInfo.serverUrl)
                        .centerCrop()
                        .into(ivPicture)
                }
                
                // 根据上传状态显示不同UI
                when (pictureInfo.uploadStatus) {
                    UploadStatus.NONE -> {
                        pbUpload.visibility = View.GONE
                        ivUploadStatus.visibility = View.GONE
                    }
                    UploadStatus.UPLOADING -> {
                        pbUpload.visibility = View.VISIBLE
                        ivUploadStatus.visibility = View.GONE
                    }
                    UploadStatus.SUCCESS -> {
                        pbUpload.visibility = View.GONE
                        ivUploadStatus.visibility = View.VISIBLE
                        ivUploadStatus.setImageResource(R.drawable.ic_check_circle_green)
                    }
                    UploadStatus.FAILED -> {
                        pbUpload.visibility = View.GONE
                        ivUploadStatus.visibility = View.VISIBLE
                        ivUploadStatus.setImageResource(R.drawable.ic_error_circle_red)
                    }
                }
                
                // 设置删除按钮点击事件
                ivDelete.setOnClickListener {
                    onDeleteClickListener?.invoke(position)
                }
                
                // 设置图片点击事件（预览）
                itemView.setOnClickListener {
                    // 可以在这里添加图片预览功能
                }
            }
        }
    }
}
