package com.xyy.wms.pda.bean.instorage.shelf

import java.io.Serializable

/**
入库上架 列表的 list 的 bean

lineNumberSplitOrigin	原始行号	number	@mock=1

onShelfCount	已上架条目数	number	（例 2/3 中的2）

orgCode	机构编号	string	@mock=007

productBatchCode	批号	string	@mock=181016

productCode	商品编号	string	@mock=Y3002021

productName	商品名称	string	@mock=开塞露（含甘油）

purchaseStorageOrderDetailVoList	该原始行中的明细列表	array<object>

shelfCount	明细条目数	number	总数（例 2/3 中的3）

shelfUser	上架员id	string	@mock=

storageOrderCode	上架单编号	string	@mock=SJD1908200070016

storageOrderStatus	入库单状态值	number	(1:进行中;2:已完成;)

storageOrderStatusDesc	入库单状态描述	string	(1:未完成;2:已完成;)

unShelfCount	未上架条目数	number
 */
class StorageOrderBean : Serializable {
    var storageOrderCode: String? = null
    var orgCode: String? = null
    var storageOrderStatus: Int = -1
    var productCode: String? = null
    var productBatchCode: String? = null
    var shelfUser: String? = null
    var storageOrderStatusDesc: String? = null
    var unShelfCount: Int = 0
    var onShelfCount: Int = 0
    var shelfCount: Int = 0
    var productName: String? = null
    var purchaseStorageOrderDetailVoList: List<NewInProductBean>? = null

    //pda商品列表页唯一标识	string	（新增字段）
    var pdaProductLineId: String? = null
    var shelvesCountBig: String? = ""

}