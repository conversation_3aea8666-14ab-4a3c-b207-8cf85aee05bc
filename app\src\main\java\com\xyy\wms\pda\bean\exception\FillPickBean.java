package com.xyy.wms.pda.bean.exception;

import java.io.Serializable;

/**
 * created by  liang<PERSON>  on 2019/4/17.
 */
public class FillPickBean implements Serializable {

    private String allocationCode;    //分配单号	string
    private String batchNumber;    //批号	string
    private String sterilizingBatchNumber;    //灭菌批号	string
    private String exceptionNumber;    //异常数量	number
    private String exceptionTaskCode;    //异常号	string
    private String exceptionType;    //异常类型	string	1:零货 2:内复核 3:整件拣货
    private String goodsAllocation;    //货位	string
    private String orderCode;    //单据编号	string
    private String ownerCode;    //业主编号	string
    private String pieceNumber;    //件包装数量	number
    private String productCode;    //商品编号	string
    private String dealUserName;//登录人	string
    private int updateUser;//	登录人ID	number
    private String passBoxCode;    //容器号	number

    private String orgCode;
    private String erpOrderCode;
    private String customerCode;
    private String customerName;
    private String channelCode;
    private String warehouseCode;
    private String buildingCode;
    private String storageAreaCode;
    private String storageRoomCode;
    private String storageTypeCode;

    private String productionTime; // 生产日期
    private String valiperiodValidity ; // 有效期

    public String getProductionTime() {
        return productionTime;
    }

    public void setProductionTime(String productionTime) {
        this.productionTime = productionTime;
    }

    public String getValiperiodValidity() {
        return valiperiodValidity;
    }

    public void setValiperiodValidity(String valiperiodValidity) {
        this.valiperiodValidity = valiperiodValidity;
    }

    public String getErpOrderCode() {
        return erpOrderCode;
    }

    public void setErpOrderCode(String erpOrderCode) {
        this.erpOrderCode = erpOrderCode;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getBuildingCode() {
        return buildingCode;
    }

    public void setBuildingCode(String buildingCode) {
        this.buildingCode = buildingCode;
    }

    public String getStorageAreaCode() {
        return storageAreaCode;
    }

    public void setStorageAreaCode(String storageAreaCode) {
        this.storageAreaCode = storageAreaCode;
    }

    public String getStorageRoomCode() {
        return storageRoomCode;
    }

    public void setStorageRoomCode(String storageRoomCode) {
        this.storageRoomCode = storageRoomCode;
    }

    public String getStorageTypeCode() {
        return storageTypeCode;
    }

    public void setStorageTypeCode(String storageTypeCode) {
        this.storageTypeCode = storageTypeCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getPassBoxCode() {
        return passBoxCode;
    }

    public void setPassBoxCode(String passBoxCode) {
        this.passBoxCode = passBoxCode;
    }

    public String getDealUserName() {
        return dealUserName;
    }

    public void setDealUserName(String dealUserName) {
        this.dealUserName = dealUserName;
    }

    public int getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(int updateUser) {
        this.updateUser = updateUser;
    }

    public String getAllocationCode() {
        return allocationCode;
    }

    public void setAllocationCode(String allocationCode) {
        this.allocationCode = allocationCode;
    }

    public String getBatchNumber() {
        return batchNumber;
    }

    public void setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
    }

    public String getSterilizingBatchNumber() {
        return sterilizingBatchNumber;
    }

    public void setSterilizingBatchNumber(String sterilizingBatchNumber) {
        this.sterilizingBatchNumber = sterilizingBatchNumber;
    }

    public String getExceptionNumber() {
        return exceptionNumber;
    }

    public void setExceptionNumber(String exceptionNumber) {
        this.exceptionNumber = exceptionNumber;
    }

    public String getExceptionTaskCode() {
        return exceptionTaskCode;
    }

    public void setExceptionTaskCode(String exceptionTaskCode) {
        this.exceptionTaskCode = exceptionTaskCode;
    }

    public String getExceptionType() {
        return exceptionType;
    }

    public void setExceptionType(String exceptionType) {
        this.exceptionType = exceptionType;
    }

    public String getGoodsAllocation() {
        return goodsAllocation;
    }

    public void setGoodsAllocation(String goodsAllocation) {
        this.goodsAllocation = goodsAllocation;
    }

    public String getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getPieceNumber() {
        return pieceNumber;
    }

    public void setPieceNumber(String pieceNumber) {
        this.pieceNumber = pieceNumber;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }
}
