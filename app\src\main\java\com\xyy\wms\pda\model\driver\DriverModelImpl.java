package com.xyy.wms.pda.model.driver;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.driver.CarrierBean;
import com.xyy.wms.pda.bean.driver.DriverBean;
import com.xyy.wms.pda.bean.driver.DriverEditBean;
import com.xyy.wms.pda.bean.driver.DriverInfoBean;
import com.xyy.wms.pda.bean.driver.UploadPicBean;
import com.xyy.wms.pda.bean.moveStorage.DriverRequest;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.io.File;
import java.util.List;

import io.reactivex.Observable;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;

public class DriverModelImpl extends BaseModel implements DriverModel{

    public static DriverModelImpl newInstance(){
        return new DriverModelImpl();
    }

    @Override
    public Observable<BaseResponseBean<List<DriverBean>>> getDriverList(String carNo) {
        return RetrofitCreateHelper.createApi(ApiService.class).getDriverListData(new DriverRequest(carNo))
            .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<DriverEditBean>> getDriverInfo(Number id) {
        return RetrofitCreateHelper.createApi(ApiService.class).getDriverInfo(new DriverInfoBean(id))
            .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean> editDriverInfo(boolean isAdd, DriverEditBean editBean) {
        if (isAdd) {
            return RetrofitCreateHelper.createApi(ApiService.class).addDriverInfo(editBean)
                .compose(RxHelper.rxSchedulerHelper());
        } else {
          return  RetrofitCreateHelper.createApi(ApiService.class).editDriverInfo(editBean)
                .compose(RxHelper.rxSchedulerHelper());
        }
    }

    @Override
    public Observable<BaseResponseBean> uploadFile(File file) {
        // 上传文件参数
        RequestBody  requestBody= RequestBody.create(MediaType.parse("multipart/form-data"), file);
        MultipartBody.Part part = MultipartBody.Part.createFormData("file", file.getName(), requestBody);
        return  RetrofitCreateHelper.createApi(ApiService.class).uploadFile(part)
            .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<CarrierBean>> getCarrier() {
        return  RetrofitCreateHelper.createApi(ApiService.class).getCarrier()
            .compose(RxHelper.rxSchedulerHelper());
    }


}
