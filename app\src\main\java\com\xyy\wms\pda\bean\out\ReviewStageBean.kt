package com.xyy.wms.pda.bean.out

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

/**
 * 拆零拣货 - 复核台
 */
@Parcelize
data class ReviewStageBean(
  var caseCode: String? = null,// 周转箱投递位置
  var passBoxCode: String? = null,// 周转箱号
  var reviewStage: String? = null,// 复核台号
  var casecodeStatus: Int = 0,// 0--未关联任务 1--未完成任务 2--异常 3--拣货完成
  var arriveStatus: Int = 0// 是否已投箱 0--未落位 1--已落位
) : Parcelable
