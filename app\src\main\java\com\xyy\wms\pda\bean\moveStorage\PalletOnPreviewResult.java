package com.xyy.wms.pda.bean.moveStorage;

import java.io.Serializable;
import java.util.List;

public class PalletOnPreviewResult implements Serializable {
    private String logicalRegionName="";
    private String allocationCount="";
    private List<PalletOnPreviewList> list;
    public static class PalletOnPreviewList implements Serializable{
        String positionRowNumber="";
        String emptyCnt="";
        public void setPositionRowNumber(String positionRowNumber){this.positionRowNumber = positionRowNumber;}
        public String getPositionRowNumber(){return positionRowNumber;}
        public void setEmptyCnt(String emptyCnt){this.emptyCnt = emptyCnt;}
        public String getEmptyCnt(){return emptyCnt;}
    }
    public void setAllocationCount(String allocationCount){
        this.allocationCount = allocationCount;
    }
    public String getAllocationCount(){
        return allocationCount;
    }
    public void setItems(List<PalletOnPreviewList> items) {
        this.list = items;
    }

    public List<PalletOnPreviewList> getItems(){
        return list;
    }
}
