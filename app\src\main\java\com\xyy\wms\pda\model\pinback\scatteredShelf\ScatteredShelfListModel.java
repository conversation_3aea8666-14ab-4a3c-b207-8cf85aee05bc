package com.xyy.wms.pda.model.pinback.scatteredShelf;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiPinBackService;
import com.xyy.wms.pda.api.ApiService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.pinback.ProductBean;
import com.xyy.wms.pda.bean.productCode.ProductCode;
import com.xyy.wms.pda.contract.pinback.scatteredShelf.ScatteredShelfListContract;
import com.xyy.wms.pda.model.productCode.GetProductCodeModel;
import com.xyy.wms.pda.net.RetrofitCreateHelper;
import java.util.List;

import io.reactivex.Observable;

/**
 * 零散上架列表model
 */
public class ScatteredShelfListModel implements ScatteredShelfListContract.IScatteredShelfListModel,GetProductCodeModel {

    public static ScatteredShelfListModel newInstance() {
        return new ScatteredShelfListModel();
    }
    @Override
    public Observable<BaseResponseBean<List<ProductBean>>> getScatteredShelfList(String containerCode, int type) {
        return RetrofitCreateHelper.createApi(ApiPinBackService.class).getPieceListByContainerCode(containerCode,type).
        compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<List<ProductCode>>> getProductBarCode(String ownerCode, String packageBarCode) {
        return RetrofitCreateHelper.createApi(ApiService.class).selectProductPda(ownerCode, packageBarCode)
                .compose(RxHelper.rxSchedulerHelper());
    }
}
