package com.xyy.wms.pda.presenter.inmanager.search

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.inmanager.search.SearchProductBean
import com.xyy.wms.pda.contract.inmanager.search.SearchProductListContract.ISearchProductListModel
import com.xyy.wms.pda.contract.inmanager.search.SearchProductListContract.ISearchProductListView
import com.xyy.wms.pda.helper.SimpleErrorConsumer
import com.xyy.wms.pda.helper.SimpleSuccessConsumer
import com.xyy.wms.pda.model.inmanager.search.SearchProductListModel

/**
 * Created by XyyMvpPdaTemplate on 04/19/2019 11:21
 */
class SearchProductListPresenter : BasePresenter<ISearchProductListModel, ISearchProductListView>() {
  override fun getModel(): SearchProductListModel {
    return SearchProductListModel.newInstance()
  }

  /**
   * 根据商品条码查询信息
   */
  fun queryProductListByBarCode(productBarCode: String?, enter: Boolean) {
    if (mIModel == null || mIView == null) return
    mRxManager.register(mIModel!!.queryProductListByBarCode(productBarCode).subscribe(
      object : SimpleSuccessConsumer<BaseResponseBean<List<SearchProductBean>>>(mIView) {
        override fun onSuccess(baseBean: BaseResponseBean<List<SearchProductBean>>) {
          if (mIView == null) return
          mIView!!.queryProductListByBarCodeSuccess(baseBean.result, enter)
        }
      }, SimpleErrorConsumer(mIView)))
  }

  companion object {
    @JvmStatic
    fun newInstance(): SearchProductListPresenter {
      return SearchProductListPresenter()
    }
  }
}
