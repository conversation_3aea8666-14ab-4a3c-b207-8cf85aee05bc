package com.xyy.wms.pad.instorage.presenter.newinspection

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.wms.pad.instorage.model.newinstorage.TracingCodeScanningModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckPermissionBystaffNumBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckPermissionBystaffNumPost
import com.xyy.wms.pda.bean.instorage.checkaccept.ConfirmCodeScanOrderPost
import com.xyy.wms.pda.bean.instorage.checkaccept.FindPurchaseCodeScanOrderBean
import com.xyy.wms.pda.bean.instorage.checkaccept.FindPurchaseCodeScanOrderPost
import com.xyy.wms.pda.bean.instorage.checkaccept.UnlockCodeScanOrder
import com.xyy.wms.pda.contract.instorage.checkaccept.TracingCodeScanningContract
import com.xyy.wms.pda.helper.SimpleErrorConsumer
import com.xyy.wms.pda.helper.SimpleSuccessConsumer

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/4/11
 */
class TracingCodeScanningPresenter : BasePresenter<TracingCodeScanningModel, TracingCodeScanningContract.TracingCodeScanningContractView>() {

    override fun getModel(): TracingCodeScanningModel {
        return TracingCodeScanningModel.newInstance()
    }

    fun findPurchaseCodeScanOrder(findPurchaseCodeScanOrderPost: FindPurchaseCodeScanOrderPost) {
        mRxManager.register(mIModel.findPurchaseCodeScanOrder(findPurchaseCodeScanOrderPost)
            .subscribe(object : SimpleSuccessConsumer<BaseResponseBean<FindPurchaseCodeScanOrderBean>>(mIView,"加载中") {
            override fun onSuccess(t: BaseResponseBean<FindPurchaseCodeScanOrderBean>) {
                mIView.findPurchaseCodeScanOrderSuccess(t)
            }
        }, SimpleErrorConsumer(mIView)))
    }

    fun confirmCodeScanOrder(confirmCodeScanOrderPost : ConfirmCodeScanOrderPost) {
        mRxManager.register(mIModel.confirmCodeScanOrder(confirmCodeScanOrderPost)
            .subscribe(object :
                SimpleSuccessConsumer<BaseResponseBean<String>>(mIView, "请求中···") {
                override fun onSuccess(responseBean : BaseResponseBean<String>) {
                    mIView.confirmCodeScanOrderSuccess(responseBean)
                }
            }, SimpleErrorConsumer(mIView))
        )
    }

    fun checkPermissionBystaffNum(checkPermissionBystaffNumPost: CheckPermissionBystaffNumPost) {
        mRxManager.register(mIModel.checkPermissionBystaffNum(checkPermissionBystaffNumPost)
            .subscribe(object :
                SimpleSuccessConsumer<BaseResponseBean<CheckPermissionBystaffNumBean>>(mIView, "请求中···") {
                override fun onSuccess(responseBean : BaseResponseBean<CheckPermissionBystaffNumBean>) {
                    mIView.checkPermissionBystaffNumSuccess(responseBean)
                }
            }, SimpleErrorConsumer(mIView))
        )
    }

    fun unlockCodeScanOrder(unlockCodeScanOrder : UnlockCodeScanOrder) {
        mRxManager.register(mIModel.unlockCodeScanOrder(unlockCodeScanOrder)
            .subscribe(object :
                SimpleSuccessConsumer<BaseResponseBean<String>>(mIView, "请求中···") {
                override fun onSuccess(responseBean : BaseResponseBean<String>) {
                    mIView.unlockCodeScanOrderSucces(responseBean)
                }
            }, SimpleErrorConsumer(mIView))
        )
    }

}
