package com.xyy.wms.pda.bean.instorage.cageCar

import java.io.Serializable

/**
 * 三级bean
 */
class CageCarProductBean : Serializable, Cloneable {
    var actualPosition: String? = null //实际货位	string	@mock=
    var approvarNumbers: String? = null//批准文号	string	@mock=国药准字Z44020994
    var approvarStatus: Int? = -1//   审批状态    number    @mock = 0
    var buildingCode: String? = null// 建筑物编码 string @mock =01
    var channelCode: String? = null//    业务类型编码    string    @mock =2
    var checkOrderCode: String? = null//        string
    var commonName: String? = null//  药品通用名称    string    @mock =少林风湿跌打膏
    var containerCode: String? = null//  容器编号    string    @mock =100061
    var createTime: String? = null// 创建时间    string    @mock =2020-02-21 12:11:42
    var createUser: String? = null//  创建人id    string    @mock =781
    var departCode: String? = null//     string    @mock =JXG0103
    var differencesBigNumber: String? = null//     string
    var differencesScatterNumber: String? = null//     string
    var dosageForm: String? = null// 剂型    string    @mock =贴膏剂
    var erpStorageOrderCode: String? = null//      string    @mock =RKDD547202002210011
    var floor: String? = null//    string    @mock =
    var fromLineNumber: Int? = -1//      number    @mock =1
    var id: Int? = -1//  主键id    number    @mock =10469
    var ids: String? = null//   客户端展示的时候，多条明细id组合    string    @mock ='10469''10470''10471''10472''10473''10474''10475'
    var largeCategory: String? = null// 商品大类    string    @mock =外用
    var lineNumber: Int? = -1//   行号    number    @mock =1
    var lineNumberSplitOrigin: Int? = -1// 原始行号    number    @mock =1
    var manufacturer: String? = null//  生产厂家名称    string    @mock =广东粤威制药有限公司
    var marketAuthor: String? = null//    string    @mock =上市许可持有人
    var mediumPackageBarCode: String? = null//    string    @mock =
    var modifyReason: String? = null//    string
    var occupyType: Int? = -1//  number    @mock =1
    var occupyTypeDesc: String? = null//   string
    var onShelvesCount: Int? = -1//    number    @mock =0
    var orderType: String? = null//   string    @mock =采购上架单
    var orgCode: String? = null//  机构编码    string    @mock =D547
    var ownerCode: String? = null//  业主编码    string    @mock =009
    var packingUnit: String? = null// 包装单位    string    @mock =盒
    var pdaProductDetailLineId: String? = null//     string    @mock =100061Y1000017201902121
    var pdaProductLineId: String? = null//    string    @mock =100061Y1000017
    var pickingPath: Int? = -1// 拣货路径    number    @mock =7010
    var piecePackageBarCode: String? = null//    string    @mock =
    var producingArea: String? = null//  商品产地    string    @mock =广东
    var productBatchCode: String? = null// 商品批号    string    @mock =20190212
    var productCode: String? = null//商品编号    string    @mock =Y1000017
    var productCountBig: Int? = -1//收货件数    number    @mock =0
    var productCountScatter: Int? = -1//  收货零散数    number    @mock =50
    var productCountSmall: Int? = -1// 收货数量    number    @mock =50
    var productManufactureDate: String? = null//  生产日期    string    @mock =2019-02-25
    var productName: String? = null// 商品名称    string    @mock =少林风湿跌打膏
    var productPackingBigNumber: Int? = -1//  件包装数量    number    @mock =300
    var productPackingId: String? = null//   商品件包装规格id    string    @mock =
    var productPackingMiddleNumber: Int? = -1//   中包装数量    number    @mock =1
    var productSupervise: Int? = -1//   追溯码采集    number    @mock =1
    var productSuperviseDesc: String? = null//      string
    var productType: Int? = -1//  number    @mock =0
    var productValidDate: String? = null//  有效期至    string    @mock =2021-01-31
    var purchaseLineNumber: Int? = -1//   采购订单行号    number    @mock =1
    var purchaseOrderCode: String? = null// 采购订单编号    string    @mock =CGD1911004050
    var purchaseUser: String? = null//    string    @mock =2105
    var receiveLineNumber: Int? = -1//       number    @mock =1
    var receiveOrderCode: String? = null//     string
    var registrationCertificate: String? = null// 进口注册证号    string    @mock =
    var rejectReasonSupplement: String? = null//     string    @mock =
    var rollContainerCode: String? = null//     string    @mock =11
    var rollContainerLocationCode: String? = null//    string    @mock =01
    var rowid: String? = null//    string
    var shadingProperty: Int? = -1//   遮光属性 从商品表取值（1：避光 2：遮光 3：凉暗）    number    @mock =3
    var shelfReceiveTime: String? = null//     string    @mock =2020-02-21 12:12:30
    var shelfUser: String? = null//  string    @mock =781
    var shelfUserName: String? = null//    string    @mock =张泽宇
    var shelvesCountBig: Int? = -1//   上架件数    number    @mock =0
    var shelvesCountScatter: Int? = 0//   上架零散数    number    @mock =50
    var shelvesCountSmall: Int? = -1//  上架数量    number    @mock =50
    var showPosition: String? = null//  显示货位    string    @mock =LHF01-010101
    var smallPackageBarCode: String? = null//    string    @mock =6916264663285
    var specifications: String? = null// 规格    string    @mock =8cm*9.5cm*4片*2袋
    var sqlId: Int? = -1//     number    @mock =10469
    var storageAreaCode: String? = null//  库区编号    string    @mock =LHF
    var storageAreaType: String? = null//     string    @mock =1
    var storageBhk: Int? = -1//   number    @mock =0
    var storageClassification: String? = null//    string    @mock =0
    var storageConditions: String? = null// 存储条件    string    @mock =8
    var storageConditionsDesc: String? = null// 存储条件展示    string    @mock =阴凉凉暗
    var storageOrderCode: String? = null// 上架单编号    string    @mock =SJDD547202002210037
    var storageOrderStatus: Int? = -1//  上架明细状态    number    (1:未上架;2:已上架);
    var storageOrderStatusDesc: String? = null// 上架明细状态中文描述    string    '未上架''已上架')
    var storageRoomCode: String? = null//  库房编码    string    @mock =LHK
    var storageRoomName: String? = null// 库房名次    string    @mock =零货库房
    var storageTimeDifference: String? = null//     string
    var storageType: String? = null//  string
    var storageTypeArea: String? = null//  上架区域(逻辑去)    string    @mock =LHK-NY
    var storageTypeAreaName: String? = null//   逻辑区中文    string    @mock =LHK内用
    var storageTypeCode: String? = null// 库别编码    string    @mock =LHK
    var storageTypeName: String? = null//   库别中文    string    @mock =零货库
    var supplierName: String? = null//     string
    var updateUser: String? = null//  更新用户id    string    @mock =781
    var warehouseCode: String? = null//  仓库编码    string    @mock =WMS360000_1
    var sterilizingBatchNumber: String? = null // 灭菌批号
    var yn: Int? = -1//  逻辑删除    number    0:删除;1:未删除(有效)

    @Throws(CloneNotSupportedException::class)// 克隆失败抛出异常
    public override fun clone(): CageCarProductBean {
        return super.clone() as CageCarProductBean
    }
}