package com.xyy.wms.pda.model.out.bind;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.out.bind.CheckResult;
import com.xyy.wms.pda.contract.out.bind.BindWallCodeContract;
import com.xyy.wms.pda.contract.out.bind.OutReviewContract;
import com.xyy.wms.pda.model.ServiceModel;

import java.util.Map;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 04/10/2019 14:59
 */
public class OutReviewModel extends ServiceModel implements OutReviewContract.IOutReviewModel {

    public static OutReviewModel newInstance() {
        return new OutReviewModel();
    }

    @Override
    public Observable<BaseResponseBean<String>> saveBoxCode(Map<String, Object> params) {
        return getApiOutManagerService().saveBoxCode(params).compose(RxHelper.rxSchedulerHelper());
    }
}
