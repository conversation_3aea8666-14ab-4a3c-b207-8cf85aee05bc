package com.xyy.wms.pda.contract.inmanager.check

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.inmanager.check.CheckUpBean
import io.reactivex.Observable

/**
 * Created by zcj on 2018/11/8 16
 */
interface CheckUpContract {
  interface ICheckUpModel : IBaseModel {
    fun checkUp(positionCode: Map<String,String?>): Observable<BaseResponseBean<List<CheckUpBean>>>
  }

  interface ICheckUpView : IBaseActivity {
    fun checkUpSuccess(productList: List<CheckUpBean>, isEnter: Boolean)
  }

}
