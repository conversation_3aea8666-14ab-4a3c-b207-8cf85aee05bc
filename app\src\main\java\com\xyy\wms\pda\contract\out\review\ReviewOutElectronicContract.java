package com.xyy.wms.pda.contract.out.review;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.out.ReviewSupervisionCodeBean;
import com.xyy.wms.pda.bean.out.ReviewSupervisionProductBean;

import java.util.List;
import java.util.Map;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 02/18/2019 18:57
 */
public interface ReviewOutElectronicContract {

    interface IReviewOutElectronicModel extends IBaseModel {
        /**
         * 外复核 获取电子追溯码列表
         *
         * @param map
         * @return
         */
        Observable<BaseResponseBean<ReviewSupervisionCodeBean>> reviewOutSupervision(Map<String, Object> map);
        /**
         * 外复核 获取电子追溯码商品信息
         *
         * @param map
         * @return
         */
        Observable<BaseResponseBean<ReviewSupervisionProductBean>> reviewOutSupervisionProduct(Map<String, String> map);
    }

    interface IReviewOutElectronicView extends IBaseActivity {
        /**
         * 外复核获取电子追溯码列表成功
         *
         * @param bean
         */
        void reviewOutSupervisionSuccess(ReviewSupervisionCodeBean bean);
        /**
         * 外复核获取电子追溯码商品信息成功
         *  @param bean
         * @param codeList
         * @param position
         */
        void reviewOutSupervisionProductSuccess(ReviewSupervisionProductBean bean, List<ReviewSupervisionCodeBean.PageBean.ListBean> codeList, int position);
    }

}
