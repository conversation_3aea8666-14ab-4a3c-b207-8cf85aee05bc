package com.xyy.wms.pda.model.instorage.checkaccept

import com.xyy.utilslibrary.base.BaseModel
import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.api.ApiInAcceptService
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckAcceptBill
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckAcceptGoodsResult
import com.xyy.wms.pda.contract.instorage.checkaccept.CheckAcceptBillContract
import com.xyy.wms.pda.net.RetrofitCreateHelper
import io.reactivex.Observable

/**
 * Created by XyyMvpPdaTemplate on 03/04/2020 15:56
 */
class CheckAcceptBillModel : BaseModel(), CheckAcceptBillContract.ICheckAcceptBillModel {
  override fun getCheckOrder(): Observable<BaseResponseBean<MutableList<CheckAcceptBill>>> {
    return RetrofitCreateHelper.createApi(ApiInAcceptService::class.java).getCheckOrder()
      .compose(RxHelper.rxSchedulerHelper())
  }

  override fun doUnbindingForOrderCode(checkOrderCode: String): Observable<BaseResponseBean<Any>> {
    return RetrofitCreateHelper.createApi(ApiInAcceptService::class.java).doUnbindingForOrderCode(checkOrderCode)
      .compose(RxHelper.rxSchedulerHelper())
  }

  override fun getDetailsByOrderCode(checkOrderCode: String): Observable<BaseResponseBean<CheckAcceptGoodsResult>> {
    return RetrofitCreateHelper.createApi(ApiInAcceptService::class.java).getDetailsByOrderCode(checkOrderCode)
      .compose(RxHelper.rxSchedulerHelper())
  }

  companion object {
    fun newInstance(): CheckAcceptBillModel {
      return CheckAcceptBillModel()
    }
  }
}
