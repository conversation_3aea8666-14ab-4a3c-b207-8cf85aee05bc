package com.xyy.wms.pda.model

import com.xyy.utilslibrary.base.BaseModel
import com.xyy.wms.pda.api.*
import com.xyy.wms.pda.net.RetrofitCreateHelper

/**
 * 公用的Model，集中懒加载方式创建service
 */
abstract class ServiceModel : BaseModel() {

  val apiService: ApiService by lazy {
    RetrofitCreateHelper.getInstance().createRetrofit(ApiService::class.java)
  }
  val apiInAcceptService: ApiInAcceptService by lazy {
    RetrofitCreateHelper.getInstance().createRetrofit(ApiInAcceptService::class.java)
  }
  val apiInExitService: ApiInExitService by lazy {
    RetrofitCreateHelper.getInstance().createRetrofit(ApiInExitService::class.java)
  }
  val apiInManagerService: ApiInManagerService by lazy {
    RetrofitCreateHelper.getInstance().createRetrofit(ApiInManagerService::class.java)
  }
  val apiOutManagerService: ApiOutManagerService by lazy {
    RetrofitCreateHelper.getInstance().createRetrofit(ApiOutManagerService::class.java)
  }
  val apiPinBackService: ApiPinBackService by lazy {
    RetrofitCreateHelper.getInstance().createRetrofit(ApiPinBackService::class.java)
  }

}
