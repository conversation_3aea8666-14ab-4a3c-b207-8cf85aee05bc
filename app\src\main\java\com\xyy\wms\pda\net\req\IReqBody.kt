package com.xyy.wms.pda.net.req

import com.google.gson.GsonBuilder
import okhttp3.MediaType
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody

/**
 * Created by lwj on 2020-03-26.
 * <EMAIL>
 */
interface IReqBody {
    fun buildMediaType(): MediaType
    fun buildReqBody(): RequestBody

    companion object {
        @JvmField
        val JSON_MEDIA_TYPE: MediaType = "application/json; charset=UTF-8".toMediaType()
        @JvmField
        val FORM_MEDIA_TYPE: MediaType = "application/x-www-form-urlencoded".toMediaType()
        @JvmField
        val gson = GsonBuilder().serializeNulls().create()
    }
}