package com.xyy.wms.pda.contract.movestorage.car;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.common.RunningTaskResult;
import com.xyy.wms.pda.bean.moveStorage.CarNumberBean;
import com.xyy.wms.pda.bean.moveStorage.CarNumberResult;
import com.xyy.wms.pda.bean.moveStorage.StartTaskBean;
import com.xyy.wms.pda.bean.moveStorage.StartTaskResult;
import com.xyy.wms.pda.ui.activity.movestorage.car.CarDownActivity;

import io.reactivex.Observable;
/**
 * 下车
 */
public interface CarDownContract {
    //通过model获取数据
    interface CarDownModel extends IBaseModel {
        /**
         * 开启任务
         */
        Observable<BaseResponseBean<StartTaskResult>> startTask(StartTaskBean startTaskBean);
        /**
         * 进行中的任务
         */
        Observable<BaseResponseBean<RunningTaskResult>> getRunningTask();
        /**
         * 获取车牌
         */
        Observable<BaseResponseBean<CarNumberResult>> getCarNumber(CarNumberBean carNumberBean);
    }

    //success监听的方法
    interface CarDownView extends IBaseActivity {
        /**
         * 开启任务
         */
        void startTaskViewSuccess(BaseResponseBean<StartTaskResult> requestBaseBean);
        /**
         * 进行中的任务
         */
        void getRunningTaskViewSuccess(BaseResponseBean<RunningTaskResult> requestBaseBean);
        /**
         * 获取车牌
         */
        void getCarNumberViewSuccess(BaseResponseBean<CarNumberResult> requestBaseBean);
    }
    //被子类重写的方法
    abstract class CarDownPresenter extends BasePresenter<CarDownModel, CarDownActivity> {
        //开启任务
        public abstract void startTask(StartTaskBean startTaskBean);
        /**
         * 进行中的任务
         */
        public abstract void getRunningTask();
        /**
         * 获取车牌
         */
        public abstract void getCarNumber(CarNumberBean carNumberBean);
    }
}
