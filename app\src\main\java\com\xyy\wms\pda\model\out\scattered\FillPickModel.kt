package com.xyy.wms.pda.model.out.scattered


import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.out.pick.FillPickSubmitBean
import com.xyy.wms.pda.contract.out.scattered.FillPickContract
import com.xyy.wms.pda.model.ServiceModel
import io.reactivex.Observable

class FillPickModel : ServiceModel(), FillPickContract.IFillPickModel {

    override fun submitFillPickGoods(bean: FillPickSubmitBean): Observable<BaseResponseBean<*>> {
        return apiOutManagerService.submitFillPickGoods(bean).compose(RxHelper.rxSchedulerHelper())
    }
}
