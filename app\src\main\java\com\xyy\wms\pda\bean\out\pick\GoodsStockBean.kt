package com.xyy.wms.pda.bean.out.pick

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

/**
 * 商品库存信息
 */
@Parcelize
data class GoodsStockBean(
        // 新货位信息
        var amountUse: Int,// 可用库存
        var batchNumber: String?,//	批号
        var sterilizingBatchNumber: String?,//	灭菌批号
        var buildingCode: String?,//建筑物
        var channelCode: String?,//	业务类型	string	@mock=2
        var goodsAllocation: String?,//	货位	string	@mock=LDZ02-05
        var orgCode: String?,//	机构	string	@mock=D1375
        var ownerCode: String?,//	业主	string	@mock=009
        var productCode: String?,//	商品编码	string	@mock=Y3001006
        var productName: String?,//	商品名称	string	@mock=午时茶颗粒
        var productionDate: String?,//	生产日期	string	@mock=2019-01-15
        var specification: String?,//	件包装	string	@mock=60
        var storageAreaCode: String?,//	库区	string	@mock=LDZ
        var storageRoomCode: String?,//	库房	string	@mock=LDK
        var storageStatus: String?,//		number	@mock=1
        var storageTypeCode: String?,//	库别	string	@mock=ZJK
        var validityDate: String?,//	有效期	string	@mock=2022-01-15
        var warehouseCode: String?//	仓库
) : Parcelable
