package com.xyy.wms.pda.contract.instorage.checkaccept

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.*
import io.reactivex.Observable

/**
 * Created by XyyMvpPdaTemplate on 03/04/2020 16:01
 */
interface CheckAcceptGoodsDetailListContract {

    interface ICheckAcceptGoodsDetailListModel : IBaseModel {
        fun getDetailsByOrderCode(checkOrderCode: String): Observable<BaseResponseBean<CheckAcceptGoodsResult>>
        fun checkOrderSubmit(acceptDetailListPost: AcceptDetailListPost?): Observable<BaseResponseBean<List<AcceptSubmitResult>>>
        fun secondLogin(secondLoginPost: SecondLoginPost): Observable<BaseResponseBean<SecondLoginResult>>
    }

    interface ICheckAcceptGoodsDetailListView : IBaseActivity {
        fun getDetailsByOrderCodeSuccess(checkAcceptGoodsResult: CheckAcceptGoodsResult?)
        fun checkOrderSubmitSuccess()
        fun checkOrderSubmitFailure(response: List<AcceptSubmitResult>, msg: String)
        fun secondLoginSuccess(baseResponseBean: BaseResponseBean<SecondLoginResult>, postData: AcceptDetailListPost, hasReject: Boolean)
    }

}
