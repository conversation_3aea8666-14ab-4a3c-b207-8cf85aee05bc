package com.xyy.wms.pda.model.out.scattered

import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.out.PDAPartsTaskStatus
import com.xyy.wms.pda.bean.out.pick.*
import com.xyy.wms.pda.contract.out.scattered.ScatteredPickingGetTaskContract.IScatteredPickingGetTaskModel
import com.xyy.wms.pda.model.ServiceModel
import io.reactivex.Observable

/**
 * Created by XyyMvpPdaTemplate on 03/18/2019 09:56
 */
class ScatteredPickingGetTaskModel : ServiceModel(), IScatteredPickingGetTaskModel {

    /**
     * 单个周转箱  校验
     */
    override fun verifyStatusPassBox(map: Map<String, String>): Observable<BaseResponseBean<VerifyStatusPassBoxBean?>> {
        return apiOutManagerService.verifyStatusPassBox(map).compose(RxHelper.rxSchedulerHelper())
    }

    /**
     * 领取任务
     */
    override fun getTask(jobNumber: String): Observable<BaseResponseBean<GetTaskBean>> {
        return apiOutManagerService.getTask(jobNumber).compose(RxHelper.rxSchedulerHelper())
    }

    /**
     * 是否有拣货任务
     */
    override fun isHasPickTask(jobNumber: String): Observable<BaseResponseBean<IsHasTask?>> {
        return apiOutManagerService.isHasPickTask(jobNumber).compose(RxHelper.rxSchedulerHelper())
    }

    /**
     * 关联周转箱后 获取箱子数据
     */
    override fun getPickTaskAfterRelationBox(map: Map<String, String>): Observable<BaseResponseBean<PickTaskAfterBoxResult?>> {
        return apiOutManagerService.getPickTaskAfterRelationBox(map).compose(RxHelper.rxSchedulerHelper())
    }

    /**
     * 关联周转箱后 获取箱子数据
     */
    override fun isPDAPartsTaskForFinished(batchInspectionCode: String): Observable<BaseResponseBean<PDAPartsTaskStatus>> {
        return apiOutManagerService.isPDAPartsTaskForFinished(batchInspectionCode).compose(RxHelper.rxSchedulerHelper())
    }

    /**
     * 获取任务数 的数据
     */
    override fun getItemTaskNumber(): Observable<BaseResponseBean<ItemTaskBean?>> {
        return apiOutManagerService.itemTaskNumber.compose(RxHelper.rxSchedulerHelper())
    }

    companion object {
        @JvmStatic
        fun newInstance(): ScatteredPickingGetTaskModel {
            return ScatteredPickingGetTaskModel()
        }
    }
}
