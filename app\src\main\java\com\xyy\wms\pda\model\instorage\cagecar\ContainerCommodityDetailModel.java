package com.xyy.wms.pda.model.instorage.cagecar;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.cageCar.ContainerProductDetailBean;
import com.xyy.wms.pda.contract.instorage.cagecar.ContainerCommodityDetailContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.List;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 02/18/2020 14:01
 */
public class ContainerCommodityDetailModel extends BaseModel implements ContainerCommodityDetailContract.IContainerCommodityDetailModel {

    public static ContainerCommodityDetailModel newInstance() {
        return new ContainerCommodityDetailModel();
    }
    @Override
    public Observable<BaseResponseBean<List<ContainerProductDetailBean>>> getDetailsByContainer(String containerCode) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).getDetailsByContainer(containerCode)
                .compose(RxHelper.rxSchedulerHelper());
    }
}