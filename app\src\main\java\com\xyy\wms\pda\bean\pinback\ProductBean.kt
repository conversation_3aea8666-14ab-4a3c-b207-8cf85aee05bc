package com.xyy.wms.pda.bean.pinback

import com.google.gson.annotations.SerializedName

import java.io.Serializable

/**
 * Created by lx on 2018/11/5 18:43
 * E-Mail：<EMAIL>
 * 销退，复查，电子监管，上架的 商品数据 公用bean
 */
class ProductBean : Serializable, Cloneable {

  /**
   * reason : 快过期了
   * isSupervise : null
   * randomCheckNum : null
   * validDate : *************
   * manufacturerName :
   * checkAssess : null
   * colNameValue : null
   * produceTime : *************
   * specifications : 0.1g*7粒
   * uuid : null
   * productName : 开塞露（含甘油）
   * manufacturer : 山东仁和制药有限公司
   * storageClassification : 0
   * measures : null
   * yn : 1
   * orgCode : 003
   * rowNum : null
   * realPiecesNum : 8
   * id : 642
   * producingArea : 广东
   * packageNum : 120
   * packingUnit : 膏药
   * totalAccount : null
   * colName : null
   * orgName : null
   * productId : A00209
   * batchNum : A00209-001
   * updateUser : 359
   * updateTime : *************
   * planPosition : null
   * customerName : 客户1
   * checkCode : 00104
   * realReturnNum : 960
   * warehouseTypeName : null
   * returnNum : 960
   * createTime : *************
   * createUser : 359
   * containerCode : 930808
   * checkId : null
   * realScatteredNum : 0
   */
  //行号，用来记录开始的顺序。
  var lineNumber: Int = 0

  @SerializedName("validDate")
  var validDate: String = ""

  @SerializedName("checkAssess")
  var checkAssess: Int = 0

  @SerializedName("produceTime")
  var produceTime: String = ""

  @SerializedName("specifications")
  var specifications: String? = null

  @SerializedName("uuid")
  var uuid: Any? = null

  @SerializedName("productName")
  var productName: String? = null

  @SerializedName("manufacturer")
  var manufacturer: String? = null

  @SerializedName("storageClassification")
  var storageClassification: Int = 0

  @SerializedName("yn")
  var yn: Int = 0 // 商品的检验状态（验收，复查，扫描，上架）

  //机构代码
  @SerializedName("orgCode")
  var orgCode: String? = null

  @SerializedName("realPiecesNum")
  var realPiecesNum: Int = 0

  @SerializedName("id")
  var id: Int = 0

  @SerializedName("producingArea")
  var producingArea: String? = null

  @SerializedName("packageNum")
  var packageNum: Int = 0

  @SerializedName("packingUnit")
  var packingUnit: String? = null

  @SerializedName("productId")
  var productId: String? = null

  @SerializedName("batchNum")
  var batchNum: String? = null

  @SerializedName("updateUser")
  var updateUser: String? = null

  @SerializedName("updateTime")
  var updateTime: String = ""

  @SerializedName("planPosition")
  var planPosition: String? = null
  var already_shelf_number: Int = 0

  @SerializedName("realReturnNum")
  var realReturnNum: Int = 0

  @SerializedName("warehouseTypeName")
  var warehouseTypeName: String? = null

  @SerializedName("createTime")
  var createTime: String = ""

  @SerializedName("createUser")
  var createUser: String? = null

  @SerializedName("containerCode")
  var containerCode: String? = null

  @SerializedName("checkId")
  var checkId: Any? = null

  @SerializedName("realScatteredNum")
  var realScatteredNum: Int = 0

  @SerializedName("storageRoomCode")
  var storageRoomCode: String? = null // 库房编码

  //电子监管单码单号
  var code: String? = null

  //中包装数量
  //生产批号
  var productBatchCode: String? = null

  //商品编码
  var productCode: String? = null

  //有效期至；日期格式(yyyy-MM-dd HH:mm)
  var productValidDate: String = ""

  //监管码
  var regulatoryCode: String? = null

  //容器下所有库区，逗号隔开
  var storageAreaCodes: String? = null

  //推荐货位所属库区
  var storageAreaCode: String? = null
  var inboundCode: String? = null    //上架单号	string	@mock=$order('123','123')
  var inboundNum: String? = null    //上架数量	string	@mock=$order('222','222')
  var realPosition: String? = null//	实际上架货位	string	@mock=$order('3123qa','3123qa')
  var warehouseTypeNameShow: String? = null// 库别 名字展示
  var middleNum: String? = null    //中包装数量	number	中包装数量
  var status: Int = 0// 1 已上架 0 未上架
  var sterilizingBatchNumber: String? = null //

  var returnOrganization: String? = null // 退货单位

  @Throws(CloneNotSupportedException::class)
  public override fun clone(): ProductBean {
    return super.clone() as ProductBean
  }

}
