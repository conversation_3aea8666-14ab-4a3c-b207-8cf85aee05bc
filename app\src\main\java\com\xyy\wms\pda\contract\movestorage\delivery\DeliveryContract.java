package com.xyy.wms.pda.contract.movestorage.delivery;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.common.RunningTaskResult;
import com.xyy.wms.pda.bean.moveStorage.CarNumberBean;
import com.xyy.wms.pda.bean.moveStorage.CarNumberResult;
import com.xyy.wms.pda.bean.moveStorage.DeliveryBean;
import com.xyy.wms.pda.bean.moveStorage.StartTaskBean;
import com.xyy.wms.pda.bean.moveStorage.StartTaskResult;
import com.xyy.wms.pda.ui.activity.movestorage.delivery.DeliveryActivity;

import io.reactivex.Observable;

/**
 * 下车
 */
public interface DeliveryContract {
    //通过model获取数据
    interface DeliveryModel extends IBaseModel {
        /**
         * 开启任务
         */
        Observable<BaseResponseBean<Boolean>> registration(DeliveryBean deliveryBean);
    }

    //success监听的方法
    interface DeliveryView extends IBaseActivity {
        /**
         * 开启任务
         */
        void registrationViewSuccess(BaseResponseBean<Boolean> responseBean);
    }
    //被子类重写的方法
    abstract class DeliveryPresenter extends BasePresenter<DeliveryModel, DeliveryActivity> {
        //开启任务
        public abstract void registration(DeliveryBean deliveryBean);
    }
}
