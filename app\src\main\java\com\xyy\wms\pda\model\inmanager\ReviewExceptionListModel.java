package com.xyy.wms.pda.model.inmanager;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.inmanager.ReviewExceptionListBean;
import com.xyy.wms.pda.bean.inmanager.post.ReviewExceptionPostBean;
import com.xyy.wms.pda.contract.inmanager.ReviewExceptionListContract;
import com.xyy.wms.pda.model.ServiceModel;

import io.reactivex.Observable;

/**
 * Created by zcj on 2018/11/14 10
 */
public class ReviewExceptionListModel extends ServiceModel implements ReviewExceptionListContract.IReviewExceptionListModel {

    public static ReviewExceptionListModel newInstance() {
        return new ReviewExceptionListModel();
    }

    @Override
    public Observable<BaseResponseBean<ReviewExceptionListBean>> exceptionPage(ReviewExceptionPostBean bean) {
        return getApiInManagerService().exceptionPage(bean).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<String>> updateException(String dealMode,
                                                                String dealUserName,
                                                                String id,
                                                                String isAskFor,
                                                                String status, String remark) {
        return getApiInManagerService().updateException(dealMode, dealUserName, id, isAskFor, status, remark).compose(RxHelper.rxSchedulerHelper());
    }
}
