package com.xyy.wms.pad.instorage.presenter.newinspection

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.wms.pad.instorage.model.newinstorage.CommodityInformationMaintenanceModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.GetUpperLowerProductInfoMaintainBean
import com.xyy.wms.pda.bean.instorage.checkaccept.GetUpperLowerProductInfoMaintainPost
import com.xyy.wms.pda.bean.instorage.checkaccept.LogicalRegionProductInfoMaintainBean
import com.xyy.wms.pda.bean.instorage.checkaccept.LogicalRegionProductInfoMaintainPost
import com.xyy.wms.pda.bean.instorage.checkaccept.ProductInfoMaintainQueryBean
import com.xyy.wms.pda.bean.instorage.checkaccept.ProductInfoMaintainQueryPost
import com.xyy.wms.pda.bean.instorage.checkaccept.SaveProductInfoMaintainPost
import com.xyy.wms.pda.contract.instorage.checkaccept.CommodityInformationMaintenanceContract
import com.xyy.wms.pda.helper.SimpleErrorConsumer
import com.xyy.wms.pda.helper.SimpleSuccessConsumer
import com.xyy.wms.pda.presenter.instorage.checkaccept.CheckAcceptBillPresenter
import java.util.ArrayList

class CommodityInformationMaintenancePresenter : BasePresenter<CommodityInformationMaintenanceModel, CommodityInformationMaintenanceContract.CommodityInformationMaintenanceView>() {
    override fun getModel(): CommodityInformationMaintenanceModel {
        return CommodityInformationMaintenanceModel.newInstance()
    }
    companion object {
      fun newInstance(): CommodityInformationMaintenancePresenter {
        return CommodityInformationMaintenancePresenter()
      }
    }
    //信息查询
    fun queryProductInfoMaintain(productInfoMaintainQueryPost : ProductInfoMaintainQueryPost) {
      mRxManager.register(mIModel.queryProductInfoMaintain(productInfoMaintainQueryPost)
        .subscribe(object : SimpleSuccessConsumer<BaseResponseBean<ProductInfoMaintainQueryBean>>(mIView,"保存中") {
          override fun onSuccess(t: BaseResponseBean<ProductInfoMaintainQueryBean>) {
            mIView.queryProductInfoMaintainSuccess(t)
          }
        }, SimpleErrorConsumer(mIView)))
    }
    //库存上下限
    fun getUpperLowerProductInfoMaintain(getUpperLowerProductInfoMaintainPost: GetUpperLowerProductInfoMaintainPost, isWhole: Boolean) {
      mRxManager.register(mIModel.getUpperLowerProductInfoMaintain(getUpperLowerProductInfoMaintainPost)
        .subscribe(object : SimpleSuccessConsumer<BaseResponseBean<GetUpperLowerProductInfoMaintainBean>>(mIView,"保存中") {
          override fun onSuccess(t: BaseResponseBean<GetUpperLowerProductInfoMaintainBean>) {
            mIView.getUpperLowerProductInfoMaintainSuccess(t.result, isWhole)
          }
        }, SimpleErrorConsumer(mIView)))
    }
    fun logicalRegionProductInfoMaintain(logicalRegionProductInfoMaintainPost: LogicalRegionProductInfoMaintainPost, isWhole: Boolean) {
      mRxManager.register(mIModel.logicalRegionProductInfoMaintain(logicalRegionProductInfoMaintainPost)
        .subscribe(object : SimpleSuccessConsumer<BaseResponseBean<ArrayList<LogicalRegionProductInfoMaintainBean>>>(mIView,"保存中") {
          override fun onSuccess(t: BaseResponseBean<ArrayList<LogicalRegionProductInfoMaintainBean>>) {
            mIView.logicalRegionProductInfoMaintainSuccess(t.result, isWhole)
          }
        }, SimpleErrorConsumer(mIView)))
    }

    fun saveProductInfoMaintain(saveProductInfoMaintainPost: SaveProductInfoMaintainPost) {
      mRxManager.register(mIModel.saveProductInfoMaintain(saveProductInfoMaintainPost)
        .subscribe(object : SimpleSuccessConsumer<BaseResponseBean<String>>(mIView,"保存中") {
          override fun onSuccess(t: BaseResponseBean<String>) {
            mIView.saveProductInfoMaintainSuccess(t.result)
          }
        }, SimpleErrorConsumer(mIView)))
    }
}

