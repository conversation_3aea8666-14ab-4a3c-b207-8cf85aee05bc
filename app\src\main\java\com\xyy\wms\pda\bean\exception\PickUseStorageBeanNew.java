package com.xyy.wms.pda.bean.exception;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * created by  liangxing  on 2019-12-18.
 */
public class PickUseStorageBeanNew implements Serializable, Cloneable {
    private String exceptionTaskCode;
    private List<PickUseStorageBean> itemList;

    public String getExceptionTaskCode() {
        return exceptionTaskCode;
    }

    public void setExceptionTaskCode(String exceptionTaskCode) {
        this.exceptionTaskCode = exceptionTaskCode;
    }

    public List<PickUseStorageBean> getItemList() {
        return itemList;
    }

    public void setItemList(List<PickUseStorageBean> itemList) {
        this.itemList = itemList;
    }
}
