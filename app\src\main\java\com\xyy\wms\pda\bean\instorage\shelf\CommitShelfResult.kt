package com.xyy.wms.pda.bean.instorage.shelf

import java.io.Serializable

/**
 *  上架单个 提交返回的结果
 *
 *  1：进行中；
 *  2：已完成（如果状态=2：说明该容器任务已全部完成，
 *  返回商品列表页，提示“当前容器内上架任务已全部完成！
 *  ”页面内容清空可重新扫描容器； 如果状态=1：则需要判断terminalListDetailVo是否存在：
 *  如不存在则返回商品列表页重新调用容器编号获取上架单的接口。
 *  如terminalListDetailVo对象存在，
 *  则根据集合定位到一个未上架的商品详情）
 */
data class CommitShelfResult(
        val storageOrderStatus: Int = -1,
        val terminalListDetailVo: StorageOrderBean?
) : Serializable