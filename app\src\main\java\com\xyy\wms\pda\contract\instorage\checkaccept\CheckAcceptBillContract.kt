package com.xyy.wms.pda.contract.instorage.checkaccept

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckAcceptBill
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckAcceptGoodsResult

import io.reactivex.Observable

/**
 * Created by XyyMvpPdaTemplate on 03/04/2020 15:56
 */
interface CheckAcceptBillContract {
  interface ICheckAcceptBillModel : IBaseModel {
    fun getCheckOrder(): Observable<BaseResponseBean<MutableList<CheckAcceptBill>>>
    fun doUnbindingForOrderCode(checkOrderCode: String): Observable<BaseResponseBean<Any>>
    fun getDetailsByOrderCode(checkOrderCode: String): Observable<BaseResponseBean<CheckAcceptGoodsResult>>
  }

  interface ICheckAcceptBillView : IBaseActivity {
    fun getCheckOrderSuccess(baseResponseBean: BaseResponseBean<MutableList<CheckAcceptBill>>?)
    fun doUnbindingForOrderCodeSuccess(baseResponseBean: BaseResponseBean<*>)
    fun getDetailsByOrderCodeStatus(baseResponseBean: BaseResponseBean<*>,checkOrderCode:String)
  }
}
