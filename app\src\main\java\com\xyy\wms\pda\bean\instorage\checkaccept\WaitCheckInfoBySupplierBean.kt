package com.xyy.wms.pda.bean.instorage.checkaccept

import java.io.Serializable

// 数据类型，按用户要求定义字段
// 供应商待验信息返回项
// 注意：所有字段均为字符串类型

data class WaitCheckInfoBySupplierBean(
    var supplierCode: String, // 供应商编码
    var supplierName: String, // 供应商名称
    var temporaryLocations: String, // 临存位多个用逗号分隔
    var waitVarietyNumber: String, // 待验品种数
    var waitCheckDetailNum: String, // 未提交条目数
    var unsubmittedContainerNumber: String, // 未提交容器数
    var ownerCode: String, // 业主编码
    var ownerName: String, // 业主名称
    var channelCode: String // 渠道编码
) : Serializable