package com.xyy.wms.pda.bean.instorage.cageCar
import java.io.Serializable

/**
 * 一级bean
 */
data class CageCarShelfResult(
        val pdaMsg: String?,        //string
        val purchaseStorageOrderTerminalListDetailVoList: MutableList<CageCarOrderBean>?,   // 商品列表    array<object>
        val rollContainerCode: String?,    //笼车编号	string	@mock=111
        val storageAreaCodes: String?,    //所在区域	string	@mock=LHF
        val storageOrderStatus:Int?,	//笼车上架状态	number	(1:未上架;2:已上架;) 数字
        val supplierName: String? = null    //供应商名称	string	@mock=供应商1
): Serializable