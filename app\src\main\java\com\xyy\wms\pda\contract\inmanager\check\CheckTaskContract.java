package com.xyy.wms.pda.contract.inmanager.check;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.inmanager.check.CheckTaskNumBean;

import io.reactivex.Observable;

public interface CheckTaskContract {
    abstract class CheckTaskPresenter extends BasePresenter<CheckTaskContract.ICheckPersenterModel, CheckTaskContract.ICheckPersenterView> {
        /**
         * 获取盘点-初盘数量-复盘数量-终盘数量
         */
        public abstract void selectCheckTaskNums();
    }

    interface ICheckPersenterModel extends IBaseModel {
        Observable<BaseResponseBean<CheckTaskNumBean>> selectCheckTaskNums();
    }

    interface ICheckPersenterView extends IBaseActivity {
        /**
         * 获取列表成功
         */
        void selectCheckTaskNumsSuccess(BaseResponseBean<CheckTaskNumBean> bean);

        /**
         * 显示网络错误
         */
        void showNetworkError();

    }
}
