<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/white_layout_bg">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingBottom="12dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="选择逻辑区域"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/text_color_333333" />

        <TextView
            android:id="@+id/tv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="关闭"
            android:textSize="14sp"
            android:textColor="@color/text_color_666666"
            android:padding="8dp"
            android:background="?android:attr/selectableItemBackground" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/gray_bg_ebedf1" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_commodity"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:layout_marginTop="12dp"
        android:scrollbars="vertical" />

</LinearLayout>

