package com.xyy.wms.pda.model.instorage.cagecar;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.cageCar.BindingForRollPost;
import com.xyy.wms.pda.bean.instorage.cageCar.ContainerCageCar;
import com.xyy.wms.pda.contract.instorage.cagecar.ContainerCageCarContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import io.reactivex.Observable;
/**
 * Created by XyyMvpPdaTemplate on 02/18/2020 13:55
 * 容器绑定笼车
 */
public class ContainerCageCarModel extends BaseModel implements ContainerCageCarContract.IContainerCageCarModel {

    public static ContainerCageCarModel newInstance() {
        return new ContainerCageCarModel();
    }

    @Override
    public Observable<BaseResponseBean<ContainerCageCar>> getStorageContainersInfo(String containerCode, String rollContainerCode) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).getStorageContainersInfo(containerCode,rollContainerCode)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean> doBindingForRoll(BindingForRollPost bindingForRollPost) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).doBindingForRoll(bindingForRollPost)
                .compose(RxHelper.rxSchedulerHelper());
    }
}