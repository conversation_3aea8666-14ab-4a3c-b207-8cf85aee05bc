package com.xyy.wms.pda.bean.instorage

/**
 * 图片信息模型
 * 用于异常图片上传功能
 */
data class PictureInfo(
    var id: String = "",                     // 图片ID
    var localPath: String = "",              // 本地路径
    var serverUrl: String = "",              // 服务器URL
    var isLocal: Boolean = false,            // 是否本地图片
    var isPlaceholder: Boolean = true,       // 是否占位符
    var uploadStatus: UploadStatus = UploadStatus.NONE,  // 上传状态
    var fileSize: Long = 0,                  // 文件大小
    var createTime: Long = System.currentTimeMillis()   // 创建时间
)

/**
 * 上传状态枚举
 */
enum class UploadStatus {
    NONE,        // 未上传
    UPLOADING,   // 上传中
    SUCCESS,     // 上传成功
    FAILED       // 上传失败
}
