package com.xyy.wms.pda.bean.inmanager;

import java.io.Serializable;

/**
 * 补货
 */
public class ReplenishmentBean implements Serializable {

    private String barCode;//    标签条码	string
    private String batchNumber;//    批号	string
    private String manufacturer;//生产厂家	string
    private String numberOfPlans;//实际件数 整件	number
    private String packingQuantity;//    包装规格（包装数量）	number
    private String packingUnit;//    包装单位	string
    private String plannedQuantity;//实际数量 零散	number
    private String produceTime;//    生产日期	string
    private String productionDate;  //生产日期-新
    private String productCode;//    商品编码	string
    private String productName;//    商品名称	string
    private String purposeGoodsAllocation;//	目的货位	string
    private String sourceGoodsAllocation;//  下架货位	string
    private String specifications;//    规格	string
    private String specification; //包装规格-新
    private String validDate;//有效期至	string
    private String validityDate; //有效期 -新
    private String replenishmentStatus;// 2 下架 4 上架
    private Integer replenishAmount; //拣货数量

    // 生产日期(PDA展示)
    private String produceTimeShow;
    // 有效期至(PDA展示)
    private String validDateShow;
    // 工作区状态 0  无权限 1正常
    private int permissionArea = 1;

    public WorkStatus getWorkStatus() {
        WorkStatus workStatus;
        if (permissionArea == 0) {
            workStatus = WorkStatus.NO_PERMISSION;
        } else {
            workStatus = WorkStatus.NORMAL;
        }
        return workStatus;
    }

    public enum WorkStatus {
        NO_PERMISSION,
        NORMAL
    }

    public String getSpecification(){
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public Integer getReplenishAmount() {
        return replenishAmount;
    }

    public void setReplenishAmount(Integer replenishAmount) {
        this.replenishAmount = replenishAmount;
    }

    public String getValidityDate() {
        return validityDate;
    }

    public void setValidityDate(String validityDate) {
        this.validityDate = validityDate;
    }

    public String getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(String productionDate) {
        this.productionDate = productionDate;
    }

    public int getPermissionArea() {
        return permissionArea;
    }

    public void setPermissionArea(int permissionArea) {
        this.permissionArea = permissionArea;
    }

    public String getProduceTimeShow() {
        return produceTimeShow;
    }

    public void setProduceTimeShow(String produceTimeShow) {
        this.produceTimeShow = produceTimeShow;
    }

    public String getValidDateShow() {
        return validDateShow;
    }

    public void setValidDateShow(String validDateShow) {
        this.validDateShow = validDateShow;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getBatchNumber() {
        return batchNumber;
    }

    public void setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getNumberOfPlans() {
        return numberOfPlans;
    }

    public void setNumberOfPlans(String numberOfPlans) {
        this.numberOfPlans = numberOfPlans;
    }

    public String getPackingQuantity() {
        return packingQuantity;
    }

    public void setPackingQuantity(String packingQuantity) {
        this.packingQuantity = packingQuantity;
    }

    public String getPackingUnit() {
        return packingUnit;
    }

    public void setPackingUnit(String packingUnit) {
        this.packingUnit = packingUnit;
    }

    public String getPlannedQuantity() {
        return plannedQuantity;
    }

    public void setPlannedQuantity(String plannedQuantity) {
        this.plannedQuantity = plannedQuantity;
    }

    public String getProduceTime() {
        return produceTime;
    }

    public void setProduceTime(String produceTime) {
        this.produceTime = produceTime;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getPurposeGoodsAllocation() {
        return purposeGoodsAllocation;
    }

    public void setPurposeGoodsAllocation(String purposeGoodsAllocation) {
        this.purposeGoodsAllocation = purposeGoodsAllocation;
    }

    public String getSourceGoodsAllocation() {
        return sourceGoodsAllocation;
    }

    public void setSourceGoodsAllocation(String sourceGoodsAllocation) {
        this.sourceGoodsAllocation = sourceGoodsAllocation;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getValidDate() {
        return validDate;
    }

    public void setValidDate(String validDate) {
        this.validDate = validDate;
    }

    public String getReplenishmentStatus() {
        return replenishmentStatus;
    }

    public void setReplenishmentStatus(String replenishmentStatus) {
        this.replenishmentStatus = replenishmentStatus;
    }

    @Override
    public String toString() {
        return "ReplenishmentBean{" +
            "barCode='" + barCode + '\'' +
            ", batchNumber='" + batchNumber + '\'' +
            ", manufacturer='" + manufacturer + '\'' +
            ", numberOfPlans='" + numberOfPlans + '\'' +
            ", packingQuantity='" + packingQuantity + '\'' +
            ", packingUnit='" + packingUnit + '\'' +
            ", plannedQuantity='" + plannedQuantity + '\'' +
            ", produceTime='" + produceTime + '\'' +
            ", productCode='" + productCode + '\'' +
            ", productName='" + productName + '\'' +
            ", purposeGoodsAllocation='" + purposeGoodsAllocation + '\'' +
            ", sourceGoodsAllocation='" + sourceGoodsAllocation + '\'' +
            ", specifications='" + specifications + '\'' +
            ", validDate='" + validDate + '\'' +
            ", replenishmentStatus='" + replenishmentStatus + '\'' +
            '}';
    }
}
