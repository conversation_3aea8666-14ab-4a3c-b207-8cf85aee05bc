<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar_check_accept"
        android:layout_width="match_parent"
        android:layout_height="@dimen/toolbar_height"
        android:layout_gravity="center"
        android:background="?attr/colorPrimary"
        android:fitsSystemWindows="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_scrollFlags="scroll|enterAlways"
        app:navigationIcon="@mipmap/ic_arrow_back_white"
        app:popupTheme="@style/AppTheme.ToolbarPopupOverlay">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="已验收商品明细"
            android:textColor="@color/white"
            android:textSize="@dimen/title_text_size" />
    </androidx.appcompat.widget.Toolbar>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">
        <LinearLayout
            android:id="@+id/ll_head"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/item_height"
                android:gravity="center_vertical"
                android:orientation="horizontal">
                <TextView
                    android:layout_width="@dimen/dp80"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp20"
                    android:text="关键字查询"
                    android:textColor="@color/text_color_333333"
                    android:textSize="@dimen/sp14" />
                <EditText
                    android:id="@+id/ed_keyword_query"
                    style="@style/et_scan_code_style"
                    android:layout_marginStart="@dimen/dp12"
                    android:layout_marginEnd="@dimen/dp12"
                    android:hint="商品助记码/商品名称/商品条码" />

            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/item_height"
                android:gravity="center_vertical"
                android:orientation="horizontal">
                <TextView
                    android:layout_width="@dimen/dp80"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp20"
                    android:text="容器号"
                    android:textColor="@color/text_color_333333"
                    android:textSize="@dimen/sp14" />
                <EditText
                    android:id="@+id/ed_container_no"
                    style="@style/et_scan_code_style"
                    android:layout_marginStart="@dimen/dp12"
                    android:layout_marginEnd="@dimen/dp12"
                    android:hint="容器号" />
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="end|right"
                >
                <Button
                    android:id="@+id/tv_search"
                    android:layout_width="50dp"
                    android:layout_height="27dp"
                    android:layout_marginEnd="@dimen/dp12"
                    android:text="查询"
                    android:textColor="@color/white"
                    android:background="@drawable/btn_submit_bg"
                    />
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="horizontal"
                android:layout_height="@dimen/dp46"
                android:background="@color/color_ffebedf1">
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0.5"
                    android:text="序号"
                    android:textColor="@color/color_ff_333333"
                    android:textSize="@dimen/sp14"
                    android:gravity="center" />
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="2">
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center_horizontal"
                        android:text="商品编码"
                        android:textColor="@color/color_ff_102442"
                        android:textSize="@dimen/sp14" />
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="商品名称"
                        android:textColor="@color/color_ff_333333"
                        android:textSize="@dimen/sp14"
                        android:gravity="center_horizontal" />
                </LinearLayout>
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:text="商品批号"
                    android:textColor="@color/color_ff_333333"
                    android:textSize="@dimen/sp14"
                    android:gravity="center" />
            </LinearLayout>
        </LinearLayout>
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/checkAcceptBill_recyclerView_checked"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/ll_head"
            android:layout_marginStart="@dimen/dp5"
            android:layout_marginTop="1dp"
            android:layout_marginBottom="5dp" />
    </RelativeLayout>
</LinearLayout>