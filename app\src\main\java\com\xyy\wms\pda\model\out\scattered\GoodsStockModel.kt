package com.xyy.wms.pda.model.out.scattered

import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.out.pick.GoodsStockBean
import com.xyy.wms.pda.contract.out.scattered.GoodsStockContract
import com.xyy.wms.pda.model.ServiceModel
import io.reactivex.Observable

class GoodsStockModel : ServiceModel(), GoodsStockContract.IGoodsStockModel {

    override fun getGoodsStock(map: Map<String, Any?>): Observable<BaseResponseBean<List<GoodsStockBean>>> {
        return apiOutManagerService.getGoodsStock(map).compose(RxHelper.rxSchedulerHelper())
    }
}
