package com.xyy.wms.pda.contract.out.scattered

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.out.PDAPartsTaskStatus
import com.xyy.wms.pda.bean.out.pick.*
import io.reactivex.Observable

/**
 * Created by XyyMvpPdaTemplate on 03/18/2019 09:56
 */
interface ScatteredPickingGetTaskContract {
  interface IScatteredPickingGetTaskModel : IBaseModel {
    fun verifyStatusPassBox(map: Map<String, String>): Observable<BaseResponseBean<VerifyStatusPassBoxBean?>>
    fun getTask(jobNumber: String): Observable<BaseResponseBean<GetTaskBean>>
    fun isHasPickTask(jobNumber: String): Observable<BaseResponseBean<IsHasTask?>>
    fun getPickTaskAfterRelationBox(map: Map<String, String>): Observable<BaseResponseBean<PickTaskAfterBoxResult?>>
    fun isPDAPartsTaskForFinished(batchInspectionCode: String): Observable<BaseResponseBean<PDAPartsTaskStatus>>
    fun getItemTaskNumber(): Observable<BaseResponseBean<ItemTaskBean?>>
  }

  interface IScatteredPickingGetTaskView : IBaseActivity {
    fun verifyStatusPassBoxSuccess(boxBean: VerifyStatusPassBoxBean?)
    fun getTaskSuccess(taskBean: GetTaskBean)
    fun isHasPickTaskSuccess(isHasTask: IsHasTask?)
    fun getPickTaskAfterRelationBoxSuccess(pickTaskAfterBoxResult: PickTaskAfterBoxResult?, updateTaskStatus: Boolean)
    fun isPDAPartsTaskForFinishedSuccess(pdaPartsTaskStatus: PDAPartsTaskStatus, batchInspectionCode: String)
    fun getItemTaskNumberSuccess(itemTaskBean: ItemTaskBean?)
  }

}
