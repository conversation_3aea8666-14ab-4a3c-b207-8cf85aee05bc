package com.xyy.wms.pda.model.personal;

import androidx.annotation.NonNull;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.contract.personal.SettingContract;
import com.xyy.wms.pda.model.ServiceModel;

import io.reactivex.Observable;

public class SettingModel extends ServiceModel implements SettingContract.ISettingModel {

    @NonNull
    public static SettingModel newInstance() {
        return new SettingModel();
    }

    @Override
    public Observable<BaseResponseBean> logout() {
        return getApiService().logout().compose(RxHelper.rxSchedulerHelper());
    }
}
