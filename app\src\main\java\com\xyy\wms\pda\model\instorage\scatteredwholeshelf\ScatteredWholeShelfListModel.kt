package com.xyy.wms.pda.model.instorage.scatteredwholeshelf

import com.xyy.utilslibrary.base.BaseModel
import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.api.ApiInExitService
import com.xyy.wms.pda.api.ApiService
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.shelf.InShelfResult
import com.xyy.wms.pda.bean.productCode.ProductCode
import com.xyy.wms.pda.contract.instorage.scatteredwholeshelf.ScatteredWholeShelfListContract
import com.xyy.wms.pda.model.productCode.GetProductCodeModel
import com.xyy.wms.pda.net.RetrofitCreateHelper
import io.reactivex.Observable
/**
 * 零散列表上架
 */
class ScatteredWholeShelfListModel : BaseModel(), ScatteredWholeShelfListContract.IScatteredWholeShelfListModel, GetProductCodeModel {

    override fun getScatteredShelfList(containerCode: String, shelfType: Int): Observable<BaseResponseBean<InShelfResult>> {
        return RetrofitCreateHelper.createApi(ApiInExitService::class.java).getStorageOrder(containerCode, shelfType)
                .compose(RxHelper.rxSchedulerHelper())
    }
    override fun getProductBarCode(ownerCode: String, packageBarCode: String): Observable<BaseResponseBean<List<ProductCode>>> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).selectProductPda(ownerCode, packageBarCode)
                .compose(RxHelper.rxSchedulerHelper())
    }
    companion object {
        fun newInstance(): ScatteredWholeShelfListModel {
            return ScatteredWholeShelfListModel()
        }
    }
}
