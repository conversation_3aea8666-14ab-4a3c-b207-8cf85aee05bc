package com.xyy.wms.pda.bean.purchaseexit;

import java.io.Serializable;

/**
 * Created by lwj on 2020-02-24.
 * <EMAIL>
 */
public class PurchaseExitReViewListBean implements Serializable {

    public String buildingCode;//	string	@mock=$order('','01','01')
    public String buildingName;//		建筑物
    public String createTime;//
    public String delivery;//
    public String deliveryMethod;//
    public String deliveryMode;//
    public String deliveryModeDesc;//
    public String deliveryPhone;//
    public String erpRefundOrderCode;//
    public String id;//
    public String orgCode;//
    public String ownerCode;//
    public String ownerName;//
    public String pickUpOrder;//	单据编号	string	@mock=$order('JHG201912090006','JHG201912090007','JHG201912130015')
    public String productType;//
    public String purchaseUser;//
    public String purchaseUserName;//
    public String recheckUser;//
    public String recheckUserName;//
    public String refundOrderCode;//
    public String refundOrderStatus;//		状态 4未执行 6 执行中	string	@mock=$order('1','2','1')
    public String reviewRemark;//
    public String storageAddress;//
    public String supplierCode;//
    public String supplierName;//

}
       
