package com.xyy.wms.pda.presenter.login;

import androidx.annotation.NonNull;

import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.user.LoginInfo;
import com.xyy.wms.pda.contract.login.LoginContract;
import com.xyy.wms.pda.helper.SimpleErrorConsumer;
import com.xyy.wms.pda.helper.SimpleSuccessConsumer;
import com.xyy.wms.pda.model.login.LoginModel;
import com.xyy.wms.pda.bean.req.LoginReq;

public class LoginPresenter extends LoginContract.LoginPresenter {

    @NonNull
    public static LoginPresenter newInstance() {
        return new LoginPresenter();
    }

    @Override
    protected LoginContract.ILoginModel getModel() {
        return LoginModel.newInstance();
    }

    @Override
    public void login(LoginReq req) {
        if (mIView == null || mIModel == null)
            return;
        mRxManager.register(mIModel.login(req).subscribe(
            new SimpleSuccessConsumer<BaseResponseBean<String>>(mIView, "登录中...") {
                    @Override
                    public void onSuccess(BaseResponseBean<String> bean) {
                        mIView.showToast("登录成功");
                        mIView.loginSuccess(bean.getResult());
                    }
                }, new SimpleErrorConsumer(mIView)));
    }

}
