package com.xyy.wms.pda.model.inmanager;


import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.inmanager.ExceptionCountBean;
import com.xyy.wms.pda.contract.inmanager.ReviewExceptionContract;
import com.xyy.wms.pda.model.ServiceModel;

import java.util.List;

import io.reactivex.Observable;

public class ReviewExceptionModel extends ServiceModel implements ReviewExceptionContract.IReviewExceptionModel {

    public static ReviewExceptionModel newInstance() {
        return new ReviewExceptionModel();
    }

    @Override
    public Observable<BaseResponseBean<List<ExceptionCountBean>>> exceptionCount(String exceptionType) {
        return getApiInManagerService().exceptionCount(exceptionType).compose(RxHelper.rxSchedulerHelper());
    }
}
