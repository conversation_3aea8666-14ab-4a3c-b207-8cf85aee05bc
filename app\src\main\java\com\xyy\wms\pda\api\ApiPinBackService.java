package com.xyy.wms.pda.api;

import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.pinback.PostShelfListBean;
import com.xyy.wms.pda.bean.pinback.ProductBean;
import com.xyy.wms.pda.bean.pinback.ProductPosition;
import com.xyy.wms.pda.bean.pinback.ProductPositionListBean;
import com.xyy.wms.pda.bean.pinback.ShelfResult;

import java.util.List;

import io.reactivex.Observable;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * 销退API
 */
public interface ApiPinBackService {
    /**
     * 获取散件上架列表
     */
    @GET("instock/wmsApp/salereturnPda/queryProductListByContainerCode")
    Observable<BaseResponseBean<List<ProductBean>>> getPieceListByContainerCode(
            @Query("containerCode") String containerCode,
            @Query("type") int type);

    /**
     * 提交整件,散件上架单列表
     */
    @POST("instock/wmsApp/salereturnPda/upperShelf")
    Observable<BaseResponseBean<ShelfResult>> commitShelfList(@Body PostShelfListBean wholeShelfListBean);

    /**
     * 货位选择校验  上架货位( 零散和整体)
     */
    @POST("instock/wmsApp/salereturnPda/validateProductPosition")
    Observable<BaseResponseBean> checkSelectGoodsPosition(@Body ProductPositionListBean<ProductPosition> productPositionListBean);


    /**
     * 获取待上架容器数
     */
    @GET("instock/wmsApp/salereturnPda/waitInboundCount")
    Observable<BaseResponseBean<Integer>> waitInboundCount(@Query("taskType") String taskType);
}
