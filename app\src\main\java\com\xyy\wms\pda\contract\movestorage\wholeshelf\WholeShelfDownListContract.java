package com.xyy.wms.pda.contract.movestorage.wholeshelf;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.common.MoveStorageFinishedBean;
import com.xyy.wms.pda.bean.common.RunningTaskResult;
import com.xyy.wms.pda.bean.moveStorage.AddTaskDetailBean;
import com.xyy.wms.pda.bean.moveStorage.PalletGetGoodsBean;
import com.xyy.wms.pda.bean.moveStorage.PalletGetGoodsResult;
import com.xyy.wms.pda.bean.moveStorage.ResourcePositionGoodsBean;
import com.xyy.wms.pda.model.moveStorage.wholeshelfdown.WholeShelfDownListModel;
import com.xyy.wms.pda.ui.activity.movestorage.wholeshelfdown.MoveStorageShelfDownListActivity;

import java.util.List;

import io.reactivex.Observable;
/**
 * 整件上架列表
 */
public interface WholeShelfDownListContract {
    interface IWholeShelfDownListModel extends IBaseModel {
        /**
         * 商品列表
         */
        Observable<BaseResponseBean<List<PalletGetGoodsResult>>> getWholeShelfDownList(ResourcePositionGoodsBean resourcePositionGoodsBean);
        /**
         * 进行中的任务
         */
        Observable<BaseResponseBean<RunningTaskResult>> getRunningTask();
        /**
         * 下架完成
         */
        Observable<BaseResponseBean<Boolean>> setFinished(MoveStorageFinishedBean finishedBean);
        /**
         * 添加任务明细
         */
        Observable<BaseResponseBean<Boolean>> addTaskDetail(AddTaskDetailBean taskDetailBean);
    }

    interface IIWholeShelfDownListView extends IBaseActivity {
        /**
         * 获取商品数据
         */
        void getWholeShelfDownListSuccess(BaseResponseBean<List<PalletGetGoodsResult>> requestBaseBean);

        /**
         * 进行中任务
         */
        void getRunningTaskViewSuccess(BaseResponseBean<RunningTaskResult> requestBaseBean);

        /**
         * 添加任务明细
         */
        void addTaskDetailViewSuccess(BaseResponseBean<Boolean> requestBaseBean);

        /**
         * 下架完成
         */
        void setFinishedViewSuccess(BaseResponseBean<Boolean> requestBaseBean);

    }
    abstract class WholeShelfDownListPresenter extends BasePresenter<WholeShelfDownListModel, MoveStorageShelfDownListActivity> {
        /**
         * 商品列表
         */
        public abstract void getWholeShelfDownList(ResourcePositionGoodsBean resourcePositionGoodsBean);
        /**
         * 进行中的任务
         */
        public abstract void getRunningTask();
        /**
         * 添加任务明细
         */
        public abstract void addTaskDetail(AddTaskDetailBean taskDetailBean);
        /**
         * 下架完成
         */
        public abstract void setFinished(MoveStorageFinishedBean finishedBean);
    }
}
