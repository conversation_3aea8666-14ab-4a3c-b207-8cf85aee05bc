package com.xyy.wms.pda.contract.out.bind;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.exception.ExceptionHandleListBean;
import com.xyy.wms.pda.bean.out.bind.CheckResult;

import java.util.Map;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 04/10/2019 14:59
 */
public interface BindWallCodeContract {

    interface IBindWallCodeModel extends IBaseModel {
        Observable<BaseResponseBean<CheckResult>> checkWallCode(Map<String, Object> params);

        Observable<BaseResponseBean<CheckResult>> checkErpOrderCode(Map<String, Object> params);

        Observable<BaseResponseBean<CheckResult>> bindErpOrderCode(Map<String, Object> params);
    }

    interface IBindWallCodeView extends IBaseActivity {
        void checkWallCodeSuccess(BaseResponseBean<CheckResult> baseResponseBean);

        void checkErpOrderCodeSuccess(BaseResponseBean<CheckResult> baseResponseBean);

        void bindErpOrderCodeSuccess(BaseResponseBean<CheckResult> baseResponseBean);
    }

}
