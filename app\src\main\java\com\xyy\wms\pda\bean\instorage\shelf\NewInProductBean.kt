package com.xyy.wms.pda.bean.instorage.shelf

import java.io.Serializable

/**
 *      actualPosition	实际货位	string	@mock=$order('','','')
approvarNumbers	批准文号	string	@mock=$order('国药准字H44-123815','国药准字H2-1-184431','国药准字Z32-12-1621')
approvarStatus	审批状态	number	@mock=$order(-1,-1,-1)
checkOrderCode
commonName	药品通用名称
containerCode	容器编号	string	@mock=$order('1-1-1287','1-1-1287','1-1-1287')
createTime	创建时间	number	@mock=$order(1566297529-1-1-1,1566297529-1-1-1,1566297529-1-1-1)
createUser	创建人id	string	@mock=$order('4632-1','4632-1','4632-1')
departCode		string	@mock=$order('CQY-1-1-14','CQY-1-1-14','CQY-1-1-14')
differencesBigNumber
differencesScatterNumber
dosageForm	剂型	string	@mock=$order('溶液剂','注射剂','搽剂')
fromLineNumber	来源行号	number	@mock=$order(1,2,3)
id	主键id	number	@mock=$order(699,7-1-1,7-11)
largeCategory	商品大类	string	@mock=$order('外用','针剂','外用')
lineNumber	行号	number	@mock=$order(1,2,3)
lineNumberSplitOrigin	原始行号	number	@mock=$order(1,1,1)
manufacturer	生产厂家名称	string	@mock=$order('广东恒健制药有限公司','山东鲁抗医药股份有限公司','南通薄荷厂有限公司')
mediumPackageBarCode	中包装条码	string	@mock=$order('','','69133-12579127')
modifyReason
occupyType	单据占用类型	number	(1:PC端; 2:PDA)
orderType	单据类型
orgCode	机构编号	string	@mock=$order('-1-17','-1-17','-1-17')
packingUnit	包装单位	string	@mock=$order('盒','支','瓶')
pickingPath	拣货路径	number	@mock=$order(1,1,1)
piecePackageBarCode	件包装条码	string	@mock=$order('','','69433-12579127')
producingArea	商品产地	string	@mock=$order('重庆','重庆','重庆')
productBatchCode	商品批号	string	@mock=$order('181-116','1812-14','18-16-15')
productCode	商品编号	string	@mock=$order('Y3-1-12-121','A1-117-1-14','Y3-1-12-124')
productCountBig	收货件数	number	@mock=$order(-1,-1,-1)
productCountScatter	收货零散数	number	@mock=$order(2,3,4)
productCountSmall	收货数量	number	@mock=$order(2,3,4)
productManufactureDate	生产日期	string	@mock=$order('2-118-1-1--19','2-118-12-11','2-118--16-14')
productName	商品名称	string	@mock=$order('开塞露（含甘油）','注射用头孢他啶','风油精')
productPackingBigNumber	件包装数量	number	@mock=$order(6-1-1,1-1-1-1,36-1)
productPackingId	商品件包装规格id	string	@mock=$order('','','')
productPackingMiddleNumber	中包装数量	number	@mock=$order(2-1,1-1,2-1)
productSupervise	追溯码采集	number	(1:是;-1:否)
productSuperviseDesc	追溯码采集描述		(1:是;-1:否)
productTaxPrice		number	@mock=$order(1-1,1-1,1-1)
productType	商品类型	number	(-1:默认;1:中药;2:器械;3:赠品;4:非药)
productvaridDate	有效期至	string	@mock=$order('2-12-1-1-1-21','2-12-1-12-1-1','2-121--15-31')
purchaseLineNumber	采购订单行号	number	@mock=$order(1,2,3)
purchaseOrderCode	采购订单编号	string	@mock=$order('CGDD19-1522-1-1-11','CGDD19-1522-1-1-11','CGDD19-1522-1-1-11')
purchaseUser		string	@mock=$order('3987','3987','3987')
receiveLineNumber		number	@mock=$order(1,2,3)
receiveOrderCode
registrationCertificate	进口注册证号	string	@mock=$order('','','1231232')
rowid
shadingProperty	遮光属性 从商品表取值（1：避光 2：遮光 3：凉暗）	number	@mock=$order(-1,3,3)
shelfReceiveTime	上架领取时间	number	@mock=$order(6-1-1-1-1,6-1-1-1-1,6-1-1-1-1)
shelfUser	上架员id	string	@mock=$order('','','')
shelfUserName
shelvesCountBig	上架件数	number	@mock=$order(-1,-1,-1)
shelvesCountScatter	上架零散数	number	@mock=$order(2,3,4)
shelvesCountSmall	上架数量	number	@mock=$order(2,3,4)
showPosition	显示货位	string	@mock=$order('','','')
smallPackageBarCode	小包装条码	string	@mock=$order('69277621668881,692776213-13771','69-134472-15929','69533-12579127')
specifications	规格	string	@mock=$order('2-1ml','1.-1g/支','12毫升')
storageAreaCode	库区编号	string	@mock=$order('-11','-11','-11')
storageClassification	是否整散合一	string	-1:整散分开, 1:整散合一
storageConditions	存储条件	string	@mock=$order('7','8','8')
storageConditionsDesc	存储条件展示
storageOrderCode	入库单单据编号	string	@mock=$order('SJD19-182-1-1-17-1-116','SJD19-182-1-1-17-1-116','SJD19-182-1-1-17-1-116')
storageOrderStatus	入库单状态	number	1:未上架;2:已上架
storageOrderStatusDesc	入库单状态中文描述	1:未上架;2:已上架 （注：storageOrderStatus=1 并且 shelfUser=当前用户 “提交”按钮才可点击）
storageType
storageTypeArea	上架区域	string	@mock=$order('LHK--11','LHK--11','LHK--11')
storageTypeAreaRecommend	推荐上架库区编号	string	@mock=$order('-11','-11','-11')
storageTypeCode	库别编码	string	@mock=$order('LHK','LHK','LHK')
storageTypeName	库别中文	string	@mock=$order('LHK','LHK','LHK')
supplierCode
supplierName
updateTime	更新时间	number	@mock=$order(6-1-1-1-1,6-1-1-1-1,6-1-1-1-1)
updateUser	更新用户id	string	@mock=$order('','','')
yn	逻辑删除	number	-1:删除;1:未删除(有效)
 */
class NewInProductBean : Serializable, Cloneable {

  var actualPosition: String? = null
  var approvarNumbers: String? = null
  var approvarStatus: Int = -1
  var checkOrderCode: String? = null
  var commonName: String? = null
  var containerCode: String? = null
  var createTime: String? = null
  var createUser: String? = null
  var departCode: String? = null
  var differencesBigNumber: Int = -1
  var differencesScatterNumber: Int = -1
  var dosageForm: String? = null
  var fromLineNumber: Int = -1
  var id: Long = -1
  var largeCategory: String? = null
  var lineNumber: Int = -1
  var batchNumber: String ?= null
  var manufacturer: String? = null
  var mediumPackageBarCode: String? = null
  var modifyReason: String? = null
  var occupyType: Int = -1
  var orgCode: String? = null
  var packingUnit: String? = null
  var pickingPath: Int = -1
  var piecePackageBarCode: String? = null
  var producingArea: String? = null
  var productBatchCode: String? = null
  var productCode: String? = null
  var productCountBig: Int = -1
  var productCountScatter: Int = -1
  var productCountSmall: Int = -1
  var productManufactureDate: String? = null
  var productName: String? = null
  var productPackingBigNumber: Int = -1
  var productPackingId: String? = null
  var productPackingMiddleNumber: Int = -1
  var productSupervise: Int = -1
  var productSuperviseDesc: String? = null
  var productTaxPrice: Double = -1.0
  var productType: Int = -1
  var productValidDate: String? = null
  var purchaseLineNumber: Int = -1
  var purchaseOrderCode: String? = null
  var purchaseUser: String? = null
  var receiveLineNumber: Int = -1
  var receiveOrderCode: String? = null
  var registrationCertificate: String? = null
  var rowid: Int = -1
  var shadingProperty: Int = -1
  var shelfReceiveTime: String? = null
  var shelfUser: String? = null
  var shelfUserName: String? = null
  var shelvesCountBig: Int = -1
  var shelvesCountScatter: Int = -1
  var shelvesCountSmall: Int = -1
  var showPosition: String? = null
  var smallPackageBarCode: String? = null
  var specifications: String? = null
  var storageAreaCode: String? = null
  var storageClassification: String? = null
  var storageConditions: String? = null
  var storageConditionsDesc: String? = null
  var storageOrderCode: String? = null
  var storageOrderStatus: Int = -1
  var storageOrderStatusDesc: String? = null
  var storageType: Int = -1
  var storageTypeArea: String? = null
  var storageTypeAreaRecommend: String? = null
  var storageTypeCode: String? = null
  var storageTypeName: String? = null
  var supplierCode: String? = null
  var supplierName: String? = null
  var updateTime: String? = null
  var updateUser: String? = null
  var yn: Int = -1
  var sqlId: Long = -1
  var buildingCode: String? = null    //建筑物编码	string	（新增字段）
  var channelCode: String? = null //业务类型编码 string    //（新增字段）
  var ids: String? = null    //客户端展示的时候，多条明细id组合    string    （新增字段）
  var ownerCode: String? = null    //业主编号    string    （新增字段）

  var pdaProductDetailLineId: String? = null // pda商品列表明细页唯一标识    string    （新增字段）代替：lineNumberSplitOrigin
  var pdaProductLineId: String? = null //  pda商品列表页唯一标识    string    （新增字段）
  var storageRoomCode: String? = null //   库房编码    string    （新增字段）
  var warehouseCode: String? = null // 仓库编码    string    （新增字段）

  var sterilizingBatchNumber: String? = null // 灭菌批号
  var storageTypeAreaName: String? = null // 逻辑区域 （中文）

  @Throws(CloneNotSupportedException::class)// 克隆失败抛出异常
  public override fun clone(): NewInProductBean {
    return super.clone() as NewInProductBean
  }
}
