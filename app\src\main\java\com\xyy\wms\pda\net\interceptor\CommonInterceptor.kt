package com.xyy.wms.pda.net.interceptor

import com.xyy.utilslibrary.global.GlobalApplication
import com.xyy.utilslibrary.utils.AppUtils
import com.xyy.wms.pda.utils.SharedPrefManager
import okhttp3.HttpUrl
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import java.io.IOException

/**
 * 网络请求拦截器
 */
class CommonInterceptor : Interceptor {
  @Throws(IOException::class)
  override fun intercept(chain: Interceptor.Chain): Response {
    val oldRequest: Request = chain.request()
    // 添加新的参数
    val authorizedUrlBuilder: HttpUrl.Builder = oldRequest.url
      .newBuilder()
      .scheme(oldRequest.url.scheme)
      .host(oldRequest.url.host)
    val tgc = SharedPrefManager.getInstance().tgc
    // 新的请求
    val newRequest = oldRequest.newBuilder()
      .header("wmsPDA", tgc ?: "")
      .header("appVersionName", AppUtils.getAppVersionName(GlobalApplication.getContext()))
      .header("appVersionCode", AppUtils.getAppVersionCode(GlobalApplication.getContext()).toString() + "")
      .header("sourceType", "PDA")
      .header("Accept-Encoding", "")
      .url(authorizedUrlBuilder.build())
      .build()
    return chain.proceed(newRequest)
  }
}
