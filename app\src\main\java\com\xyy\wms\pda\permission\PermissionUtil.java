package com.xyy.wms.pda.permission;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.provider.Settings;
import android.view.View;

import com.xyy.utilslibrary.dailog.JYDialog;
import com.xyy.utilslibrary.utils.ColorUtils;
import com.xyy.wms.pda.R;

/**
 * 权限请求的工具类
 */

public class PermissionUtil {

    /**
     * 打开 APP 的详情设置
     */
    public static void openAppDetails(final Context context, String permission, Boolean canCancel) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setMessage("应用需要 “" + permission + "” 权限，请到 “应用信息 -> 权限” 中授予！");
        builder.setPositiveButton("去手动授权", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                openAppSettingDetail(context);
            }
        });
        if (canCancel) {
            builder.setNegativeButton("取消", null);
        }
        builder.show();
    }

    /**
     * 显示授权提示框
     *
     * @param context      上下文
     * @param permission   权限名称
     * @param isCancelable 是否可以取消
     */
    public static void showPermissionDialog(final Context context, String permission, Boolean isCancelable) {
        final JYDialog permissionDialog = new JYDialog(context, null, isCancelable);
        permissionDialog.setContent(permission + "权限，请到 “应用信息 -> 权限” 中授予！");
        permissionDialog.setTitleIsVisible(false);
        permissionDialog.setRightButtonTextColor(ColorUtils.getColor(context, R.color.colorPrimary));
        permissionDialog.setRightButtonTextStyle();
        if (isCancelable) {
            permissionDialog.setLeftText("取消", new View.OnClickListener() {

                @Override
                public void onClick(View arg0) {
                    permissionDialog.dismiss();
                }
            });
        } else {
            // 不可以点击外部取消，也不显示取消按钮
            permissionDialog.setShowOk(false);
        }
        permissionDialog.setRightText("去授权", new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openAppSettingDetail(context);
                permissionDialog.dismiss();
            }
        }).show();
    }

    /**
     * 显示授权提示框
     *
     * @param context      上下文
     * @param permission   权限名称
     * @param isCancelable 是否可以取消
     */
    public static void showPermissionDialog(final Context context, String permission, Boolean isCancelable, final OnCancelCallBack cancelCallBack) {
        final JYDialog permissionDialog = new JYDialog(context, null, isCancelable);
        permissionDialog.setContent(permission + "权限，请到 “应用信息 -> 权限” 中授予！");
        permissionDialog.setTitleIsVisible(false);
        permissionDialog.setRightButtonTextColor(ColorUtils.getColor(context, R.color.colorPrimary));
        permissionDialog.setRightButtonTextStyle();
        if (isCancelable) {
            permissionDialog.setLeftText("取消", new View.OnClickListener() {

                @Override
                public void onClick(View arg0) {
                    permissionDialog.dismiss();
                    if (cancelCallBack != null) {
                        cancelCallBack.cancel();
                    }
                }
            });
        } else {
            // 不可以点击外部取消，也不显示取消按钮
            permissionDialog.setShowOk(false);
        }
        permissionDialog.setRightText("去授权", new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openAppSettingDetail(context);
                permissionDialog.dismiss();
            }
        }).show();
    }

    /**
     * 打开应用设置详情页
     *
     * @param context
     */
    private static void openAppSettingDetail(Context context) {
        Intent intent = new Intent();
        intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        intent.addCategory(Intent.CATEGORY_DEFAULT);
        intent.setData(Uri.parse("package:" + context.getPackageName()));
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.addFlags(Intent.FLAG_ACTIVITY_NO_HISTORY);
        intent.addFlags(Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS);
        context.startActivity(intent);
    }

    public interface OnCancelCallBack {
        void cancel();
    }
}
