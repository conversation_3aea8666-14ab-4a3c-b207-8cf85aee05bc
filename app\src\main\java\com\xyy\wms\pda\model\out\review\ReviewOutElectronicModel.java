package com.xyy.wms.pda.model.out.review;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiOutManagerService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.out.ReviewSupervisionCodeBean;
import com.xyy.wms.pda.bean.out.ReviewSupervisionProductBean;
import com.xyy.wms.pda.contract.out.review.ReviewOutElectronicContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.Map;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 02/18/2019 18:57
 */
public class ReviewOutElectronicModel extends BaseModel implements ReviewOutElectronicContract.IReviewOutElectronicModel {

    public static ReviewOutElectronicModel newInstance() {
        return new ReviewOutElectronicModel();
    }

    @Override
    public Observable<BaseResponseBean<ReviewSupervisionCodeBean>> reviewOutSupervision(Map<String, Object> map) {
        return RetrofitCreateHelper.createApi(ApiOutManagerService.class).reviewOutSupervision(map)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<ReviewSupervisionProductBean>> reviewOutSupervisionProduct(Map<String, String> map) {
        return RetrofitCreateHelper.createApi(ApiOutManagerService.class).reviewOutSupervisionProduct(map)
                .compose(RxHelper.rxSchedulerHelper());
    }
}