package com.xyy.wms.pda.contract.out.bind;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.out.bind.CheckResult;
import com.xyy.wms.pda.bean.out.bind.ContainerCheckResult;

import java.util.Map;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 04/10/2019 14:59
 */
public interface BindCollectionAreaContract {

    interface IBindCollectionAreaModel extends IBaseModel {
        Observable<BaseResponseBean<ContainerCheckResult>> checkContainerCode(Map<String, Object> params);

        Observable<BaseResponseBean<String>> bindCollectionAreaCode(Map<String, Object> params);
    }

    interface IBindCollectionAreaView extends IBaseActivity {
        void checkContainerCodeSuccess(BaseResponseBean<ContainerCheckResult> baseResponseBean);
        void checkContainerCodeFailed(String msg);

        void bindCollectionAreaCodeSuccess(BaseResponseBean<String> baseResponseBean);
        void bindCollectionAreaCodeFail(String msg);
    }

}
