package com.xyy.wms.pda.bean.inmanager

import java.io.Serializable

/**
 * 移库单详情
 */
class MovementBillDetailBean : Serializable {
  var batchNumber //商品批号
      : String? = null
  var sterilizingBatchNumber: String? = null //灭菌批号
  var commonName //商品批号
      : String? = null
  var containerCode //容器编码
      : String? = null
  var id //主键id
      : String? = null
  var manufacturer //生产厂家
      : String? = null
  var moveNum //可移动数量
      : String? = null
  var movementId //移库单id
      : String? = null
  var movementNo //移库单号
      : String? = null
  var outGoodsPositionCode //移出货位编码
      : String? = null
  var outStorageAreaCode //移出库别编码
      : String? = null
  var outStorageAreaName //移出库区名称
      : String? = null
  var outStorageTypeCode //移出库别编码
      : String? = null
  var outStorageTypeName //移出库别名称
      : String? = null
  var outStorageRoomName: String? = null  //移出库房名称
  var packageNum = 0 //包装数量
  var middlePackingNumber = 0 //中包装数量
  var planMovePackNum //计划移库件数
      : String? = null
  var planMoveScatteredNum //计划移库零散数
      : String? = null
  var planMoveTotalNum //移库数量  移库数量=（移库件数*包装数量）+移库零散数。移库数量<=可移动数量
      = 0
  var produceTime //生成时间
      : String? = null
  var productCode //商品编码
      : String? = null
  var productName //商品名称
      : String? = null
  var standard //商品规格
      : String? = null
  var submit //提交状态    0，初始状态 1，下架完成 2，上架完成
      : String? = null
  var unit //单位
      : String? = null
  var validDate //	有效日期
      : String? = null
  var inGoodsPositionCode //实际移入货位编码
      : String? = null
  var inStorageTypeCode //实际移入库别编码
      : String? = null
  var inStorageTypeName //实际移入库别名称
      : String? = null
  var suggestStorageTypeCode //建议库别编码
      : String? = null
  var inStorageRoomName //建议库房名称
      : String? = null
  var downGoodsPositionCode //
      : String? = null
  var movePackNum //移库件数，ZJK 使用
      : Int? = null
  var moveScatteredNum // 移库散货数，
      : Int? = null
  var moveTotalNum //移库数量。  movePackNum * packageNum + moveScatteredNum
      : Int? = null
  var buildingCode //建筑物编码	string
      : String? = null
  var buildingName //建筑物名称
      : String? = null
  var channelCode //业务类型编码	string
      : String? = null
  var channelName //业务类型名称
      : String? = null
  var orgCode //机构码	string
      : String? = null
  var ownerCode //	业主编码	string
      : String? = null
  var storageAreaCode //	库区编码	string
      : String? = null
  var storageRoomCode //库房编码	string
      : String? = null
  var warehouseCode //仓库编码	string
      : String? = null
  val checkPosition // 是否需要二次校验货位
      : String? = null
  val agvType //agv 类型，下架且 == 2 时，需要验证容器
      : String? = null


  /**
   * 下架时，是否需要校验容器
   */
  fun isCheckContainer(): Boolean {
    return agvType.equals("2")
  }

  /**
   * 是否需要校验货位
   */
  fun isCheckPosition(): Boolean {
    return checkPosition.equals("1")
  }
}