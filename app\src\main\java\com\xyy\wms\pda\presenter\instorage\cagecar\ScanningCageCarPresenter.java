package com.xyy.wms.pda.presenter.instorage.cagecar;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.cageCar.ScanCageCar;
import com.xyy.wms.pda.contract.instorage.cagecar.ScanningCageCarContract;
import com.xyy.wms.pda.helper.SimpleErrorConsumer;
import com.xyy.wms.pda.helper.SimpleSuccessConsumer;
import com.xyy.wms.pda.model.instorage.cagecar.ScanningCageCarModel;
/**
 * Created by XyyMvpPdaTemplate on 02/25/2020 11:58
 */
public class ScanningCageCarPresenter extends BasePresenter<ScanningCageCarContract.IScanningCageCarModel, ScanningCageCarContract.IScanningCageCarView> {

    public static ScanningCageCarPresenter newInstance() {
        return new ScanningCageCarPresenter();
    }

    @Override
    protected ScanningCageCarModel getModel() {
        return ScanningCageCarModel.newInstance();
    }
    public void getStorageContainersInfo(String containerCode, String rollContainerCode) {
        mRxManager.register(mIModel.getStorageContainersInfo(containerCode, rollContainerCode).subscribe(new SimpleSuccessConsumer<BaseResponseBean<ScanCageCar>>(mIView) {

            public void onSuccess(BaseResponseBean<ScanCageCar> baseResponseBean) {
                mIView.getStorageContainersInfoSuccess(baseResponseBean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }
}
