package com.xyy.wms.pda.model.instorage.scatteredshelf;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.api.ApiService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.shelf.InShelfResult;
import com.xyy.wms.pda.bean.productCode.ProductCode;
import com.xyy.wms.pda.contract.instorage.scatteredshelf.ScatteredShelfListContract;
import com.xyy.wms.pda.model.productCode.GetProductCodeModel;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.List;

import io.reactivex.Observable;

/**
 * 零散列表上架
 */
public class ScatteredShelfListModel extends BaseModel implements ScatteredShelfListContract.IScatteredShelfListModel, GetProductCodeModel {

    public static ScatteredShelfListModel newInstance() {
        return new ScatteredShelfListModel();
    }

    @Override
    public Observable<BaseResponseBean<InShelfResult>> getScatteredShelfList(String containerCode, int shelfType) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).getStorageOrder(containerCode, shelfType)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<List<ProductCode>>> getProductBarCode(String ownerCode, String packageBarCode) {
        return RetrofitCreateHelper.createApi(ApiService.class).selectProductPda(ownerCode, packageBarCode)
                .compose(RxHelper.rxSchedulerHelper());
    }
}
