package com.xyy.wms.pda.contract.inmanager

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.inmanager.AddPositionAdjustmentBean
import io.reactivex.Observable

interface AddPositionAdjustmentContract {
  interface IAddPositionAdjustmentModel : IBaseModel {
    /**
     * 获取货位调整单号
     */
    fun getLocationMovementNo(): Observable<BaseResponseBean<String>>
    /**
     * 通过货位编码查询货位调整单列表
     */
    fun getGoodsInfoByLocationCode(map: Map<String, String>): Observable<BaseResponseBean<List<AddPositionAdjustmentBean>>>

  }

  interface IAddPositionAdjustmentView : IBaseActivity {
    /**
     * 获取货位调整单号
     */
    fun getLocationMovementNoSuccess(resultCode: String?)
    /**
     * 获取列表成功
     */
    fun getAddPositionAdjustmentListSuccess(list: List<AddPositionAdjustmentBean>)

    fun showEmpty()
  }
}
