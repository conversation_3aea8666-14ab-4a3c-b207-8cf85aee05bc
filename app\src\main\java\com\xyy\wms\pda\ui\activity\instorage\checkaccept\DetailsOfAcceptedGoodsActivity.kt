package com.xyy.wms.pda.ui.activity.instorage.checkaccept

import android.os.Bundle
import android.util.Log
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.xyy.common.widget.DefaultItemDecoration
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity
import com.xyy.utilslibrary.utils.DisplayUtils
import com.xyy.wms.pad.instorage.presenter.newinspection.DetailsOfAcceptedGoodsPresenter
import com.xyy.wms.pda.R
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderDetailListBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderDetailListPost
import com.xyy.wms.pda.bean.instorage.checkaccept.WaitCheckDetailListBean
import com.xyy.wms.pda.contract.instorage.checkaccept.DetailsOfAcceptedGoodsContract
import com.xyy.wms.pda.ui.adapter.instorage.checkaccept.DetailsOfAcceptedGoodsAdapter
import com.xyy.wms.pda.utils.CommonInfoUtils
import kotlinx.android.synthetic.main.activity_details_of_accepted_goods.toolbar_check_accept
import kotlinx.android.synthetic.main.activity_details_of_accepted_goods.checkAcceptBill_recyclerView_checked
import kotlinx.android.synthetic.main.operate_layout.tv_examiner
import kotlinx.android.synthetic.main.operate_layout.tv_examiner_key
import kotlinx.android.synthetic.main.operate_layout.tv_reception_time
import kotlinx.android.synthetic.main.operate_layout.tv_reception_time_key
import kotlinx.android.synthetic.main.activity_details_of_accepted_goods.*

/**
 * <AUTHOR>
 * @Description 已验收商品明细（跳转页）
 * @Date 2022/4/11
 */
class DetailsOfAcceptedGoodsActivity : BaseMVPCompatActivity<DetailsOfAcceptedGoodsPresenter>(), DetailsOfAcceptedGoodsContract.DetailsOfAcceptedGoodsContractView {
    private lateinit var mDetailsOfUnAcceptedGoodsAdapter: DetailsOfAcceptedGoodsAdapter
    private var checkedAcceptBillList: MutableList<CheckOrderDetailListBean>? = mutableListOf()

    private lateinit var checkOrderCode : String
    private var channelCode: String = ""
    override fun getLayoutId(): Int {
      return R.layout.activity_details_of_accepted_goods
    }
    override fun initPresenter(): BasePresenter<*, *> = DetailsOfAcceptedGoodsPresenter()

    override fun initTransferData() {
        super.initTransferData()
        intent?.let {
            checkOrderCode = it.getStringExtra("checkOrderCode")
            channelCode = it.getStringExtra("channelCode")
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        setupOperatorLayout()
        addListener()
        initRecyclerView()
    }
    private fun setupOperatorLayout() {
      initTitleBar(toolbar_check_accept, "")
      CommonInfoUtils.setOperationName(tv_examiner_key, "操作员：", tv_examiner)
      CommonInfoUtils.setOperationTime(tv_reception_time_key, "操作时间：", tv_reception_time)
    }


    private fun addListener() {
      tv_search.setOnClickListener {
            var checkOrderDetailListPost = CheckOrderDetailListPost(checkOrderCode, ed_keyword_query.text.toString(),channelCode)
            checkOrderDetailListPost.containerCode = ed_container_no.text.toString()
            mPresenter.getCheckOrderDetailList(checkOrderDetailListPost)
        }
    }

    private fun initRecyclerView() {
        //控制列表的布局和项目之间的分隔线
        checkAcceptBill_recyclerView_checked.layoutManager = LinearLayoutManager(mContext)
        val defaultItemDecoration = DefaultItemDecoration(this, DisplayUtils.dp2px(1f))
        defaultItemDecoration.setHasHeaderAndFooter(true)
        defaultItemDecoration.setLineColorResId(R.color.bg)
        checkAcceptBill_recyclerView_checked.addItemDecoration(defaultItemDecoration)
        // 确保适配器已创建
        if (!::mDetailsOfUnAcceptedGoodsAdapter.isInitialized) {
          mDetailsOfUnAcceptedGoodsAdapter = DetailsOfAcceptedGoodsAdapter(R.layout.item_details_of_unaccepted_goods_new)
          checkAcceptBill_recyclerView_checked.adapter = mDetailsOfUnAcceptedGoodsAdapter
        }
        mDetailsOfUnAcceptedGoodsAdapter.setNewData(checkedAcceptBillList)
        mPresenter.getCheckOrderDetailList(CheckOrderDetailListPost(checkOrderCode, "",channelCode))
    }

    override fun getCheckOrderDetailListSuccess(result : BaseResponseBean<List<CheckOrderDetailListBean>>) {
        if (result.code == 0) {
            if (result.result != null) {
                checkedAcceptBillList = result.result as MutableList<CheckOrderDetailListBean>?
                Log.i("checkedAcceptBillList", checkedAcceptBillList!!.size.toString())
                mDetailsOfUnAcceptedGoodsAdapter.setNewData(result.result)
            } else {
                showToast("暂无数据")
                checkedAcceptBillList = mutableListOf()
                mDetailsOfUnAcceptedGoodsAdapter.setNewData(arrayListOf())
            }
        } else {
          showToast(result.msg)
        }
    }
}


