package com.xyy.wms.pda.model.out.purchaseexit;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitElectronicCodeBean;
import com.xyy.wms.pda.contract.out.purchaseexit.PurchaseExitElectronicCodeContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.List;

import io.reactivex.Observable;

/**
 * Created by lwj on 2020-02-24.
 * <EMAIL>
 */
public class PurchaseExitElectronicCodeModel implements PurchaseExitElectronicCodeContract.IPurchaseExitElectronicCodeModel {

    public static PurchaseExitElectronicCodeModel newInstance() {
        return new PurchaseExitElectronicCodeModel();
    }


    @Override
    public Observable<BaseResponseBean<List<PurchaseExitElectronicCodeBean>>> queryReceipts(String startDate, String endDate, String scanStatus, String receiptsNum, String supplierName) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).queryReceipts(startDate, endDate,scanStatus, receiptsNum, supplierName)
                .compose(RxHelper.rxSchedulerHelper());
    }
}
