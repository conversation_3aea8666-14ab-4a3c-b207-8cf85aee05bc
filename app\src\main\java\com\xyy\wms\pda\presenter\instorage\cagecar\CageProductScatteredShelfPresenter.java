package com.xyy.wms.pda.presenter.instorage.cagecar;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.cageCar.CageCarOrderBean;
import com.xyy.wms.pda.bean.instorage.cageCar.CageCarShelfDetailPost;
import com.xyy.wms.pda.bean.instorage.cageCar.CommitCageCarShelfResult;
import com.xyy.wms.pda.bean.instorage.shelf.CheckSelectGoodsPositionBean;
import com.xyy.wms.pda.helper.SimpleErrorConsumer;
import com.xyy.wms.pda.helper.SimpleSuccessConsumer;
import com.xyy.wms.pda.model.instorage.cagecar.CageProductScatteredShelfModel;
import com.xyy.wms.pda.presenter.checkSelectGoodsPosition.instorage.CheckSelectGoodsPositionPresenter;
import com.xyy.wms.pda.bean.req.CheckGoodsPositionReq;
import com.xyy.wms.pda.ui.activity.instorage.cagecar.CageProductScatteredShelfActivity;

/**
 * Created by XyyMvpPdaTemplate on 02/25/2020 12:02
 */
public class CageProductScatteredShelfPresenter extends BasePresenter<CageProductScatteredShelfModel,
        CageProductScatteredShelfActivity> implements CheckSelectGoodsPositionPresenter {

    public static CageProductScatteredShelfPresenter newInstance() {
        return new CageProductScatteredShelfPresenter();
    }

    @Override
    protected CageProductScatteredShelfModel getModel() {
        return CageProductScatteredShelfModel.newInstance();
    }

    /**
     * 单个明细提交
     */
    public void commitCageCarOrderDetail(CageCarShelfDetailPost carShelfDetailPost) {
        mRxManager.register(mIModel.commitCageCarOrderDetail(carShelfDetailPost).subscribe(new SimpleSuccessConsumer<BaseResponseBean<CommitCageCarShelfResult>>(mIView) {

            @Override
            public void onSuccess(BaseResponseBean<CommitCageCarShelfResult> baseResponseBean) {
                mIView.commitCageCarOrderDetailSuccess(baseResponseBean);
            }
        }, new SimpleErrorConsumer(mIView)));

    }

    @Override
    public void checkSelectGoodsPosition(CheckGoodsPositionReq req) {
        mRxManager.register(mIModel.checkSelectGoodsPosition(req).subscribe(new SimpleSuccessConsumer<BaseResponseBean<CheckSelectGoodsPositionBean>>(mIView) {
            @Override
            public void onSuccess(BaseResponseBean<CheckSelectGoodsPositionBean> baseResponseBean) {
                mIView.checkSelectGoodsPositionSuccess(baseResponseBean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }

    /**
     * 单个明细提交
     */
    public void getRollContainerShelfProductList(String pdaProductLineId, String storageOrderCode) {
        mRxManager.register(mIModel.getRollContainerShelfProductList(pdaProductLineId, storageOrderCode).subscribe(new SimpleSuccessConsumer<BaseResponseBean<CageCarOrderBean>>(mIView) {
            @Override
            public void onSuccess(BaseResponseBean<CageCarOrderBean> baseResponseBean) {
                mIView.getRollContainerShelfProductListSuccess(baseResponseBean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }

}
