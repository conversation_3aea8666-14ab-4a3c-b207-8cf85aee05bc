package com.xyy.wms.pda.bean.instorage.checkaccept

/**
 * 新版 入库验收单 Bean（仅包含新页面所需字段）
 */
data class CheckAcceptBillNew(
    var buildingName: String? = null,
    var checkOrderCode: String? = null,
    var receiveUserName: String? = null,
    var supplierName: String? = null,
    var checkUser: String? = null,
    var containerCodes: String? = null
) {
    companion object {
        fun fromOld(old: CheckAcceptBill): CheckAcceptBillNew {
            return CheckAcceptBillNew(
                buildingName = old.buildingName,
                checkOrderCode = old.checkOrderCode,
                receiveUserName = old.receiveUserName,
                supplierName = old.supplierName,
                checkUser = old.checkUser,
                containerCodes = old.containerCodes
            )
        }
    }
}