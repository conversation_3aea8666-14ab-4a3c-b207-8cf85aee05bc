package com.xyy.wms.pda.model.pinback.wholeShelf;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiPinBackService;
import com.xyy.wms.pda.api.ApiService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.pinback.ProductBean;
import com.xyy.wms.pda.bean.productCode.ProductCode;
import com.xyy.wms.pda.contract.pinback.wholeShelf.WholeShelfListContract;
import com.xyy.wms.pda.model.productCode.GetProductCodeModel;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.List;

import io.reactivex.Observable;

/**
 * 销退整件上架列表
 */
public class WholeShelfListModel extends BaseModel implements WholeShelfListContract.IWholeShelfItemModel, GetProductCodeModel {

    public static WholeShelfListModel newInstance() {
        return new WholeShelfListModel();
    }

    @Override
    public Observable<BaseResponseBean<List<ProductBean>>> getWholeShelfList(String containerCode, int type) {
        return RetrofitCreateHelper.createApi(ApiPinBackService.class).getPieceListByContainerCode(containerCode,type)
                .compose(RxHelper.rxSchedulerHelper());
    }
    @Override
    public Observable<BaseResponseBean<List<ProductCode>>> getProductBarCode(String ownerCode, String packageBarCode) {
        return RetrofitCreateHelper.createApi(ApiService.class).selectProductPda(ownerCode, packageBarCode)
                .compose(RxHelper.rxSchedulerHelper());
    }
}
