<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar_check_accept"
        android:layout_width="match_parent"
        android:layout_height="@dimen/toolbar_height"
        android:layout_gravity="center"
        android:background="?attr/colorPrimary"
        android:fitsSystemWindows="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_scrollFlags="scroll|enterAlways"
        app:navigationIcon="@mipmap/ic_arrow_back_white"
        app:popupTheme="@style/AppTheme.ToolbarPopupOverlay">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="追溯码扫描"
            android:textColor="@color/white"
            android:textSize="@dimen/title_text_size" />
    </androidx.appcompat.widget.Toolbar>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <LinearLayout
            android:id="@+id/ll_head"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/item_height"
                android:gravity="center_vertical"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/tv_product_name_tag"
                    style="@style/item_text_style_333"
                    android:layout_width="@dimen/dp80"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp20"
                    android:text="商品名称:"
                    android:textColor="@color/text_color_333333"
                    android:textSize="@dimen/sp14" />
                <TextView
                    android:id="@+id/tv_product_name"
                    style="@style/item_text_style_333"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp20"
                    android:text="999感冒灵..."
                    android:textColor="@color/text_color_333333"
                    android:textSize="@dimen/sp14" />
                <TextView
                    android:id="@+id/btn_unLock"
                    style="@style/item_text_style_333"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp20"
                    android:text="解锁"
                    android:textColor="@color/colorAccent"
                    android:textSize="@dimen/sp14" />
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/item_height"
                android:gravity="center_vertical"
                android:orientation="horizontal">
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal"
                    >
                    <TextView
                        android:id="@+id/tv_should_sweep_tag"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp20"
                        android:text="应扫："
                        android:textColor="@color/text_color_333333"
                        android:textSize="@dimen/sp14" />
                    <TextView
                        android:id="@+id/tv_should_sweep"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="1000"
                        android:textColor="@color/text_color_333333"
                        android:textSize="@dimen/sp14" />
                </LinearLayout>
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal"
                    >
                    <TextView
                        android:id="@+id/tv_solid_sweep_tag"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="实扫："
                        android:textColor="@color/text_color_333333"
                        android:textSize="@dimen/sp14" />
                    <TextView
                        android:id="@+id/tv_solid_sweep"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="1000"
                        android:textColor="@color/text_color_52C347"
                        android:textSize="@dimen/sp14" />
                </LinearLayout>
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal"
                    >
                    <TextView
                        android:id="@+id/tv_surplus_tag"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="实扫："
                        android:textColor="@color/text_color_333333"
                        android:textSize="@dimen/sp14" />
                    <TextView
                        android:id="@+id/tv_surplus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="1000"
                        android:textColor="@color/red_bg"
                        android:textSize="@dimen/sp14" />
                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/item_height"
                android:gravity="center_vertical"
                android:orientation="horizontal">
                <TextView
                    style="@style/item_text_style_333"
                    android:id="@+id/tv_small_tag"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp20"
                    android:text="1"
                    android:textColor="@color/text_color_333333"
                    android:textSize="@dimen/sp14" />
                <TextView
                    android:id="@+id/tv_normal_tag"
                    style="@style/item_text_style_333"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp20"
                    android:text="10"
                    android:textColor="@color/text_color_333333"
                    android:textSize="@dimen/sp14" />
                <TextView
                    android:id="@+id/tv_big_tag"
                    style="@style/item_text_style_333"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp20"
                    android:text="50"
                    android:textColor="@color/text_color_333333"
                    android:textSize="@dimen/sp14" />
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/item_height"
                android:gravity="center_vertical"
                android:orientation="horizontal">
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="@dimen/item_height"
                    android:gravity="center_vertical">
                    <RadioButton
                        android:id="@+id/radio_small_tag"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />
                    <TextView
                        style="@style/item_text_style_333"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"

                        android:text="小包装"
                        android:textColor="@color/text_color_333333"
                        android:textSize="@dimen/sp14" />
                </LinearLayout>
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="@dimen/item_height"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">
                    <RadioButton
                        android:id="@+id/radio_normal_tag"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />
                    <TextView
                        style="@style/item_text_style_333"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:text="中包装"
                        android:textColor="@color/text_color_333333"
                        android:textSize="@dimen/sp14" />
                </LinearLayout>
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="@dimen/item_height"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">
                    <RadioButton
                        android:id="@+id/radio_big_tag"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        />
                    <TextView
                        style="@style/item_text_style_333"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:text="大包装"
                        android:textColor="@color/text_color_333333"
                        android:textSize="@dimen/sp14" />
                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/item_height"
                android:gravity="center_vertical"
                android:orientation="horizontal">
                <TextView
                    style="@style/item_text_style_333"
                    android:layout_width="@dimen/dp80"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp20"
                    android:text="追溯码扫描:"
                    android:textColor="@color/text_color_333333"
                    android:textSize="@dimen/sp14" />
                <EditText
                    android:id="@+id/ed_bar_code"
                    style="@style/et_scan_code_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp20"
                    android:hint=""
                    android:textColor="@color/color_ff_333333" />
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp46"
                android:background="@color/color_ffebedf1"
                >
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="2"
                    android:gravity="center"
                    android:text="业主名称"
                    android:textColor="@color/color_ff_102442"
                    android:textSize="@dimen/sp14" />
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0.5"
                    android:gravity="center"
                    android:text="数量"
                    android:textColor="@color/color_ff_102442"
                    android:textSize="@dimen/sp14" />
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0.5"
                    android:gravity="center"
                    android:text="操作"
                    android:textColor="@color/color_ff_102442"
                    android:textSize="@dimen/sp14" />
            </LinearLayout>
        </LinearLayout>
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/layout_recyclerview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/btn_submit"
            android:layout_below="@+id/ll_head"
            android:layout_marginStart="@dimen/dp5"
            android:layout_marginTop="1dp"
            android:layout_marginBottom="-2dp" />
        <Button
            android:id="@+id/btn_submit"
            android:background="@drawable/shape_driver_license_plate_btn"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp30"
            android:layout_above="@+id/ll_bottom"
            android:layout_weight="1"
            android:text="提交"
            android:textColor="@color/white"
            />
        <LinearLayout
            android:id="@+id/ll_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:orientation="vertical">
            <include layout="@layout/operate_layout" />
        </LinearLayout>
    </RelativeLayout>
</LinearLayout>