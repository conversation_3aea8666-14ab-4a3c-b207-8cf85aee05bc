package com.xyy.wms.pda.api;

import com.xyy.wms.pda.bean.base.BaseResponseBean;

import com.xyy.wms.pda.bean.common.CancelTaskBean;
import com.xyy.wms.pda.bean.common.CheckContainerCodeResult;
import com.xyy.wms.pda.bean.common.LogicalRegionResult;
import com.xyy.wms.pda.bean.common.MoveStorageFinishedBean;
import com.xyy.wms.pda.bean.common.RunningTaskResult;
import com.xyy.wms.pda.bean.common.SourcePositionCodeBean;
import com.xyy.wms.pda.bean.moveStorage.AddTaskCarDownDetailBean;
import com.xyy.wms.pda.bean.moveStorage.AddTaskDetailBean;
import com.xyy.wms.pda.bean.moveStorage.CarNumberBean;
import com.xyy.wms.pda.bean.moveStorage.CarNumberResult;
import com.xyy.wms.pda.bean.moveStorage.PalletGetGoodsBean;
import com.xyy.wms.pda.bean.moveStorage.PalletGetGoodsResult;
import com.xyy.wms.pda.bean.moveStorage.ResourcePositionGoodsBean;
import com.xyy.wms.pda.bean.moveStorage.StartTaskBean;
import com.xyy.wms.pda.bean.moveStorage.StartTaskResult;
import com.xyy.wms.pda.bean.moveStorage.DeliveryBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseCommitResult;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitDetail;
import com.xyy.wms.pda.bean.common.CheckContainerCodeBean;

import java.util.List;

import io.reactivex.Observable;
import retrofit2.http.Body;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

public interface ApiMoveStorageService {
    /**
     * 购进退出 - 执行提交 （明细提交）
     */
//    @POST("migrate/checkContainerStatus")
//    Observable<BaseResponseBean<PurchaseCommitResult>> checkContainerCode(@Body PurchaseExitDetail commitPost);

    /**
     * 校验托盘位
     */
    @POST("migrate/checkContainerStatus")
    Observable<BaseResponseBean<Boolean>> checkContainerCode(@Body CheckContainerCodeBean commitPost);

    /**
     * 通过原货位获取逻辑区
     */
    @POST("migrate/selectLogicalRegionByAllocation")
    Observable<BaseResponseBean<LogicalRegionResult>> getLogicalRegion(@Body SourcePositionCodeBean commitPost);

    /**
     * 开启任务 1整件下架，2零货下架，3装车，4下车,5整件上架，6零货上架
     */
    @POST("migrate/startTask")
    Observable<BaseResponseBean<StartTaskResult>> startTaskWholeShelfDown(@Body StartTaskBean commitPost);

    /**
     * 进行中任务
     */
    @POST("migrate/selectRunningTask")
    Observable<BaseResponseBean<RunningTaskResult>> getRunningTask();

    /**
     * 托盘位-获取商品
     */
    @POST("migrate/selectProductsByPallet")
    Observable<BaseResponseBean<List<PalletGetGoodsResult>>> getWholeShelfDownList(@Body PalletGetGoodsBean commitPost);

    /**
     * 原盘位-获取商品
     */
    @POST("migrate/selectProductsByAllocation")
    Observable<BaseResponseBean<List<PalletGetGoodsResult>>> getProductsByAllocationList(@Body ResourcePositionGoodsBean commitPost);

    /**
     * 添加任务明细-下架
     */
    @POST("migrate/addTaskDetail")
    Observable<BaseResponseBean<Boolean>> addTaskDetail(@Body AddTaskDetailBean commitPost);

    /**
     * 添加任务明细-下车/装车
     */
    @POST("migrate/addTaskDetail")
    Observable<BaseResponseBean<Boolean>> addTaskCarDownDetail(@Body AddTaskCarDownDetailBean commitPost);

    /**
     * 移除任务明细
     */
    @POST("migrate/removeDetail")
    Observable<BaseResponseBean<Boolean>> canelTask(@Body CancelTaskBean commitPost);

    /**
     * 完成任务
     */
    @POST("migrate/submitTask")
    Observable<BaseResponseBean<Boolean>> setFinished(@Body MoveStorageFinishedBean commitPost);

    /**
     * 获取车牌码
     */
    @POST("driver/selectDriverById")
    Observable<BaseResponseBean<CarNumberResult>> getCarNumber(@Body CarNumberBean commitPost);

    /**
     * 转运登记
     */
    @POST("migrate/v2/registration")
    Observable<BaseResponseBean<Boolean>> delivery(@Body DeliveryBean commitPost);
}
