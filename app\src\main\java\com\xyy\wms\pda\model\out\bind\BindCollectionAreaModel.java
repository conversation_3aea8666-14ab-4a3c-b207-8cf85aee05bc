package com.xyy.wms.pda.model.out.bind;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.out.bind.CheckResult;
import com.xyy.wms.pda.bean.out.bind.ContainerCheckResult;
import com.xyy.wms.pda.contract.out.bind.BindCollectionAreaContract;
import com.xyy.wms.pda.contract.out.bind.BindWallCodeContract;
import com.xyy.wms.pda.model.ServiceModel;

import java.util.Map;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 04/10/2019 14:59
 */
public class BindCollectionAreaModel extends ServiceModel implements BindCollectionAreaContract.IBindCollectionAreaModel {

    public static BindCollectionAreaModel newInstance() {
        return new BindCollectionAreaModel();
    }

    @Override
    public Observable<BaseResponseBean<ContainerCheckResult>> checkContainerCode(Map<String, Object> params) {
        return getApiOutManagerService().checkContainerCode(params).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<String>> bindCollectionAreaCode(Map<String, Object> params) {
        return getApiOutManagerService().bindCollecionArea(params).compose(RxHelper.rxSchedulerHelper());
    }
}
