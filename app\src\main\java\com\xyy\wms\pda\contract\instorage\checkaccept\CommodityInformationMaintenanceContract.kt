package com.xyy.wms.pda.contract.instorage.checkaccept

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.GetUpperLowerProductInfoMaintainBean
import com.xyy.wms.pda.bean.instorage.checkaccept.GetUpperLowerProductInfoMaintainPost
import com.xyy.wms.pda.bean.instorage.checkaccept.LogicalRegionProductInfoMaintainBean
import com.xyy.wms.pda.bean.instorage.checkaccept.LogicalRegionProductInfoMaintainPost
import com.xyy.wms.pda.bean.instorage.checkaccept.ProductInfoMaintainQueryBean
import com.xyy.wms.pda.bean.instorage.checkaccept.ProductInfoMaintainQueryPost
import com.xyy.wms.pda.bean.instorage.checkaccept.SaveProductInfoMaintainPost
import io.reactivex.Observable
import java.util.ArrayList

interface CommodityInformationMaintenanceContract {

    interface CommodityInformationMaintenanceModel : IBaseModel {
      fun queryProductInfoMaintain(productInfoMaintainQueryPost : ProductInfoMaintainQueryPost) : Observable<BaseResponseBean<ProductInfoMaintainQueryBean>>
      fun getUpperLowerProductInfoMaintain(getUpperLowerProductInfoMaintainPost : GetUpperLowerProductInfoMaintainPost) : Observable<BaseResponseBean<GetUpperLowerProductInfoMaintainBean>>
      fun logicalRegionProductInfoMaintain(logicalRegionProductInfoMaintainPost : LogicalRegionProductInfoMaintainPost) : Observable<BaseResponseBean<ArrayList<LogicalRegionProductInfoMaintainBean>>>
      fun saveProductInfoMaintain(saveProductInfoMaintainPost : SaveProductInfoMaintainPost) : Observable<BaseResponseBean<String>>
    }

    interface CommodityInformationMaintenanceView : IBaseActivity {
      fun queryProductInfoMaintainSuccess(productInfoMaintainQueryBean : BaseResponseBean<ProductInfoMaintainQueryBean>)
      fun getUpperLowerProductInfoMaintainSuccess(getUpperLowerProductInfoMaintainBean : GetUpperLowerProductInfoMaintainBean, boolean: Boolean)
      fun logicalRegionProductInfoMaintainSuccess(logicalRegionProductInfoMaintainBean : ArrayList<LogicalRegionProductInfoMaintainBean>, isWhole: Boolean)
      fun saveProductInfoMaintainSuccess(string: String)
    }
}
