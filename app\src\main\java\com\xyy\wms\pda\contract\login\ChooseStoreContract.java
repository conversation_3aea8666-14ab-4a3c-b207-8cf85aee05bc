package com.xyy.wms.pda.contract.login;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.user.UserInfoBean;
import com.xyy.wms.pda.bean.user.WarehouseResult;
import io.reactivex.Observable;
import java.util.List;
/**
 * Created by XyyMvpPdaTemplate on 12/04/2019 15:51
 */
public interface ChooseStoreContract {

    interface IChooseStoreModel extends IBaseModel {

        /**
         * 请求登录接口
         */
        Observable<BaseResponseBean<List<WarehouseResult>>> getChooseStoreList();

        Observable<BaseResponseBean<Object>> commitChooseStore(String storageCode);

    }

    interface IChooseStoreView extends IBaseActivity {

        /**
         * 登录成功
         */
        void getChooseStoreListSuccess(List<WarehouseResult> warehouseResult);

        void commitChooseStoreSuccess(Object req);

    }

}
