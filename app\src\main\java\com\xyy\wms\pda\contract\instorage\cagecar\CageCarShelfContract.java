package com.xyy.wms.pda.contract.instorage.cagecar;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.cageCar.CageCarShelfResult;

import io.reactivex.Observable;
/**
 * Created by XyyMvpPdaTemplate on 02/25/2020 12:00
 * 笼车上架
 */
public interface CageCarShelfContract {

    interface ICageCarShelfModel extends IBaseModel {
        Observable<BaseResponseBean<CageCarShelfResult>> getRollContainerShelfList(String rollContainerCode);
    }

    interface ICageCarShelfView extends IBaseActivity {
        void getRollContainerShelfListSuccess(BaseResponseBean<CageCarShelfResult> baseResponseBean);
        void getRollContainerShelfListFail();
    }
}
