package com.xyy.wms.pda.contract.instorage.checkaccept

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.utilslibrary.base.IBaseModel
import io.reactivex.Observable
import com.xyy.wms.pda.bean.instorage.checkaccept.WaitCheckDetailListPost
import com.xyy.wms.pda.bean.instorage.checkaccept.WaitCheckDetailListBean

/**
 * 验收单商品列表
 * @Description
 * @Date 2022/4/11
 */
interface DetailsOfUnAcceptedGoodsContract {
    interface DetailsOfUnAcceptedGoodsContractModel : IBaseModel {
        fun getWaitCheckDetailList(waitCheckDetailListPost: WaitCheckDetailListPost) : Observable<BaseResponseBean<List<WaitCheckDetailListBean>>>
    }
    interface DetailsOfUnAcceptedGoodsContractView : IBaseActivity {
        fun getWaitCheckDetailListSuccess(list: BaseResponseBean<List<WaitCheckDetailListBean>>)
    }
}
