package com.xyy.wms.pda.constant

import java.util.concurrent.atomic.AtomicInteger

object BusCode {
  /**
   * 出库 - 外复合成功 更新已复核数量
   */
  val RX_BUS_UPDATE_REVIEW_NUMBER: Int by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
    generateBusCode()
  }

  /**
   * 出库 - 外复合成功 更新已复扫描电子追溯码数量
   */
  val RX_BUS_UPDATE_SUPERVISION_NUMBER: Int by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
    generateBusCode()
  }

  /**
   * 出库 - 补拣提交成功，关闭拆零拣货明细，刷新并显示拣货任务列表
   */
  val RX_BUS_FILL_PICK_SUBMIT_SUCCESS: Int by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
    generateBusCode()
  }

  /**
   * 出库 - 补拣选择新货位
   */
  val RX_BUS_FILL_PICK_SELECT_NEW_POSITION: Int by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
    generateBusCode()
  }

  /**
   *  刷新列表页面
   */
  val REFRESH_LIST: Int by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
    generateBusCode()
  }

  /**
   * 上架全部完成
   */
  val STORAGE_IN_ALL_COMPLETE: Int by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
    generateBusCode()
  }

  /**
   * 刷新购进退出单列表
   */
  val RX_CODE_OUT_REFRESH_PurchaseExitBill: Int by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
    generateBusCode()
  }

  /**
   * 刷新购进退出单
   */
  val RX_CODE_OUT_REFRESH_PurchaseExitOrderList: Int by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
    generateBusCode()
  }

  /**
   * 购进退出追溯码扫描，跳过下一个商品
   */
  val RX_CODE_OUT_REFRESH_PURCHASE_EXIT_ORDER_LIST_SELECT_GOODS: Int by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
    generateBusCode()
  }

  /**
   * 购进退出追溯码扫描，删除追溯码
   */
  val OUT_REFRESH_PURCHASEEXIT_ORDERLIST_DELETE_DRUGREGULATORY_CODE: Int by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
    generateBusCode()
  }

  /**
   * 购进退出复核成功
   */
  val OUT_REFRESH_PurchaseExit_REVIEW_SUCCESS: Int by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
    generateBusCode()
  }
  /**
   * 购进退出复核--追溯码扫描后，进入信息补充页面
   */
  val OUT_REFRESH_PURCHASE_EXIT_ORDER_LIST_FINISH_GOODS: Int by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
    generateBusCode()
  }
  /**
   * 购进退出复核--单被其他人复核完毕
   */
  val OUT_REFRESH_PURCHASE_EXIT_ORDER_ALREADY_REVIEW: Int by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
    generateBusCode()
  }
  /**
   * 更新验收数据
   */
  val IN_STORAGE_ACCEPT_UPDATE: Int by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
    generateBusCode()
  }



  private val baseCode: AtomicInteger = AtomicInteger(100)

  private fun generateBusCode(): Int {
    return baseCode.addAndGet(1)
  }
}
