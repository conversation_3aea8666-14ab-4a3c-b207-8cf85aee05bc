# 异常图片上传功能使用说明

## 功能概述

异常图片上传功能已成功集成到入库验收单详情页面（CheckBillDetailActivityNew），允许用户在处理异常情况时拍照或选择图片进行上传。

## 主要功能特性

### 1. 核心功能
- **图片拍摄**：支持调用相机拍照
- **图片选择**：支持从相册选择图片
- **图片预览**：支持查看已选择的图片
- **图片删除**：支持删除不需要的图片
- **批量上传**：支持一次性上传多张图片
- **进度显示**：上传过程中显示进度提示

### 2. 业务规则
- 最多支持5张图片上传
- 支持JPG、PNG格式图片
- 单张图片大小限制：5MB以内
- 自动压缩图片以优化上传速度
- 上传前进行图片质量检查

## 技术实现

### 1. 新增文件结构

#### 数据模型类 (app\src\main\java\com\xyy\wms\pda\bean\instorage\)
- `PictureInfo.kt` - 图片信息模型
- `BusinessInfo.kt` - 业务信息模型  
- `PictureUploadRequest.kt` - 上传请求模型
- `PictureUploadResponse.kt` - 上传响应模型
- `PictureDeleteRequest.kt` - 删除请求模型

#### UI组件类
- `ExceptionPictureButton.kt` - 异常图片按钮组件
- `ExceptionPictureDialog.kt` - 图片上传弹窗
- `PictureGridAdapter.kt` - 图片网格适配器

#### 配置和工具类
- `ExceptionPictureConfig.kt` - 配置参数
- `PictureError.kt` - 错误处理类
- `ImageCompressUtils.kt` - 图片压缩工具

#### 布局文件
- `dialog_exception_picture.xml` - 图片上传弹窗布局
- `item_picture_grid.xml` - 图片网格项布局

### 2. 修改的现有文件

#### API接口
- `ApiInAcceptServiceNew.kt` - 添加了图片上传相关API接口

#### MVP架构文件
- `CheckAcceptBillDetailContractNew.kt` - 添加图片上传相关接口定义
- `CheckAcceptBillDetailModelNew.kt` - 添加图片上传Model方法
- `CheckAcceptBillDetailPresenterNew.kt` - 添加图片上传Presenter方法

#### Activity
- `CheckBillDetailActivityNew.kt` - 集成异常图片按钮功能

#### 资源文件
- `styles.xml` - 添加全屏对话框样式
- `colors.xml` - 添加相关颜色资源
- `file_paths.xml` - 添加图片路径配置

## 使用方法

### 1. 在入库验收单详情页面
1. 扫描或输入商品信息
2. 点击"异常图片"按钮
3. 在弹出的对话框中选择"拍照"或"从相册选择"
4. 选择完图片后点击"提交"按钮上传

### 2. 图片管理
- 点击图片右上角的删除按钮可以删除图片
- 最多可以上传5张图片
- 按钮会显示当前已选择的图片数量

## 配置参数

可以通过修改 `ExceptionPictureConfig.kt` 来调整以下参数：

```kotlin
object ExceptionPictureConfig {
    const val MAX_PICTURE_COUNT = 5          // 最大图片数量
    const val MAX_FILE_SIZE = 5 * 1024 * 1024  // 最大文件大小 5MB
    const val COMPRESS_QUALITY = 80          // 压缩质量
    const val UPLOAD_TIMEOUT = 30000         // 上传超时时间
    const val RETRY_COUNT = 3                // 重试次数
    
    val SUPPORTED_FORMATS = listOf("jpg", "jpeg", "png")  // 支持格式
}
```

## API接口

### 1. 上传异常图片
```
POST /api/exception/pictures/upload
Content-Type: multipart/form-data

参数：
- businessId: 业务单据ID
- businessType: 业务类型
- pictures: 图片文件列表
```

### 2. 删除异常图片
```
POST /api/exception/pictures/delete
Content-Type: application/json

参数：
{
  "businessId": "业务单据ID",
  "pictureUrls": ["图片URL列表"]
}
```

### 3. 查询异常图片
```
GET /api/exception/pictures/{businessId}

返回：图片URL列表
```

## 权限要求

应用需要以下权限（已在AndroidManifest.xml中配置）：
- `android.permission.CAMERA` - 相机权限
- `android.permission.READ_EXTERNAL_STORAGE` - 读取外部存储权限
- `android.permission.WRITE_EXTERNAL_STORAGE` - 写入外部存储权限
- `android.permission.INTERNET` - 网络权限

## 错误处理

系统会处理以下常见错误：
- 权限被拒绝
- 网络连接失败
- 文件大小超限
- 格式不支持
- 服务器错误
- 存储空间不足

## 注意事项

1. **权限处理**：首次使用时会请求相机和存储权限
2. **图片压缩**：系统会自动压缩图片以优化上传速度
3. **网络要求**：上传功能需要网络连接
4. **存储空间**：确保设备有足够的存储空间
5. **API集成**：后端需要实现对应的API接口

## 后续扩展

该功能设计为可扩展的，可以轻松集成到其他业务模块中：
1. 复制相关的UI组件和数据模型
2. 在目标Activity中集成ExceptionPictureDialog
3. 实现对应的API接口
4. 配置相应的业务信息

功能已完成开发并可以投入使用。如有问题或需要调整，请参考代码注释或联系开发团队。
