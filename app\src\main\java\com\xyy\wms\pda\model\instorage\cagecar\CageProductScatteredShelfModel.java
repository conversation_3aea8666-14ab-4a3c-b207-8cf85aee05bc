package com.xyy.wms.pda.model.instorage.cagecar;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.cageCar.CageCarOrderBean;
import com.xyy.wms.pda.bean.instorage.cageCar.CageCarShelfDetailPost;
import com.xyy.wms.pda.bean.instorage.cageCar.CommitCageCarShelfResult;
import com.xyy.wms.pda.contract.instorage.cagecar.CageProductScatteredShelfContract;
import com.xyy.wms.pda.model.checkSelectGoodsPosition.in.CheckSelectGoodsPositionModel;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import io.reactivex.Observable;
/**
 * Created by XyyMvpPdaTemplate on 02/25/2020 12:02
 */
public class CageProductScatteredShelfModel extends BaseModel implements CageProductScatteredShelfContract.ICageProductScatteredShelfModel , CheckSelectGoodsPositionModel {

    public static CageProductScatteredShelfModel newInstance() {
        return new CageProductScatteredShelfModel();
    }

    @Override
    public Observable<BaseResponseBean<CageCarOrderBean>> getRollContainerShelfProductList(String pdaProductLineId, String storageOrderCode) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).getRollContainerShelfProductList(pdaProductLineId, storageOrderCode)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<CommitCageCarShelfResult>> commitCageCarOrderDetail(CageCarShelfDetailPost cageCarShelfDetailPost) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).commitCageCarOrderDetail(cageCarShelfDetailPost)
                .compose(RxHelper.rxSchedulerHelper());
    }
}