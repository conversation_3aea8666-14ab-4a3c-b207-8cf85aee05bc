package com.xyy.wms.pda.bean.out

import java.io.Serializable

/**
 * 出库——整件拣货
 */
class WholePickingListBean : Serializable {

    /**
     * 批拣任务单号
     */
    var batchInspectionCode: String? = null
    /**
     * 商品名称
     */
    var productName: String? = null
    /**
     * 商品编号
     */
    var productCode: String? = null
    /**
     * 作业状态
     */
    var jobState: String? = null

    /**
     * 拣货货位
     */
    var soldOut: String? = null
    /**
     * 拣货数量
     */
    var jobOrderPickingNumber: String? = null
    /**
     * 厂家名称
     */
    var manufacturer: String? = null
    /**
     * 批号
     */
    var batchNumber: String? = null
    /**
     * 商品规格
     */
    var metrologicalSpecification: String? = null
    /**
     * 件包装数量
     */
    var pieceNumber: String? = null
    /**
     * 计量规格
     */
    var specifications: String? = null
    /**
     * 生产日期
     */
    var productionTime: String? = null
    /**
     * 有效期
     */
    var periodValidity: String? = null
    /**
     * 暂存区货位
     */
    var workingAreaBegin: String? = null
}
