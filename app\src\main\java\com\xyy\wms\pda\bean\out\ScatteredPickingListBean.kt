package com.xyy.wms.pda.bean.out

import java.io.Serializable

/**
 * 出库——拆零拣货任务列表
 */
class ScatteredPickingListBean : Serializable {

  var productName: String? = "" // 商品名称
  var batchInspectionCode: String? = null // 批拣任务单号
  var productCode: String? = null // 商品编号
  var soldOut: String? = ""// 货位编码
  var pdaStatus: Int = 0 // 拣货状态 0 表示待拣货（默认） 1 表示已拣货
  var soldOutStatus: Int = 0 // 货位类型	0--未拆分； 1--原货位（-）； 2--新货位（+）
  var filllogId: Long = 0 // 补拣日志表id
  var pid: Long = 0 // 任务明细表id
  var flag: Long = 0 // 补拣不能提交异常标识
  var channelCode: String? = null // 渠道编码
  var pickType: String? = null //拣货类型  1：原始任务类型  2：单据墙
  var erpOrderCode: String? = null // 销售单号
}
