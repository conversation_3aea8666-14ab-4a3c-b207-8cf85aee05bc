package com.xyy.wms.pda.bean.out.pick

import android.os.Parcelable
import com.xyy.wms.pda.bean.out.ReviewStageBean
import kotlinx.android.parcel.Parcelize

/**
 * 复核台数据bean
 */
@Parcelize
data class ReviewStageListBean(
  var pickingComplete: Boolean = false, // 是否拣货任务完成
  var allocateNode: Int = 0, //复核台分配节点 1,2为非设备仓模式，强制投箱
  var CompleteNumber: Int = 0, // 完成的任务数
  var unCompleteNumber: Int = 0, // 未完成的任务数
  var useTime: String = "", // 已用时间
  var list: List<ReviewStageBean>? = null // 列表
) : Parcelable {
  // BCR模式，不显示复核台
  fun isBCR(): Boolean {
    return allocateNode == 3
  }
}
