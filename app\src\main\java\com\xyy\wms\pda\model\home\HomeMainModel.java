package com.xyy.wms.pda.model.home;

import androidx.annotation.NonNull;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.utilslibrary.utils.LogUtils;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.home.PendingOrder;
import com.xyy.wms.pda.bean.user.UserInfoBean;
import com.xyy.wms.pda.bean.user.WarehouseResult;
import com.xyy.wms.pda.contract.home.HomeMainContract;
import com.xyy.wms.pda.model.ServiceModel;
import com.xyy.wms.pda.bean.user.PostUserChooseStoreBean;
import java.util.List;

import java.util.Map;

import io.reactivex.Observable;

/**
 * 主页model
 */
public class HomeMainModel extends ServiceModel implements HomeMainContract.IHomeMainModel {

    @NonNull
    public static HomeMainModel newInstance() {
        return new HomeMainModel();
    }

    @Override
    public Observable<BaseResponseBean<PendingOrder>> getPendingOrderList(Map<String, String> map) {
        return getApiService().getPendingOrderList(map).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<List<WarehouseResult>>> getChooseStoreList() {
        return getApiService().getChooseStoreList().compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<Object>> commitChooseStore(String warehouseCode) {
        return getApiService().commitChooseStoreSuccess(new PostUserChooseStoreBean(warehouseCode)).compose(RxHelper.rxSchedulerHelper());
    }
}
