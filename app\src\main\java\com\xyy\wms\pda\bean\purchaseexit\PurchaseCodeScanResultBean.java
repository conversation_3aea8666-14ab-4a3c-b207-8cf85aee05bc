package com.xyy.wms.pda.bean.purchaseexit;

import java.io.Serializable;

/**
 * Created by lwj on 2020-02-24.
 * <EMAIL>
 */
public class PurchaseCodeScanResultBean implements Serializable {


    //    public String approveNo;//
    public String codeLevel;//        string    @mock = 1
    //    public String codeRelation;// array < object >
    public PurchaseCodeScanCodeRelation createTime;//
    public String createUser;//       string    @mock =4312
    public String id;//
    //    public String isStorageStatus;//
    public String lineNumber;//      ;//  string    @mock =1
    //    public String moveType;//      number    @mock =2
    public String orderCode;//       string    @mock =JHG201912090006
    public String orderType;//       number    @mock =113
    //    public String parentRegulatoryCode;//
//    public String pkgAmount;//
//    public String pkgRatio;//    string    @mock =1:1:1
    public String productBatchCode;//
    public String productCode;//
    //    public String productManufactureDate;//
    public String productName;//
    public String productValidDate;//
    public String regulatoryCode; //
    public int scannedNumber; //   已扫描条目数/已扫描次数
    public int scannedNumberLarge; //   已扫描件包装数
    public int scannedNumberMiddle; // 已扫描中包装数
    public int scannedNumberSmall; //  已扫描小包装数
    public int scannedNumberTotal = 0; //  已扫描总数量
    public String updateTime;//
    public String updateUser;//     string    @mock =4312
//    public String yb;


    public String getCodeLevel() {
        return codeLevel;
    }

    public void setCodeLevel(String codeLevel) {
        this.codeLevel = codeLevel;
    }


    public PurchaseCodeScanCodeRelation getCreateTime() {
        return createTime;
    }

    public void setCreateTime(PurchaseCodeScanCodeRelation createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }


    public String getLineNumber() {
        return lineNumber;
    }

    public void setLineNumber(String lineNumber) {
        this.lineNumber = lineNumber;
    }


    public String getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }


    public String getProductBatchCode() {
        return productBatchCode;
    }

    public void setProductBatchCode(String productBatchCode) {
        this.productBatchCode = productBatchCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }


    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductValidDate() {
        return productValidDate;
    }

    public void setProductValidDate(String productValidDate) {
        this.productValidDate = productValidDate;
    }

    public String getRegulatoryCode() {
        return regulatoryCode;
    }

    public void setRegulatoryCode(String regulatoryCode) {
        this.regulatoryCode = regulatoryCode;
    }

    public int getScannedNumber() {
        return scannedNumber;
    }

    public void setScannedNumber(int scannedNumber) {
        this.scannedNumber = scannedNumber;
    }

    public int getScannedNumberLarge() {
        return scannedNumberLarge;
    }

    public void setScannedNumberLarge(int scannedNumberLarge) {
        this.scannedNumberLarge = scannedNumberLarge;
    }

    public int getScannedNumberMiddle() {
        return scannedNumberMiddle;
    }

    public void setScannedNumberMiddle(int scannedNumberMiddle) {
        this.scannedNumberMiddle = scannedNumberMiddle;
    }

    public int getScannedNumberSmall() {
        return scannedNumberSmall;
    }

    public void setScannedNumberSmall(int scannedNumberSmall) {
        this.scannedNumberSmall = scannedNumberSmall;
    }

    public int getScannedNumberTotal() {
        return scannedNumberTotal;
    }

    public void setScannedNumberTotal(int scannedNumberTotal) {
        this.scannedNumberTotal = scannedNumberTotal;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

}
  

