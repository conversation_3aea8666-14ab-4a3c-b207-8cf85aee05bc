package com.xyy.wms.pda.bean.purchaseexit;

import java.io.Serializable;

/**
 * Created by lwj on 2020-02-24.
 * <EMAIL>
 */
public class PurchaseExitReViewOrderBean implements Serializable {

    public String forcedState;
    public String orgCode;
    public String productCode;    //商品编码	string	@mock=Y3008429
    public String productName;//	商品名称	string	@mock=午时茶颗粒
    public int quantityCompletion; // 分子
    public String refundOrderStatus;
    public int size; // 分母
    public int updateUser;

//    public ArrayList<PurchaseExitReViewGoods> purchaseRefundOrderDetails = new ArrayList<>();// 商品列表


    public String getForcedState() {
        return forcedState;
    }

    public void setForcedState(String forcedState) {
        this.forcedState = forcedState;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public int getQuantityCompletion() {
        return quantityCompletion;
    }

    public void setQuantityCompletion(int quantityCompletion) {
        this.quantityCompletion = quantityCompletion;
    }

    public String getRefundOrderStatus() {
        return refundOrderStatus;
    }

    public void setRefundOrderStatus(String refundOrderStatus) {
        this.refundOrderStatus = refundOrderStatus;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(int updateUser) {
        this.updateUser = updateUser;
    }
}
       
