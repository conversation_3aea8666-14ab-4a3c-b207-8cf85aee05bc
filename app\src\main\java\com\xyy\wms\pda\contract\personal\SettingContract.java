package com.xyy.wms.pda.contract.personal;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;

import io.reactivex.Observable;

/**
 * 设置
 */
public interface SettingContract {

    abstract class SettingPresenter extends BasePresenter<ISettingModel, ISettingView> {

        /**
         * 退出
         *
         */
        public abstract void logout();
    }

    interface ISettingModel extends IBaseModel {

        /**
         * 请求退出接口
         *
         * @return
         */
        Observable<BaseResponseBean> logout();
    }

    interface ISettingView extends IBaseActivity {

        /**
         * 退出成功
         */
        void logoutSuccess();
    }
}
