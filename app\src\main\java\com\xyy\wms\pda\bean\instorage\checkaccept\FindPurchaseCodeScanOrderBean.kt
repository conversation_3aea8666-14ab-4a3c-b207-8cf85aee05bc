package com.xyy.wms.pda.bean.instorage.checkaccept

import java.io.Serializable

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/16
 */
data class FindPurchaseCodeScanOrderBean (
    var productValidDate : String,                                  // 有效期至
    var productManufactureDate : String,                            // 生产日期
    var productBatchCode : String,                                  // 商品批号
    var containerCode : String,                                     // 容器编号
    var productName : String,                                       // 商品名称
    var productPackUnitBigSpecification : String,                   // 大包装规格
    var productPackUnitMediumSpecification : String,                // 中包装规格
    var needSweepCount : String,                                    // 应扫
    var largeCategoryCode: String                                // 商品大类  医疗器械202，不做8开头 20位数字校
) : Serializable
