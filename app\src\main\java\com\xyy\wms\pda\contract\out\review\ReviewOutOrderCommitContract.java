package com.xyy.wms.pda.contract.out.review;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;

import io.reactivex.Observable;
/**
 * 出库——外复核订单
 */
public interface ReviewOutOrderCommitContract {

    interface IReviewOutOrderModel extends IBaseModel {
        /**
         * 外复核提交
         */
        Observable<BaseResponseBean> reviewOutConfirm(String orderCode, String buildingCode
                , String taskOutReviewCode);
    }

    interface IReviewOutOrderView extends IBaseActivity {
        /**
         * 外复核提交成功
         */
        void reviewOutConfirmSuccess();
    }
}
