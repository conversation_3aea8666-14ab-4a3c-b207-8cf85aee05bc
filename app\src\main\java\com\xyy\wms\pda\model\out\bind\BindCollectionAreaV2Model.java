package com.xyy.wms.pda.model.out.bind;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.out.bind.ConsolidationResult;
import com.xyy.wms.pda.contract.out.bind.BindCollectionAreaV2Contract;
import com.xyy.wms.pda.model.ServiceModel;

import java.util.Map;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 04/10/2019 14:59
 */
public class BindCollectionAreaV2Model extends ServiceModel implements BindCollectionAreaV2Contract.IBindCollectionAreaV2Model {

    public static BindCollectionAreaV2Model newInstance() {
        return new BindCollectionAreaV2Model();
    }

    @Override
    public Observable<BaseResponseBean<String>> checkStoreArea(Map<String, Object> params) {
        return getApiOutManagerService().checkStoreArea(params).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<String>> checkBoxCode(Map<String, Object> params) {
        return getApiOutManagerService().checkBoxCode(params).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<ConsolidationResult>> consolidationByBoxCode(Map<String, Object> params) {
        return getApiOutManagerService().consolidationByBoxCode(params).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<String>> bindSubmit(Map<String, Object> params) {
        return getApiOutManagerService().bindSubmit(params).compose(RxHelper.rxSchedulerHelper());
    }
}
