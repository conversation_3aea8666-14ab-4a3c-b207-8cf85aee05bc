package com.xyy.wms.pda.contract.pinback.scatteredShelf;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.IBaseView;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.pinback.ProductBean;
import com.xyy.wms.pda.model.pinback.scatteredShelf.ScatteredShelfListModel;
import com.xyy.wms.pda.ui.activity.pinback.scattered.ScatteredShelfListActivity;

import java.util.List;

import io.reactivex.Observable;

/**
 * 销退零散上架
 */
public interface ScatteredShelfListContract {

    interface IScatteredShelfListModel extends IBaseModel {

        Observable<BaseResponseBean<List<ProductBean>>> getScatteredShelfList(String containerCode, int type);
    }

    interface IScatteredShelfListView extends IBaseView {
        void getScatteredShelfListSuccess(BaseResponseBean<List<ProductBean>> baseResponseBean);
    }

    abstract class ScatteredShelfListPresenter extends BasePresenter<ScatteredShelfListModel, ScatteredShelfListActivity> {

        public abstract void getScatteredShelfList(String containerCode,int type);
    }
}
