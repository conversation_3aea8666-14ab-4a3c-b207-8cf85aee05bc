package com.xyy.wms.pda.contract.home;
import com.xyy.utilslibrary.base.IBaseFragment;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.home.PendingOrder;
import com.xyy.wms.pda.bean.user.UserInfoBean;
import com.xyy.wms.pda.bean.user.WarehouseResult;

import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
/**
 * 主页Contract
 */
public interface HomeMainContract {

    interface IHomeMainModel extends IBaseModel {
        /**
         * 请求模块列表
         *
         * @return
         */
        Observable<BaseResponseBean<PendingOrder>> getPendingOrderList(Map<String, String> map);

        /**
         * 请求仓库列表
         */
        Observable<BaseResponseBean<List<WarehouseResult>>> getChooseStoreList();

        /**
         * 提交选择仓库
         */
        Observable<BaseResponseBean<Object>> commitChooseStore(String storageCode);

    }

    interface IHomeMainView extends IBaseFragment {
        /**
         * 显示模块列表
         */
        void showPendingOrderList(PendingOrder bean);


        void getChooseStoreListSuccess(List<WarehouseResult> warehouseResult);

        void commitChooseStoreSuccess(Object req);
    }
}
