package com.xyy.wms.pda.contract.instorage.scatteredwholeshelf
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.shelf.InShelfResult
import com.xyy.wms.pda.model.instorage.scatteredwholeshelf.ScatteredWholeShelfListModel
import com.xyy.wms.pda.ui.activity.instorage.scatteredwholeshelf.InScatteredWholeListActivity
import io.reactivex.Observable

/**
 * 零散上架列表
 */
interface ScatteredWholeShelfListContract {
    interface IScatteredWholeShelfListModel : IBaseModel {
        fun getScatteredShelfList(containerCode: String, shelfType: Int): Observable<BaseResponseBean<InShelfResult>>
    }
    interface IScatteredWholeShelfListView : IBaseActivity {
        fun getScatteredWholeShelfListSuccess(requestBaseBean: BaseResponseBean<InShelfResult>)
    }
    abstract class ScatteredWholeShelfListPresenter : BasePresenter<ScatteredWholeShelfListModel, InScatteredWholeListActivity>() {
        abstract fun getScatteredShelfList(containerCode: String, shelfType: Int)
    }
}
