package com.xyy.wms.pda.model.moveStorage.wholeshelfdown;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.shelf.CommitShelfResult;
import com.xyy.wms.pda.bean.instorage.shelf.ShelfDetailPost;
import com.xyy.wms.pda.contract.instorage.wholeshelf.WholeShelfProductContract;
import com.xyy.wms.pda.model.checkSelectGoodsPosition.in.CheckSelectGoodsPositionModel;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * 整件商品上架
 */
public class WholeShelfProductModel extends BaseModel implements WholeShelfProductContract.IWholeShelfProductModel, CheckSelectGoodsPositionModel {
    public static WholeShelfProductModel newInstance() {
        return new WholeShelfProductModel();
    }
    /**
     * 单个明细提交
     */
    public Observable<BaseResponseBean<CommitShelfResult>> commitStorageOrderDetail(ShelfDetailPost shelfDetailPost) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).commitStorageOrderDetail(shelfDetailPost)
                .compose(RxHelper.rxSchedulerHelper());
    }

}
