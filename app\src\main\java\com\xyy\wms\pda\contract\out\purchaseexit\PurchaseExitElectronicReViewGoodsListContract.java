package com.xyy.wms.pda.contract.out.purchaseexit;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitReViewGoods;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitReViewOrderBean;

import java.util.List;

import io.reactivex.Observable;

/**
 * Created by lwj on 2020-02-24.
 * <EMAIL>
 * 购进退出复核单 商品列表
 */
public interface PurchaseExitElectronicReViewGoodsListContract {


    interface IPurchaseExitElectronicReViewGoodsListModel extends IBaseModel {

        Observable<BaseResponseBean<List<PurchaseExitReViewGoods>>> queryPerformTheDocuments(
                String packageBarCode,
                String productCode,
                String pickUpOrder);


        Observable<BaseResponseBean<List<PurchaseExitReViewOrderBean>>> queryReviewDetail(
                String pickUpOrder);


    }


    interface IPurchaseExitElectronicReViewGoodsListView extends IBaseActivity {

        void queryPerformTheDocumentsSuccess(String packageBarCode, BaseResponseBean<List<PurchaseExitReViewGoods>> bean);

        void queryReviewDetailSuccess(BaseResponseBean<List<PurchaseExitReViewOrderBean>> bean);
    }

    abstract class IPurchaseExitElectronicReViewGoodsListPresenter extends BasePresenter<IPurchaseExitElectronicReViewGoodsListModel, IPurchaseExitElectronicReViewGoodsListView> {

        public abstract void queryPerformTheDocuments(
                String packageBarCode,
                String productCode,
                String pickUpOrder);


        public abstract void queryReviewDetail(
                String pickUpOrder);

    }


}
