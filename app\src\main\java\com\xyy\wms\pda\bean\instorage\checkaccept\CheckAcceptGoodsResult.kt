package com.xyy.wms.pda.bean.instorage.checkaccept

data class CheckAcceptGoodsResult(
        val buildingCode: String,//	建筑物编码	string	@mock=01
        val buildingName: String,//	建筑物名称	string	@mock=主仓
        val checkOrderCode: String,//验收单号	string	@mock=YSD202002270051
        val checkOrderStatus: String,//	验收单状态	string	@mock=1
        val checkUser: String,//验收员id	string	@mock=781
        val id: String,//	单据主键id	string	@mock=9181
        val occupyType: Int,//	number	@mock=1
        val orgCode: String,//机构编码	string	@mock=D547
        val ownerCode: String,//	业主编码	string	@mock=009
        val purchaseCheckOrderDetailVos: ArrayList<CheckOrderDetail>,//	验收单明细	array<object>
        val receiveOrderCode: String,//收货单号	string	@mock=SHD202002270007
        val receiveUser: String,//	收货员id	string	@mock=781
        val receiveUserName: String,//收货员姓名	string	@mock=张泽宇
        val supplierCode: String,//供应商编号	string	@mock=GPFJIX052001
        val updateTime: Long,//	number	@mock=1583825985321
        val updateUser: String,//	string	@mock=781
        val warehouseCode: String,//	仓库编码	string	@mock=WMS360000_1
        val yn: Int,//	number	@mock=1
        val ysfsTypeDictCode: String,//		string	@mock=YSFS
        val ysgjTypeDictCode: String,//	string	@mock=YSGJ
        val receiveFinishTime: Long // 收货时间	number
)