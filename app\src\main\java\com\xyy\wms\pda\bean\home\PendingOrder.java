package com.xyy.wms.pda.bean.home;

import java.io.Serializable;
import java.util.List;

/**
 * author :lx
 * date 2019/2/27.
 * email： <EMAIL>
 * 待处理订单
 */
public class PendingOrder implements Serializable {

    /**
     * 是否有下一页
     */
    private boolean hasNextPage;
    private boolean isLastPage;

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    private int total;

    public boolean isLastPage() {
        return isLastPage;
    }

    public void setLastPage(boolean lastPage) {
        isLastPage = lastPage;
    }

    private List<ListBean> list;

    public boolean isHasNextPage() {
        return hasNextPage;
    }

    public void setHasNextPage(boolean hasNextPage) {
        this.hasNextPage = hasNextPage;
    }

    public List<ListBean> getList() {
        return list;
    }

    public void setList(List<ListBean> list) {
        this.list = list;
    }

    public static class ListBean implements Serializable {

        /**
         * 待办任务名称
         */
        private String taskName;
        /**
         * 明细单号
         */
        private String detailNo;
        /**
         * 待办任务类型
         * 1--出库拆零拣货；2--出库外复核；3--采购整件上架；4--采购零散上架；5--购进退出执行；6--补货下架；7--补货上架；8--移库上架
         */
        private int taskType;

        public String getTaskName() {
            return taskName;
        }

        public void setTaskName(String taskName) {
            this.taskName = taskName;
        }

        public String getDetailNo() {
            return detailNo;
        }

        public void setDetailNo(String detailNo) {
            this.detailNo = detailNo;
        }

        public int getTaskType() {
            return taskType;
        }

        public void setTaskType(int taskType) {
            this.taskType = taskType;
        }
    }
}
