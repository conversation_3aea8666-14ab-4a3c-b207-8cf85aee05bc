package com.xyy.wms.pda.model.pinback.scatteredShelf;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiPinBackService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.pinback.PostShelfListBean;
import com.xyy.wms.pda.bean.pinback.ProductPosition;
import com.xyy.wms.pda.bean.pinback.ProductPositionListBean;
import com.xyy.wms.pda.bean.pinback.ShelfResult;
import com.xyy.wms.pda.contract.pinback.scatteredShelf.ProductScatteredShelfContract;
import com.xyy.wms.pda.model.checkSelectGoodsPosition.pinback.PinBackCheckGoodsPositionModel;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * 商品零散上架model
 */
public class ProductScatteredShelfModel implements ProductScatteredShelfContract.IProductScatteredShelfModel, PinBackCheckGoodsPositionModel {

    public static ProductScatteredShelfModel newInstance() {

        return new ProductScatteredShelfModel();
    }

    @Override
    public Observable<BaseResponseBean> checkSelectGoodsPosition(ProductPositionListBean<ProductPosition> productPositionListBean) {
        return RetrofitCreateHelper.createApi(ApiPinBackService.class).checkSelectGoodsPosition(productPositionListBean)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<ShelfResult>> commitScatteredShelfList(PostShelfListBean wholeShelfListBean) {
        return RetrofitCreateHelper.createApi(ApiPinBackService.class).commitShelfList(wholeShelfListBean)
                .compose(RxHelper.rxSchedulerHelper());
    }
}
