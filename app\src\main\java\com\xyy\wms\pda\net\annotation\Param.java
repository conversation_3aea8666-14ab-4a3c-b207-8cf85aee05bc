package com.xyy.wms.pda.net.annotation;



import com.xyy.wms.pda.net.convert.CommonConvert;
import com.xyy.wms.pda.net.convert.ParamConvert;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * Created by lwj on 2020/3/30
 * <EMAIL>
 * get 请求参数
 */
@Documented
@Target(FIELD)
@Retention(RUNTIME)
public @interface Param {

    // 参数名 当不指定时为成员变量名
    String name() default "";

    // 默认值 当为空时，指定默认值
    String defaultValue() default "";

    Class<? extends ParamConvert> convert() default CommonConvert.class;

}
