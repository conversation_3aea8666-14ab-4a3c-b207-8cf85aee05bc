package com.xyy.wms.pda.bean.inmanager.post;

/**
 * Created by zcj on 2018/11/14 10
 */
public class ReviewExceptionPostBean {
    /**
     * exceptionCause	异常原因 （1.少货 2.多货）	number
     * exceptionType	异常类型 (1.零货拣货 2.内复核)	number
     * pageNum	当前页	number
     * pageSize	每页的数量	number
     * isAskFor 是否索取 （0 未索取 1 已索取）
     */

    private String exceptionCause;
    private String exceptionType;
    private String pageNum;
    private String pageSize;
    private String status;
    private String isAskFor;

    public String getExceptionCause() {
        return exceptionCause;
    }

    public void setExceptionCause(String exceptionCause) {
        this.exceptionCause = exceptionCause;
    }

    public String getExceptionType() {
        return exceptionType;
    }

    public void setExceptionType(String exceptionType) {
        this.exceptionType = exceptionType;
    }

    public String getPageNum() {
        return pageNum;
    }

    public void setPageNum(String pageNum) {
        this.pageNum = pageNum;
    }

    public String getPageSize() {
        return pageSize;
    }

    public void setPageSize(String pageSize) {
        this.pageSize = pageSize;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIsAskFor() {
        return isAskFor;
    }

    public void setIsAskFor(String isAskFor) {
        this.isAskFor = isAskFor;
    }

    @Override
    public String toString() {
        return "ReviewExceptionPostBean{" +
                "exceptionCause='" + exceptionCause + '\'' +
                ", exceptionType='" + exceptionType + '\'' +
                ", pageNum='" + pageNum + '\'' +
                ", pageSize='" + pageSize + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
