package com.xyy.wms.pda.presenter.instorage.checkaccept

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.helper.SimpleErrorConsumer
import com.xyy.wms.pda.helper.SimpleSuccessConsumer
import com.xyy.wms.pda.contract.instorage.checkaccept.DetailsOfUnAcceptedGoodsContract
import com.xyy.wms.pda.bean.instorage.checkaccept.WaitCheckDetailListPost
import com.xyy.wms.pda.bean.instorage.checkaccept.WaitCheckDetailListBean
import com.xyy.wms.pda.model.instorage.checkaccept.DetailsOfUnAcceptedGoodsModel
/**
 * 验收单-未验收-商品列表页
 * @Description
 * @Date 2022/4/11
 */
class DetailsOfUnAcceptedGoodsPresenter : BasePresenter<DetailsOfUnAcceptedGoodsModel, DetailsOfUnAcceptedGoodsContract.DetailsOfUnAcceptedGoodsContractView>() {

    override fun getModel(): DetailsOfUnAcceptedGoodsModel {
        return DetailsOfUnAcceptedGoodsModel.newInstance()
    }
    companion object {
      fun newInstance(): DetailsOfUnAcceptedGoodsPresenter = DetailsOfUnAcceptedGoodsPresenter()
    }
    fun getWaitCheckDetailList(waitCheckDetailListPost: WaitCheckDetailListPost) {
      mRxManager.register(mIModel.getWaitCheckDetailList(waitCheckDetailListPost)
        .subscribe(object :
          SimpleSuccessConsumer<BaseResponseBean<List<WaitCheckDetailListBean>>>(mIView, "请求中···") {
          override fun onSuccess(responseBean : BaseResponseBean<List<WaitCheckDetailListBean>>) {
            mIView.getWaitCheckDetailListSuccess(responseBean)
          }
        }, SimpleErrorConsumer(mIView))
      )
    }

}
