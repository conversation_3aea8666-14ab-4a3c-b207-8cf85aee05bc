package com.xyy.wms.pda.contract.out.review

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.out.ReviewOutListBean
import com.xyy.wms.pda.bean.out.ReviewOutOrderBean
import com.xyy.wms.pda.bean.out.outsideReview.Building
import io.reactivex.Observable

/**
 * 出库——外复合任务列表
 */
interface ReviewOutListContract {
    interface IReviewOutListModel : IBaseModel {
        /**
         * 请求外复合任务列表
         */
        fun getReviewOutList(map: Map<String, String?>): Observable<BaseResponseBean<ReviewOutListBean>>
        /**
         * 外复核 取消出库订单
         */
        fun cancelOutReceiveOrder(map: Map<String, String?>): Observable<BaseResponseBean<*>>
        /**
         * 外复核 索取任务并获取订单详情
         */
        fun requestReviewTask(map: Map<String, String?>): Observable<BaseResponseBean<ReviewOutOrderBean>>
        /**
         * 外复核 索取建筑物 列表
         */
        fun getAllBuildingCode(): Observable<BaseResponseBean<MutableList<Building>>>
    }

    interface IReviewOutListView : IBaseActivity {
        /**
         * 获取外复合任务列表成功
         */
        fun getReviewOutListSuccess(bean: ReviewOutListBean?)

        /**
         * 外复核取消出库订单成功
         */
        fun cancelOutReceiveOrderSuccess(baseResponseBean: BaseResponseBean<*>)

        /**
         * 显示网络错误
         */
        fun showNetworkError()

        /**
         * 外复核 索取任务并获取订单详情成功
         */
        fun requestReviewTaskSuccess(bean: ReviewOutOrderBean?)

        /**
         * 外复核 索取建筑物 列表 成功
         */
        fun getAllBuildingCodeSuccess(list: MutableList<Building>?)
    }
}