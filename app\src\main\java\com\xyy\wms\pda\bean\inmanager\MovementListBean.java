package com.xyy.wms.pda.bean.inmanager;

import java.io.Serializable;
import java.util.List;

/**
 * 移库单列表
 * billState	        单据状态 0，初始状态 1，待下架 2，待上架 3，已完成 4，已取消	string	0，待移库 1，2统称移库中
 * createTime	        创建时间	                                                number	@mock=1539808138000
 * id	                主键	                                                    number	@mock=41
 * inStorageTypeCode	移入库别编码
 * inStorageTypeName	移入库别名称	                                            string	@mock=零货库
 * movementNo	        移库单号	                                                string	@mock=YKD181018000012
 * outStorageTypeCode	移出库别编码	                                            string
 * outStorageTypeName	移出库别名称	                                            string	@mock=零货库
 */
public class MovementListBean implements Serializable {

    private List<ListBean> list;
    private int pageNum;
    private int pageSize;
    private int total;

    public static class ListBean implements Serializable {
        String id; //主键
        String createTime;
        String inStorageTypeCode;
        String billState;
        String inStorageTypeName;
        String inStorageRoomCode;
        String inStorageRoomName;
        String outStorageRoomCode;
        String movementNo;
        String outStorageTypeCode;
        String outStorageTypeName;
        String outStorageRoomName;
        String ownerName;
        String dictName;
        String buildingName;
        String createUserName;
        String productCnt;

        public String getInStorageRoomCode(){return inStorageRoomCode;}
        public void setInStorageRoomCode(String inStorageRoomCode){this.inStorageRoomCode=inStorageRoomCode;}
        public String getInStorageRoomName(){return inStorageRoomName;}
        public void setInStorageRoomName(String inStorageRoomName){this.inStorageRoomName = inStorageRoomName;}
        public String getOutStorageRoomCode(){return outStorageRoomCode;}
        public void setOutStorageRoomCode(String outStorageRoomCode){this.outStorageRoomCode = outStorageRoomCode;}
        public String getOutStorageRoomName(){return outStorageRoomName;}
        public void setOutStorageRoomName(String outStorageRoomName){this.outStorageRoomName=outStorageRoomName;}
        public String getDictName(){return dictName;}
        public void setDictName(String dictName){this.dictName = dictName;}
        public String getCreateUserName(){return createUserName;}
        public void setCreateUserName(String createUserName){this.createUserName = createUserName;}
        public String getProductCnt(){return productCnt;}
        public void setProductCnt(String productCnt){this.productCnt = productCnt;}

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getInStorageTypeCode() {
            return inStorageTypeCode;
        }

        public void setInStorageTypeCode(String inStorageTypeCode) {
            this.inStorageTypeCode = inStorageTypeCode;
        }

        public String getBillState() {
            return billState;
        }

        public void setBillState(String billState) {
            this.billState = billState;
        }

        public String getInStorageTypeName() {
            return inStorageTypeName;
        }

        public void setInStorageTypeName(String inStorageTypeName) {
            this.inStorageTypeName = inStorageTypeName;
        }

        public String getMovementNo() {
            return movementNo;
        }

        public void setMovementNo(String movementNo) {
            this.movementNo = movementNo;
        }

        public String getOutStorageTypeCode() {
            return outStorageTypeCode;
        }

        public void setOutStorageTypeCode(String outStorageTypeCode) {
            this.outStorageTypeCode = outStorageTypeCode;
        }

        public String getOutStorageTypeName() {
            return outStorageTypeName;
        }

        public void setOutStorageTypeName(String outStorageTypeName) {
            this.outStorageTypeName = outStorageTypeName;
        }

        public String getOwnerName() {
            return ownerName;
        }

        public void setOwnerName(String ownerName) {
            this.ownerName = ownerName;
        }

        public String getBuildingName() {
            return buildingName;
        }

        public void setBuildingName(String buildingName) {
            this.buildingName = buildingName;
        }

        @Override
        public String toString() {
            return "ListBean{" + "id=" + id + ", createTime=" + createTime + ", inStorageTypeCode=" + inStorageTypeCode + ", billState='" + billState + '\'' + ", inStorageTypeName='" + inStorageTypeName + '\'' + ", movementNo='" + movementNo + '\'' + ", outStorageTypeCode='" + outStorageTypeCode + '\'' + ", outStorageTypeName='" + outStorageTypeName + '\'' + '}';
        }
    }

    public List<ListBean> getList() {
        return list;
    }

    public void setList(List<ListBean> list) {
        this.list = list;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    @Override
    public String toString() {
        return "MovementListBean{" + "list=" + list + ", pageNum=" + pageNum + ", pageSize=" + pageSize + ", total=" + total + '}';
    }
}
