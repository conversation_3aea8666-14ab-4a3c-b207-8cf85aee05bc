package com.xyy.wms.pda.bean.user;

import java.io.Serializable;

/**
 * 用户信息
 */
public class UserInfoBean implements Serializable {

    private String employeeNumber;// 员工编号
    private String mobile;// 手机号
    private String name;// 用户姓名
    private String realname; //用户名-新
    private String postName;// 岗位名字
    private String orgCode;// 机构编码
    private String oaId; //唯一 ID
    private String account;// 账户名称
    private String staffNum; //员工编号

    public void setStaffNum(String staffNum) {
        this.staffNum = staffNum;
    }

    public String getStaffNum() {
        return staffNum;
    }

    public void setRealname(String realname) {
        this.realname = realname;
    }

    public String getRealname() {
        return realname;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getEmployeeNumber() {
        return employeeNumber;
    }

    public void setEmployeeNumber(String employeeNumber) {
        this.employeeNumber = employeeNumber;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOaId() {
        return oaId;
    }

    public void setOaId(String oaId) {
        this.oaId = oaId;
    }
}
