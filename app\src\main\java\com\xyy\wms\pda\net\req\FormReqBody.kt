package com.xyy.wms.pda.net.req

import okhttp3.FormBody
import okhttp3.MediaType
import okhttp3.RequestBody

/**
 * Created by lwj on 2020-03-26.
 * <EMAIL>
 */
abstract class FormReqBody : IReqBody {
    override fun buildMediaType(): MediaType {
        return IReqBody.FORM_MEDIA_TYPE
    }

    protected open fun buildForm(builder: FormBody.Builder) {

    }

    override fun buildReqBody(): RequestBody {
        val formBuilder = FormBody.Builder()
        buildForm(formBuilder)
        return formBuilder.build()
    }
}