package com.xyy.wms.pda.contract.out.purchaseexit;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitBillBean;

import java.util.List;
import java.util.Map;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 01/13/2020 10:50
 */
public interface PurchaseExitBillFragmentContract {

    interface IPurchaseExitBillModel extends IBaseModel {

        /**
         * 获取单据列表
         */
        Observable<BaseResponseBean<List<PurchaseExitBillBean>>> getPurchaseExitBillList(Map<String, Object> map);
        Observable<BaseResponseBean> queryContainerValidity(Map<String, String> map);

    }

    interface IPurchaseExitBillView extends IBaseActivity {
        void getPurchaseExitBillListSuccess(List<PurchaseExitBillBean> listRequestBaseBean);
        void queryContainerValiditySuccess(String containerCode);
    }
}
