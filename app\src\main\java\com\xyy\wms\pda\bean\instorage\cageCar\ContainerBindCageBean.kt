package com.xyy.wms.pda.bean.instorage.cageCar

import java.io.Serializable

data class ContainerBindCageBean(
        val containerCode: String?,//	容器编号	string	@mock=325045
        val productCount: Int?,//	品种数	number	@mock=1
        val rollContainerCode: String?,    //笼车编号	string	@mock=LC0529
        val rollContainerLocationCode: String?,//	笼车位置号	string	@mock=01
        val storageAreaCodes: String?//	库区编号	string	@mock=
) : Serializable