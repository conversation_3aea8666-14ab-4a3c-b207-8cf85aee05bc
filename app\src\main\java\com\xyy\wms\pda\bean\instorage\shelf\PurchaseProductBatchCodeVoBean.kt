package com.xyy.wms.pda.bean.instorage.shelf

import java.io.Serializable

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/14
 */
data class PackageBarCodeBean (
  var orgCode : String,                           // 机构编码
  var orgName : String,                           // 机构名称
  var productName : String,                       // 商品名称
  var commonName : String,                        // 通用名
  var prescriptionClassification : String,        // 处方分类
  var marketAuthor : String,                      // 上市许可持有人
  var middlePackingNumber : String,               // 中包装规格
  var packingUnit : String,                       // 包装单位
  var specifications : String,                    // 规格
  var registrationCertificate : String,           // 进口注册号
  var productCode : String,                       // 商品编号
  var manufacturer : String,                      // 厂家名称
  var largePackingNumber : String,                // 件包装规格
  var producingArea : String,                     // 产地
  var approvalNumbers : String,                   // 批准文号
  var sterilizingBatchNumber : String,            // 灭菌批号
  var ephedrineFlag: Boolean,                     //是否含麻
  var smallPackageBarCode: String,                //69码
  var purchaseProductBatchCodeVoList : List<PurchaseProductBatchCodeVoBean>
) : Serializable

data class PurchaseProductBatchCodeVoBean (
    var productBatchCode : String,                  // 批号
    var productManufactureDate : String,            // 生产日期
    var productValidDate : String,                  // 有效期至
    var productCountBig : String,                   // 收获件数
    var productCountScatter : String,               // 收获零散数
    var productPackingBigNumber : Int,              // 件包装数量
    var productCountSmall : String,                 // 收获数量
    var productType : Int,                          // 商品类型 0 - 普通药品, 1 - 中药, 2 - 器械, 3 - 赠品, 4 - 非药, 5 - 二精, 6 - 蛋肽, 7 - 冷藏, 8 - 冷冻, 9 - 恒温冷冻
    var productCode : String,                       // 商品编号
    var ownerCode : String,                         // 业主
    var buildingCode : String,                      // 建筑物
    var channelCode : String,                       // 渠道
    var sterilizingBatchNumber : String,            // 灭菌批号
    var productPackingMiddleNumber : Int,           // 中包装数量
    var storageClassification : Int                // 存储分类(0 整散分开  1整散合一)
) : Serializable
