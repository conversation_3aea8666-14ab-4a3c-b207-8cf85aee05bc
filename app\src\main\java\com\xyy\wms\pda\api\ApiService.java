package com.xyy.wms.pda.api;

import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.common.DictListByNameAndType;
import com.xyy.wms.pda.bean.common.RunningTaskResult;
import com.xyy.wms.pda.bean.driver.CarrierBean;
import com.xyy.wms.pda.bean.driver.DriverBean;
import com.xyy.wms.pda.bean.driver.DriverEditBean;
import com.xyy.wms.pda.bean.driver.DriverInfoBean;
import com.xyy.wms.pda.bean.driver.UploadPicBean;
import com.xyy.wms.pda.bean.home.HomeListBeanNew;
import com.xyy.wms.pda.bean.home.HomeListBean;
import com.xyy.wms.pda.bean.home.PendingOrder;
import com.xyy.wms.pda.bean.moveStorage.AddTaskBean;
import com.xyy.wms.pda.bean.moveStorage.DriverRequest;
import com.xyy.wms.pda.bean.moveStorage.LogicBean;
import com.xyy.wms.pda.bean.moveStorage.LogicReqBean;
import com.xyy.wms.pda.bean.moveStorage.PalletGoodsBean;
import com.xyy.wms.pda.bean.moveStorage.PalletOnPreviewResult;
import com.xyy.wms.pda.bean.moveStorage.StartTaskBean;
import com.xyy.wms.pda.bean.moveStorage.StartTaskResult;
import com.xyy.wms.pda.bean.productCode.ProductCode;
import com.xyy.wms.pda.bean.update.VersionBeanInfo;
import com.xyy.wms.pda.bean.update.VersionInfo;
import com.xyy.wms.pda.bean.user.LoginInfo;
import com.xyy.wms.pda.bean.user.PostUserChooseStoreBean;
import com.xyy.wms.pda.bean.user.UserInfoBean;
import com.xyy.wms.pda.bean.user.WarehouseResult;
import com.xyy.wms.pda.bean.req.LoginReq;

import java.util.List;
import java.util.Map;

import io.reactivex.Flowable;
import io.reactivex.Observable;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.http.Body;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.Part;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;
import retrofit2.http.Streaming;
import retrofit2.http.Url;

public interface ApiService {
    /**
     * 登录
     */
    @POST("basicdata/login")
    Observable<BaseResponseBean<String>> login(@Body LoginReq loginReq);

    /**
     * 退出
     */
    @POST("basicdata/logout")
    Observable<BaseResponseBean> logout();

    /**
     * 版本升级
     */
    @POST("gateway/basicdata/versionUpgradeInfo")
    Observable<BaseResponseBean<VersionInfo>> checkUpdate(@Body VersionBeanInfo versionBeanInfo);

    /**
     * 下载apk
     */
    @Headers("Accept-Encoding: identity")
    @Streaming
    @GET
    Flowable<ResponseBody> downloadApk(@Url String url);

    /**
     * 首页菜单列表
     */
    @POST("basicdata/index")
    Observable<BaseResponseBean<HomeListBeanNew>> getHomeList();

    /**
     * 首页待办任务
     */
    @GET("backlog/queryBacklogListByCondition")
    Observable<BaseResponseBean<PendingOrder>> getPendingOrderList(@QueryMap Map<String, String> map);

    /**
     * 公共 - 商品条码校验
     */
    @GET("pdaWarehouseMovement/checkProductBarCode")
    Observable<BaseResponseBean<String>> checkProductBarCode(@QueryMap Map<String, Object> map);

    /**
     * 公共- 容器校验
     */
    @GET("pdaWarehouseMovement/getContainerStats")
    Observable<BaseResponseBean<String>> getContainerStats(@Query("containerCode") String containerCode, @Query("movementNo") String movementNo);

    /**
     * 公共 - 根据商品条码查询商品信息
     * ownerCode 业主编号(个人信息 机构编码)
     * packageBarCode 	条码
     */
    @POST("instock/wmsApp/purchase/storageOrder/selectProductPda")
    Observable<BaseResponseBean<List<ProductCode>>> selectProductPda(@Query("ownerCode") String ownerCode, @Query("packageBarCode") String packageBarCode);

    /**
     * 获取仓库列表
     */
    @POST("basicdata/selectOwnWarehouses")
    Observable<BaseResponseBean<List<WarehouseResult>>> getChooseStoreList();

    /**
     * 提交仓库
     */
    @POST("basicdata/selectWarehouse")
    Observable<BaseResponseBean<Object>> commitChooseStoreSuccess(@Body PostUserChooseStoreBean req);

    @POST("migrate/startTask")
    Observable<BaseResponseBean<StartTaskResult>> self(@Body StartTaskBean taskBean);

    @POST("migrate/selectLogicalRegionByPallet")
    Observable<BaseResponseBean<LogicBean>> getLogic(@Body LogicReqBean logicBean);

    @POST("migrate/selectProductsByPallet")
    Observable<BaseResponseBean<List<PalletGoodsBean>>> getPalletGoods(@Body LogicReqBean logicBean);

    @POST("migrate/addTaskDetail")
    Observable<BaseResponseBean> addTaskDetail(@Body AddTaskBean taskBean);

    @POST("migrate/submitTask")
    Observable<BaseResponseBean> commitTask(@Body StartTaskResult taskBean);

    @POST("migrate/selectRunningTask")
    Observable<BaseResponseBean<RunningTaskResult>> getRunningTask();

    //零货上架巷道预览
    @POST("/wmsApp/migrate/selectPalletOnPreview")
    Observable<BaseResponseBean<PalletOnPreviewResult>> getPalletOnPreview(@Body LogicReqBean logicBean);

    /**
     * 根据字典类型和字典名称模糊查询
     */
    @GET("dictBasesPda/getDictListByNameAndType")
    Observable<BaseResponseBean<List<DictListByNameAndType>>> getDictListByNameAndType(@Query("dictName") String dictName, @Query("dictType") String dictType);

    @POST("/wmsApp/driver/selectDriverList")
    Observable<BaseResponseBean<List<DriverBean>>> getDriverListData(@Body DriverRequest driverRequest);

    @POST("/wmsApp/driver/selectDriverById")
    Observable<BaseResponseBean<DriverEditBean>> getDriverInfo(@Body DriverInfoBean infoBean);

    @POST("/wmsApp/driver/addDriver")
    Observable<BaseResponseBean> addDriverInfo(@Body DriverEditBean editBean);

    @POST("/wmsApp/driver/updateDriver")
    Observable<BaseResponseBean> editDriverInfo(@Body DriverEditBean editBean);

    @Multipart
    @POST("/wmsApp/driver/upload")
    Observable<BaseResponseBean> uploadFile(@Part MultipartBody.Part file);

    @POST("/wmsApp/driver/carriers")
    Observable<BaseResponseBean<CarrierBean>> getCarrier();
}
