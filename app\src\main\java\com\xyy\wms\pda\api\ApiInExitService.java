package com.xyy.wms.pda.api;

import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.common.DictParam;
import com.xyy.wms.pda.bean.common.DictParamResult;
import com.xyy.wms.pda.bean.instorage.BatchCodeResultBean;
import com.xyy.wms.pda.bean.instorage.cageCar.BindingForRollPost;
import com.xyy.wms.pda.bean.instorage.cageCar.CageCarOrderBean;
import com.xyy.wms.pda.bean.instorage.cageCar.CageCarShelfDetailPost;
import com.xyy.wms.pda.bean.instorage.cageCar.CageCarShelfResult;
import com.xyy.wms.pda.bean.instorage.cageCar.CommitCageCarShelfResult;
import com.xyy.wms.pda.bean.instorage.cageCar.ContainerCageCar;
import com.xyy.wms.pda.bean.instorage.cageCar.ContainerProductDetailBean;
import com.xyy.wms.pda.bean.instorage.cageCar.ScanCageCar;
import com.xyy.wms.pda.bean.instorage.container.ScanContainerQueryResult;
import com.xyy.wms.pda.bean.instorage.shelf.CheckSelectGoodsPositionBean;
import com.xyy.wms.pda.bean.instorage.shelf.CommitShelfResult;
import com.xyy.wms.pda.bean.instorage.shelf.InShelfResult;
import com.xyy.wms.pda.bean.instorage.shelf.ShelfDetailPost;
import com.xyy.wms.pda.bean.purchaseexit.CacheAreaPdaBean;
import com.xyy.wms.pda.bean.purchaseexit.DrugRegulatoryCodeBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseCodeScanResultBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseCodeScannedResultBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseCommitResult;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitBillBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitDetail;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitDoingBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitElectronicCodeBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitElectronicReceiptsBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitReViewGoods;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitReViewListBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitReViewOrderBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitReViewPrivUser;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseRefundOrderDetailsBean;
import com.xyy.wms.pda.bean.purchaseexit.UpdateReviewStatusBean;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
import retrofit2.http.Body;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;

/**
 * 入库和购进退出
 */
public interface ApiInExitService {

    /**
     * 购进退出 - 获取购进退出单列表
     */
    @GET("purchase/refundOrder/queryContainerBoundDocuments")
    Observable<BaseResponseBean<List<PurchaseExitBillBean>>> getPurchaseExitBillList(@QueryMap Map<String, Object> map);

    /**
     * 购进退出 - 校验容器编号
     */
    @GET("purchase/refundOrder/queryContainerValidity")
    Observable<BaseResponseBean> queryContainerValidity(@QueryMap Map<String, String> map);

    /**
     * 购进退出 - 验证商品条形码
     */
    @GET("purchase/refundOrder/queryPackageBarValidity")
    Observable<BaseResponseBean> queryPackageBarValidity(@QueryMap Map<String, String> map);

    /**
     * 购进退出单
     * 查询当前执行单据
     */
    @GET("purchase/refundOrder/queryPerformDocument")
    Observable<BaseResponseBean<List<PurchaseExitDoingBean>>> queryPerformDocument(@QueryMap Map<String, Object> map);

    /**
     * 购进退出单
     * 查询商品展示顺序
     */
    @GET("purchase/refundOrder/queryExecutionDetailList")
    Observable<BaseResponseBean<List<PurchaseExitDetail>>> queryExecutionDetailList(@QueryMap Map<String, Object> map);

    /**
     * 购进退出 - 执行提交 （明细提交）
     */
    @POST("purchase/refundOrder/saveImplement")
    Observable<BaseResponseBean<PurchaseCommitResult>> commitPurchaseExitDetail(@Body PurchaseExitDetail commitPost);

    /**
     * 购进退出电子追溯码
     * 通过容器编号查询
     */
    @GET("purchase/refundOrder/queryUnScannedGoods")
    Observable<BaseResponseBean<List<PurchaseRefundOrderDetailsBean>>> queryUnScannedGoods(@Query("containerCode") String containerCode);


    /**
     * 购进退出电子追溯码
     * 解锁商品
     */
    @FormUrlEncoded
    @POST("purchase/refundOrder/saveTheUnlock")
    Observable<BaseResponseBean> saveTheUnlock(@Field("lineNumber") String productBatchCode,
                                               @Field("productCode") String productCode,
                                               @Field("refundOrderCode") String refundOrderCode);

    /**
     * 购进退出电子追溯码 -整单解锁
     */
    @FormUrlEncoded
    @POST("purchase/refundOrder/updateWholeSingleUnlock")
    Observable<BaseResponseBean> updateWholeSingleUnlock(@Field("refundOrderCode") String refundOrderCode);

    /**
     * 入库 （整体或者零散）
     * 根据容器编号查询 上架单()
     */
    @POST("instock/wmsApp/purchase/storageOrder/getStorageOrder")
    Observable<BaseResponseBean<InShelfResult>> getStorageOrder(@Query("containerCode") String containerCode,
                                                                @Query("shelfType") int shelfType);

    /**
     * 货位选择校验
     */
    @POST("instock/wmsApp/purchase/storageOrder/checkSelectGoodsPosition")
    Observable<BaseResponseBean<CheckSelectGoodsPositionBean>> checkSelectGoodsPosition(@Body Map<String, String> map);

    /**
     * 单条监管码查询
     */
    @GET("purchase/refundOrder/findDrugRegulatoryCode")
    Observable<BaseResponseBean<DrugRegulatoryCodeBean>> findDrugElectronicSuperVisionCode(@QueryMap HashMap<String, String> map);


    /**
     * 查询已扫描数量
     */
    @GET("purchase/refundOrder/findScannedQuantity")
    Observable<BaseResponseBean<DrugRegulatoryCodeBean>> findScannedQuantity(@Query("lineNumber") String lineNumber, @Query("orderCode") String orderCode);

    /**
     * 入库的  提交上架明细
     */
    @POST("instock/wmsApp/purchase/storageOrder/executeStorageOrderDetail")
    Observable<BaseResponseBean<CommitShelfResult>> commitStorageOrderDetail(@Body ShelfDetailPost shelfDetailPost);


    /**
     * 购进退出监管码-查询扫描商品列表
     *
     * @param beginTime    开始时间
     * @param endTime      结束时间
     * @param scanStatus   1未扫描2已扫描
     * @param supplierName 商品批号
     * @param pickUpOrder  单据编号
     * @return
     */
    @GET("purchase/refundOrder/queryScanDocuments")
    Observable<BaseResponseBean<List<PurchaseExitElectronicCodeBean>>> queryReceipts(
            @Query("beginTime") String beginTime,
            @Query("endTime") String endTime,
            @Query("scanStatus") String scanStatus,
            @Query("pickUpOrder") String pickUpOrder,
            @Query("supplierName") String supplierName
    );


    /**
     * 购进退出监管码-查询扫描商品列表
     *
     * @param productName      商品名称
     * @param lineNumber       行号
     * @param productBatchCode 商品批号
     * @param pickUpOrder      单据编号
     * @return
     */
    @GET("purchase/refundOrder/queryReviewGoods")
    Observable<BaseResponseBean<List<PurchaseExitElectronicReceiptsBean>>> queryElectronicReceipts(
            @Query("lineNumber") String lineNumber,
            @Query("pickUpOrder") String pickUpOrder,
            @Query("productBatchCode") String productBatchCode,
            @Query("productName") String productName
    );

    /**
     * 购进退出监管码-扫码
     *
     * @param lineNumber  单据明细行号
     * @param pickUpOrder 单据编号
     * @return
     */
    @GET("purchase/refundOrder/findDrugRegulatoryCode")
    Observable<BaseResponseBean<PurchaseCodeScanResultBean>> findDrugRegulatoryCode(
            @Query("actualRefundCount") String actualRefundCount,
            @Query("codeLevel") String codeLevel,
            @Query("lineNumber") String lineNumber,
            @Query("pickUpOrder") String pickUpOrder,
            @Query("productCode") String productCode,
            @Query("regulatoryCode") String regulatoryCode
    );


    /**
     * 购进退出监管码-查询已扫描追溯码
     *
     * @param lineNumber  单据明细行号
     * @param pickUpOrder 单据编号
     * @return
     */
    @GET("purchase/refundOrder/queryDrugregulatorycode")
    Observable<BaseResponseBean<List<PurchaseCodeScannedResultBean>>> queryDrugregulatorycode(@Query("lineNumber") String lineNumber, @Query("pickUpOrder") String pickUpOrder);

    /**
     * 购进退出监管码-删除追溯码
     *
     * @param lineNumber     单据明细行号
     * @param pickUpOrder    单据编号
     * @param regulatoryCode 电子监管码
     * @return
     */
    @GET("purchase/refundOrder/deleteDrugregulatorycode")
    Observable<BaseResponseBean> deleteDrugregulatorycode(@Query("lineNumber") String lineNumber, @Query("pickUpOrder") String pickUpOrder, @Query("regulatoryCode") String regulatoryCode);

    /**
     * 购进退出复核-查询单据列表
     *
     * @param containerCode     容器编号
     * @param refundOrderStatus 单据状态 4未执行 6执行中
     * @return
     */
    @GET("purchase/refundOrder/queryReviewDocuments")
    Observable<BaseResponseBean<List<PurchaseExitReViewListBean>>> queryReviewDocuments(
            @Query("containerCode") String containerCode,
            @Query("refundOrderStatus") String refundOrderStatus);

    /**
     * 购进退出复核-查询单据列表
     *
     * @param pickUpOrder 拣货单编号
     * @return
     */
    @GET("purchase/refundOrder/queryReviewDetail")
    Observable<BaseResponseBean<List<PurchaseExitReViewOrderBean>>> queryReviewDetail(
            @Query("pickUpOrder") String pickUpOrder);


    /**
     * 购进退出复核-查询商品列表
     * <p>
     * pickUpOrder 和  productCode 必填一项
     *
     * @param packageBarCode 商品条码
     * @param pickUpOrder    单据编号
     * @param productCode    商品编码
     * @return
     */
    @GET("purchase/refundOrder/queryPerformTheDocuments")
    Observable<BaseResponseBean<List<PurchaseExitReViewGoods>>> queryPerformTheDocuments(
            @Query("packageBarCode") String packageBarCode,
            @Query("productCode") String productCode,
            @Query("pickUpOrder") String pickUpOrder);


    /**
     * 购进退出复核-明细提交
     *
     * @param lineNumber  行号
     * @param pickUpOrder 单据编号
     * @return
     */
    @GET("purchase/refundOrder/updateReviewDetailStatus")
    Observable<BaseResponseBean<PurchaseExitElectronicReceiptsBean>> updateReviewDetailStatus(
            @Query("lineNumber") String lineNumber,

            @Query("pickUpOrder") String pickUpOrder);


    /**
     * 购进退出复核-明细提交
     *
     * @param refundShelfTemporary 退货区货位
     * @param pickUpOrder          单据编号
     * @param modifyOrder          备注
     * @return
     */
    @GET("purchase/refundOrder/updateReviewStatus")
    Observable<BaseResponseBean<UpdateReviewStatusBean>> updateReviewStatus(
            @Query("refundOrderCode") String refundOrderCode,
            @Query("refundRemark") String modifyOrder,
            @Query("pickUpOrder") String pickUpOrder,
            @Query("refundShelfTemporary") String refundShelfTemporary,
            @Query("secondRecheckUser") String secondRecheckUser
    );

    /**
     * 购进退出复核-5.查询复核员
     *
     * @param userName 用户名称
     * @return
     */
    @GET("purchase/refundOrder/selectListSysPrivUser")
    Observable<BaseResponseBean<List<PurchaseExitReViewPrivUser>>> selectListSysPrivUser(@Query("userName") String userName);


    /**
     * 购进退出复核-查询退货暂存区
     *
     * @return
     */
    @GET("cacheAreaPda/getCacheAreaPda")
    Observable<BaseResponseBean<CacheAreaPdaBean>> getCacheAreaPda();


    /**
     * 入库
     * 获取笼车上架任务列表
     */
    @GET("purchase/storageOrder/getRollContainerShelfList")
    Observable<BaseResponseBean<CageCarShelfResult>> getRollContainerShelfList(@Query("rollContainerCode") String rollContainerCode);

    /**
     * 入库
     * 根据 笼车上架列表页获取 单个商品集合
     */
    @GET("purchase/storageOrder/getRollContainerShelfProductList")
    Observable<BaseResponseBean<CageCarOrderBean>> getRollContainerShelfProductList(
            @Query("pdaProductLineId") String pdaProductLineId,
            @Query("storageOrderCode") String storageOrderCode);

    /**
     * 入库的  笼车上架提交
     */
    @POST("purchase/storageOrder/executeRollContainerShelf")
    Observable<BaseResponseBean<CommitCageCarShelfResult>> commitCageCarOrderDetail(@Body CageCarShelfDetailPost carShelfDetailPost);

    /**
     * 入库的  获取 笼车 上架的容器信息
     */
    @GET("purchase/storageOrder/getStorageContainersInfo")
    Observable<BaseResponseBean<ContainerCageCar>> getStorageContainersInfo(
            @Query("containerCode") String containerCode,
            @Query("rollContainerCode") String rollContainerCode);

    /**
     * 入库的  扫描笼车上架
     */
    @GET("purchase/storageOrder/getStorageBoundContainersInfo")
    Observable<BaseResponseBean<ScanCageCar>> getStorageBoundContainersInfo(
            @Query("containerCode") String containerCode,
            @Query("rollContainerCode") String rollContainerCode);

    /**
     * 入库的  对笼车进行绑定
     */
    @POST("purchase/storageOrder/doBindingForRoll")
    Observable<BaseResponseBean> doBindingForRoll(@Body BindingForRollPost bindingForRollPost);

    /**
     * 入库的  获取 获取容器中的商品 明细列表
     */
    @GET("purchase/storageOrder/getDetailsByContainer")
    Observable<BaseResponseBean<List<ContainerProductDetailBean>>> getDetailsByContainer(
            @Query("containerCode") String containerCode);


    /**
     * 入库的 根据上架类型获取待上架任务数
     */
    @POST("instock/wmsApp/purchase/storageOrder/getStorageWaitShelfCount")
    Observable<BaseResponseBean<Integer>> getStorageWaitShelfCount(
            @Query("shelfType") String shelfType);

    /**
     * 入库的 查询容器号状态
     */
    @POST("instock/wmsApp/purchase/storageOrder/getStorageShelfContainerState")
    Observable<BaseResponseBean<ScanContainerQueryResult>> queryContainerStatus(@Query("containerCode") String containerCode);

//    /**
//     * 整件、零散上架商品详情获取批号
//     */
//    @POST("instock/wmsApp/purchase/storageOrder/getProductValidBatchCodes")
//    Observable<BaseResponseBean<String[]>> getBatchCodes(@Query("productCode") String productCode);

    /**
     * 整件、零散上架商品详情获取批号 - 新
     */
    @POST("instock/wmsApp/purchase/storageOrder/getProductValidBatchCodeInfo")
    Observable<BaseResponseBean<List<BatchCodeResultBean>>> getBatchCodes(@Query("productCode") String productCode);

    /**
     * 上架批号开关
     */
    @POST("basicdata/dictParam/getDictParamList")
    Observable<BaseResponseBean<DictParamResult>> getDictParamList(@Body DictParam dictParam);
}
