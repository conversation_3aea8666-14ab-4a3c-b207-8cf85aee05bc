package com.xyy.wms.pda.bean.instorage

import java.io.Serializable

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/14
 */
data class PackageBarCodeBean (
  var orgCode : String,                           // 机构编码
  var orgName : String,                           // 机构名称
  var productName : String,                       // 商品名称
  var commonName : String,                        // 通用名
  var prescriptionClassification : String,        // 处方分类
  var marketAuthor : String,                      // 上市许可持有人
  var middlePackingNumber : String,               // 中包装规格
  var packingUnit : String,                       // 包装单位
  var specifications : String,                    // 规格
  var registrationCertificate : String,           // 进口注册号
  var productCode : String,                       // 商品编号
  var manufacturer : String,                      // 厂家名称
  var largePackingNumber : String,                // 件包装规格
  var producingArea : String,                     // 产地
  var approvalNumbers : String,                   // 批准文号
  var sterilizingBatchNumber : String,            // 灭菌批号
  var ephedrineFlag: Boolean,                     //是否含麻
  var smallPackageBarCode: String,                //69码
  var purchaseProductBatchCodeVoList : List<PurchaseProductBatchCodeVoBean>
) : Serializable
