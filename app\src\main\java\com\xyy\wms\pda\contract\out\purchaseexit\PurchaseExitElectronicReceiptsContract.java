package com.xyy.wms.pda.contract.out.purchaseexit;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitElectronicReceiptsBean;

import java.util.List;

import io.reactivex.Observable;

/**
 * Created by lwj on 2020-02-24.
 * <EMAIL>
 * 电子监管码扫描单
 */
public interface PurchaseExitElectronicReceiptsContract {


    interface IPurchaseExitElectronicReceiptsModel extends IBaseModel {

        /**
         * 电子监管码扫描单
         *
         * @param pickUpOrder 单据编号
         */
        Observable<BaseResponseBean<List<PurchaseExitElectronicReceiptsBean>>> queryElectronicReceipts(String lineNumber, String pickUpOrder, String productBatchCode, String productName);
    }


    interface IPurchaseExitElectronicReceiptsView extends IBaseActivity {

        void queryElectronicReceiptsSuccess(BaseResponseBean<List<PurchaseExitElectronicReceiptsBean>> bean, String lineNumber, String productBatchCode, String productName);

    }

    abstract class IPurchaseExitElectronicReceiptsPresenter extends BasePresenter<IPurchaseExitElectronicReceiptsModel, IPurchaseExitElectronicReceiptsView> {

        /**
         * 电子监管码扫描单
         *
         * @param pickUpOrder 单据编号
         */
        public abstract void queryElectronicReceipts(String lineNumber, String pickUpOrder, String productBatchCode, String productName);

    }


}
