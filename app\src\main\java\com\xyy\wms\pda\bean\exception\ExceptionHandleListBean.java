package com.xyy.wms.pda.bean.exception;

import java.io.Serializable;
import java.util.List;

/**
 * 异常处理问题处理列表bean
 */
public class ExceptionHandleListBean implements Serializable {

    private boolean hasNextPage;
    private boolean hasPreviousPage;
    private boolean isFirstPage;
    private boolean isLastPage;
    private int total;
    private String exceptionTaskCode;

    public String getExceptionTaskCode() {
        return exceptionTaskCode;
    }

    public void setExceptionTaskCode(String exceptionTaskCode) {
        this.exceptionTaskCode = exceptionTaskCode;
    }

    private List<ExceptionHandleBean> list;

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public boolean isHasNextPage() {
        return hasNextPage;
    }

    public void setHasNextPage(boolean hasNextPage) {
        this.hasNextPage = hasNextPage;
    }

    public boolean isHasPreviousPage() {
        return hasPreviousPage;
    }

    public void setHasPreviousPage(boolean hasPreviousPage) {
        this.hasPreviousPage = hasPreviousPage;
    }

    public boolean isFirstPage() {
        return isFirstPage;
    }

    public void setFirstPage(boolean firstPage) {
        isFirstPage = firstPage;
    }

    public boolean isLastPage() {
        return isLastPage;
    }

    public void setLastPage(boolean lastPage) {
        isLastPage = lastPage;
    }

    public List<ExceptionHandleBean> getList() {
        return list;
    }

    public void setList(List<ExceptionHandleBean> list) {
        this.list = list;
    }
}
