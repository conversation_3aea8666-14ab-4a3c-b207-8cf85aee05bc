package com.xyy.wms.pda.contract.movestorage.wholeshelf;

import android.media.browse.MediaBrowser;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.common.CheckContainerCodeBean;
import com.xyy.wms.pda.bean.common.CheckContainerCodeResult;
import com.xyy.wms.pda.bean.common.LogicalRegionResult;

import com.xyy.wms.pda.bean.common.RunningTaskResult;
import com.xyy.wms.pda.bean.common.SourcePositionCodeBean;
import com.xyy.wms.pda.bean.moveStorage.StartTaskBean;
import com.xyy.wms.pda.bean.moveStorage.StartTaskResult;
import com.xyy.wms.pda.ui.activity.movestorage.wholeshelfdown.MoveStorageShelfDownActivity;

import org.jetbrains.annotations.NotNull;

import java.util.List;

import io.reactivex.Observable;
/**
 * 通过原货位获取逻辑区
 */
public interface WholeShelfDownContract {
    //通过model获取数据
    interface WholeShelfDownModel extends IBaseModel {
        /**
         * 通过原货位获取逻辑区
         */
        Observable<BaseResponseBean<LogicalRegionResult>> getLogicalRegion(SourcePositionCodeBean goodsAllocation);
        /**
         * 校验托盘位
         */
        Observable<BaseResponseBean<Boolean>> checkContainerCode(CheckContainerCodeBean containerCode);
        /**
         * 开启任务
         */
        Observable<BaseResponseBean<StartTaskResult>> startTask(StartTaskBean startTaskBean);
        /**
         * 进行中的任务
         */
        Observable<BaseResponseBean<RunningTaskResult>> getRunningTask();
    }

    //success监听的方法
    interface IIWholeShelfDownView extends IBaseActivity {
        void getLogicalRegionViewSuccess(BaseResponseBean<LogicalRegionResult> requestBaseBean);

        void checkContainerCodeSuccess(BaseResponseBean<Boolean> requestBaseBean);

        void startTaskViewSuccess(BaseResponseBean<StartTaskResult> requestBaseBean);
        void getRunningTaskViewSuccess(BaseResponseBean<RunningTaskResult> requestBaseBean);
    }
    //被子类重写的方法
    abstract class WholeShelfDownPresenter extends BasePresenter<WholeShelfDownModel, MoveStorageShelfDownActivity> {
        public abstract void checkContainerCode(CheckContainerCodeBean containerCode);

        public abstract void getLogicalRegion(SourcePositionCodeBean goodsAllocation);

        //开启任务
        public abstract void startTask(StartTaskBean startTaskBean);
        /**
         * 进行中的任务
         */
        public abstract void getRunningTask();
    }
}
