package com.xyy.wms.pda.bean.inmanager.check

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

/**
 * 盘查 根据货位编码返回数据
 */
@Parcelize
data class CheckUpBean(
  val goodsPositionCode: String? = null,      // 显示货位
  val goodsAllocation: String?= null,         // 显示货位-新
  val batchNumber: String? = null,            // 批号
  val sterilizingBatchNumber: String? = null, // 灭菌批号
  val commonName: String? = null,             // 通用名称
  val manufacturer: String? = null,           // 厂家名称
  val packageNum: String? = null,             // 包装规格
  val produceTime: String? = null,            // 生产日期
  val productionDate: String? = null,         // 生产日期-新
  val productName: String? = null,            // 商品名称
  val specifications: String? = null,         // 规格
  val validDate: String? = null,              // 有效期至
  val validityDate: String? = null,           // 有效期至-新
  val wholeNum: String? = null,               // 库存数量（货位数量）
  val occupyAmount: String? = null,           // 出库预占
  val packingUnit: String? = null,            // 单位
  val lockedQuantity: String? = null,         // 锁定数量
  val ownerName: String? = null,              // 业主名称
  val warehouseWhole: Long? = 0,         // 件数
  val warehouseScattered: Long? = 0,     // 零散数
  val smallPackageBarCode: String? = null,    // 小包装条码
  val amountWait: String? = null,             // 入库预占
  val channelName: String? = null,            // 业务类型
  val storageTypeCode: String? = null         // 库别编号
) : Parcelable
