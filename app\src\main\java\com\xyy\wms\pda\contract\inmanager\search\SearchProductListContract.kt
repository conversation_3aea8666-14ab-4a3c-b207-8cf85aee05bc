package com.xyy.wms.pda.contract.inmanager.search

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.inmanager.search.SearchProductBean
import io.reactivex.Observable

/**
 * Created by XyyMvpPdaTemplate on 04/19/2019 11:21
 */
interface SearchProductListContract {
  interface ISearchProductListModel : IBaseModel {
    fun queryProductListByBarCode(barCode: String?): Observable<BaseResponseBean<List<SearchProductBean>>>
  }

  interface ISearchProductListView : IBaseActivity {
    fun queryProductListByBarCodeSuccess(productList: List<SearchProductBean>, enter: Boolean)
  }
}
