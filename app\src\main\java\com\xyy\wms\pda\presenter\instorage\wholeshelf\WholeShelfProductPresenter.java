package com.xyy.wms.pda.presenter.instorage.wholeshelf;

import androidx.annotation.Nullable;

import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.common.DictParam;
import com.xyy.wms.pda.bean.common.DictParamResult;
import com.xyy.wms.pda.bean.instorage.BatchCodeResultBean;
import com.xyy.wms.pda.bean.instorage.shelf.CheckSelectGoodsPositionBean;
import com.xyy.wms.pda.bean.instorage.shelf.CommitShelfResult;
import com.xyy.wms.pda.bean.instorage.shelf.ShelfDetailPost;
import com.xyy.wms.pda.contract.instorage.wholeshelf.WholeShelfProductContract;
import com.xyy.wms.pda.helper.SimpleErrorConsumer;
import com.xyy.wms.pda.helper.SimpleSuccessConsumer;
import com.xyy.wms.pda.model.instorage.wholeshelf.WholeShelfProductModel;
import com.xyy.wms.pda.presenter.checkSelectGoodsPosition.instorage.CheckSelectGoodsPositionPresenter;
import com.xyy.wms.pda.bean.req.CheckGoodsPositionReq;

import java.util.List;

/**
 * 整件商品上架
 */
public class WholeShelfProductPresenter extends WholeShelfProductContract.WholeShelfProductPresenter implements
        CheckSelectGoodsPositionPresenter {

    public static WholeShelfProductPresenter newInstance() {
        return new WholeShelfProductPresenter();
    }

    @Override
    protected WholeShelfProductModel getModel() {
        return WholeShelfProductModel.newInstance();
    }

    @Override
    public void checkSelectGoodsPosition(CheckGoodsPositionReq req) {
        mRxManager.register(mIModel.checkSelectGoodsPosition(req).subscribe(new SimpleSuccessConsumer<BaseResponseBean<CheckSelectGoodsPositionBean>>(mIView) {
            @Override
            public void onSuccess(BaseResponseBean<CheckSelectGoodsPositionBean> baseResponseBean) {
                mIView.checkSelectGoodsPositionSuccess(baseResponseBean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }

    /**
     * 单个明细提交
     */
    public void commitStorageOrderDetail(ShelfDetailPost shelfDetailPost) {
        mRxManager.register(mIModel.commitStorageOrderDetail(shelfDetailPost).subscribe(new SimpleSuccessConsumer<BaseResponseBean<CommitShelfResult>>(mIView) {

            @Override
            public void onSuccess(BaseResponseBean<CommitShelfResult> baseResponseBean) {
                mIView.commitStorageOrderDetailSuccess(baseResponseBean);
            }
        }, new SimpleErrorConsumer(mIView)));

    }
    /**
     * 获取商品批号
     */
    public void getBatchCodes(String productCode){
        mRxManager.register(mIModel.getBatchCodes(productCode).subscribe(new SimpleSuccessConsumer<BaseResponseBean<List<BatchCodeResultBean>>>(mIView) {
            @Override
            public void onSuccess(@Nullable BaseResponseBean<List<BatchCodeResultBean>> baseResponseBean) {
                mIView.getBatchCodesSuccess(baseResponseBean.getResult());
            }
        }, new SimpleErrorConsumer(mIView)));
    }

    /**
     * 批号开关
     */
    public void getDictParamList(DictParam dictParam){
        mRxManager.register(mIModel.getDictParamList(dictParam).subscribe(new SimpleSuccessConsumer<BaseResponseBean<DictParamResult>>(mIView) {
            @Override
            public void onSuccess(@Nullable BaseResponseBean<DictParamResult> baseResponseBean) {
                mIView.getDictParamListSuccess(baseResponseBean.getResult());
            }
        }, new SimpleErrorConsumer(mIView)));
    }
}
