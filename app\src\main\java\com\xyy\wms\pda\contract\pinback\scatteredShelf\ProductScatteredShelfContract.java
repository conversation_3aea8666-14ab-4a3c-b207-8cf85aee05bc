package com.xyy.wms.pda.contract.pinback.scatteredShelf;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.pinback.PostShelfListBean;
import com.xyy.wms.pda.bean.pinback.ShelfResult;
import com.xyy.wms.pda.model.pinback.scatteredShelf.ProductScatteredShelfModel;

import io.reactivex.Observable;

/**
 * 商品零散上架
 */
public interface ProductScatteredShelfContract {

    interface IProductScatteredShelfModel extends IBaseModel {

        Observable<BaseResponseBean<ShelfResult>> commitScatteredShelfList(PostShelfListBean wholeShelfListBean);
    }

    interface IProductScatteredShelfView extends IBaseActivity {
        void checkSelectGoodsPositionSuccess(BaseResponseBean baseResponseBean);

        void commitScatteredShelfListSuccess(BaseResponseBean<ShelfResult> listBaseResponseBean);

        void commitScatteredShelfListFail(BaseResponseBean<ShelfResult> baseResponseBean);
    }

    abstract class ProductScatteredShelfPresenter extends BasePresenter<ProductScatteredShelfModel, IProductScatteredShelfView> {

        public abstract void commitScatteredShelfList(PostShelfListBean wholeShelfListBean);
    }

}
