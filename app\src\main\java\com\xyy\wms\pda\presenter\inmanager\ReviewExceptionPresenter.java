package com.xyy.wms.pda.presenter.inmanager;


import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.inmanager.ExceptionCountBean;
import com.xyy.wms.pda.contract.inmanager.ReviewExceptionContract;
import com.xyy.wms.pda.helper.SimpleErrorConsumer;
import com.xyy.wms.pda.helper.SimpleSuccessConsumer;
import com.xyy.wms.pda.model.inmanager.ReviewExceptionModel;

import java.util.List;

public class ReviewExceptionPresenter extends ReviewExceptionContract.ReviewExceptionPresenter {

    public static ReviewExceptionPresenter newInstance() {

        return new ReviewExceptionPresenter();

    }


    @Override
    protected ReviewExceptionContract.IReviewExceptionModel getModel() {
        return ReviewExceptionModel.newInstance();
    }

    @Override
    public void exceptionCount(String exceptionType) {
        if (mIModel == null || mIView == null) return;
        mRxManager.register(mIModel.exceptionCount(exceptionType).subscribe(new SimpleSuccessConsumer<BaseResponseBean<List<ExceptionCountBean>>>(mIView) {
            @Override
            public void onSuccess(BaseResponseBean<List<ExceptionCountBean>> listBaseResponseBean) {
                if (mIView == null) return;
                mIView.exceptionCountSuccess(listBaseResponseBean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }
}
