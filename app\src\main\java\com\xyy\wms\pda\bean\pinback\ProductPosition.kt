package com.xyy.wms.pda.bean.pinback
import java.io.Serializable
/**
 * 货位校验bean
 */
class ProductPosition : Serializable {
    var batchNum: String? = null    //批号	string	批号
    var checkAssess: Int = 0    //验收评定	number	1--合格，2--不合格，3--待处理
    var orgCode: String? = null    //组织机构	string	组织机构
    var productId: String? = null    //商品编号	string	商品编号
    var realPosition: String? = null//实际货位	string	实际货位
    var storageClassification: Int = 0    //存储类别	number	存储类别
    var warehouseTypeName: String? = null    //库别名称
    var realPiecesNum: Int = 0    //整件数量
    var realScatteredNum: Int = 0    //零散数量
    var packageNum: Int = 0//件包装数量
}
