package com.xyy.wms.pda.contract.out.purchaseexit;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.checkaccept.SecondLoginPost;
import com.xyy.wms.pda.bean.instorage.checkaccept.SecondLoginResult;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitElectronicReceiptsBean;

import io.reactivex.Observable;

/**
 * Created by lwj on 2020-02-24.
 * <EMAIL>
 * 购进退出复核单-商品复核
 */
public interface PurchaseExitElectronicReViewGoodsContract {


    interface IPurchaseExitElectronicReViewGoodsModel extends IBaseModel {

        Observable<BaseResponseBean<PurchaseExitElectronicReceiptsBean>> updateReviewDetailStatus(
                String lineNumber,
                String pickUpOrder);

        Observable<BaseResponseBean<SecondLoginResult>> secondLogin(
                SecondLoginPost secondLoginPost);
    }


    interface IPurchaseExitElectronicReViewGoodsView extends IBaseActivity {

        void updateReviewDetailStatusSuccess(BaseResponseBean<PurchaseExitElectronicReceiptsBean> bean);

        void secondLoginSuccess(BaseResponseBean<SecondLoginResult> bean);

    }

    abstract class IPurchaseExitElectronicReViewGoodsPresenter extends BasePresenter<IPurchaseExitElectronicReViewGoodsModel, IPurchaseExitElectronicReViewGoodsView> {

        public abstract void updateReviewDetailStatus(
                String lineNumber,
                String pickUpOrder);

        /**
         * 二次登陆确认
         */
        public abstract void secondLogin(SecondLoginPost secondLoginPost);

    }


}
