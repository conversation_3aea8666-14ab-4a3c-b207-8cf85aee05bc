package com.xyy.wms.pda.model.instorage.checkaccept

import io.reactivex.Observable
import com.xyy.wms.pda.contract.instorage.checkaccept.DetailsOfUnAcceptedGoodsContract
import com.xyy.wms.pda.bean.instorage.checkaccept.WaitCheckDetailListPost
import com.xyy.wms.pda.bean.instorage.checkaccept.WaitCheckDetailListBean
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.net.RetrofitCreateHelper
import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.api.ApiInAcceptServiceNew

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/4/11
 */
class DetailsOfUnAcceptedGoodsModel : DetailsOfUnAcceptedGoodsContract.DetailsOfUnAcceptedGoodsContractModel {
  companion object {
    fun newInstance(): DetailsOfUnAcceptedGoodsModel {
        return DetailsOfUnAcceptedGoodsModel()
      }
    }
    override fun getWaitCheckDetailList(waitCheckDetailListPost: WaitCheckDetailListPost): Observable<BaseResponseBean<List<WaitCheckDetailListBean>>> {
        return RetrofitCreateHelper
            .createApi(ApiInAcceptServiceNew::class.java)
            .getWaitCheckDetailList(waitCheckDetailListPost)
            .compose(RxHelper.rxSchedulerHelper())
    }
}
