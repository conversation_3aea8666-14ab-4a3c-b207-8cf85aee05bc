package com.xyy.wms.pda.model.out.purchaseexit;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitReViewPrivUser;
import com.xyy.wms.pda.contract.out.purchaseexit.PurchaseExitElectronicReViewPrivUserContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.List;

import io.reactivex.Observable;

/**
 * Created by lwj on 2020-03-06.
 * <EMAIL>
 */
public class PurchaseExitElectronicReViewPrivUserModel implements PurchaseExitElectronicReViewPrivUserContract.IPurchaseExitElectronicReViewPrivUserModel {

    public static PurchaseExitElectronicReViewPrivUserModel newInstance() {
        return new PurchaseExitElectronicReViewPrivUserModel();
    }

    @Override
    public Observable<BaseResponseBean<List<PurchaseExitReViewPrivUser>>> selectListSysPrivUser(String userName) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).selectListSysPrivUser(userName)
                .compose(RxHelper.rxSchedulerHelper());
    }
}
