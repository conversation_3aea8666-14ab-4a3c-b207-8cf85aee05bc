# 异常图片上传功能需求文档

## 1. 需求概述

### 1.1 功能描述
在业务系统中集成异常图片上传功能，允许用户在处理异常情况时拍照或选择图片进行上传，支持图片的预览、删除和批量提交。

### 1.2 业务场景
- 商品验收异常记录
- 质量问题图片采集
- 设备故障图片上传
- 其他需要图片证据的异常情况

## 2. 功能需求

### 2.1 核心功能
- **图片拍摄**：支持调用相机拍照
- **图片选择**：支持从相册选择图片
- **图片预览**：支持查看已选择的图片
- **图片删除**：支持删除不需要的图片
- **批量上传**：支持一次性上传多张图片
- **进度显示**：上传过程中显示进度提示

### 2.2 业务规则
- 最多支持5张图片上传
- 支持JPG、PNG格式图片
- 单张图片大小限制：5MB以内
- 自动压缩图片以优化上传速度
- 上传前进行图片质量检查

## 3. 界面设计需求

### 3.1 主界面集成
```
┌─────────────────────────────────┐
│  业务表单内容                    │
│  ┌─────────────────────────────┐ │
│  │     异常图片 (2)            │ │  ← 按钮显示已选图片数量
│  └─────────────────────────────┘ │
│  其他业务字段...                 │
└─────────────────────────────────┘
```

### 3.2 图片上传弹窗
```
┌─────────────────────────────────────────┐
│  返回                    异常图片    提交 │
├─────────────────────────────────────────┤
│  业务单号：XXX-2024-001                 │
│  相关信息：商品编码等                    │
├─────────────────────────────────────────┤
│  异常图片（最多可以传五张）              │
│  ┌───┐ ┌───┐ ┌───┐ ┌───┐ ┌───┐      │
│  │ + │ │img│ │img│ │ + │ │ + │      │
│  └───┘ └───┘ └───┘ └───┘ └───┘      │
│         ×     ×                      │  ← 删除按钮
└─────────────────────────────────────────┘
```

## 4. 技术实现需求

### 4.1 前端组件需求

#### 4.1.1 异常图片按钮组件
```kotlin
// 组件属性
class ExceptionPictureButton : Button {
    var maxCount: Int = 5                    // 最大图片数量
    var currentCount: Int = 0                // 当前图片数量
    var businessId: String = ""              // 业务单据ID
    var onPictureUploadListener: (() -> Unit)? = null  // 上传完成回调
    
    // 更新按钮文本
    fun updateButtonText() {
        text = if (currentCount > 0) "异常图片($currentCount)" else "异常图片"
    }
}
```

#### 4.1.2 图片上传弹窗组件
```kotlin
class ExceptionPictureDialog : Dialog {
    var maxCount: Int = 5
    var businessInfo: BusinessInfo? = null   // 业务信息显示
    var pictureList: MutableList<PictureInfo> = mutableListOf()
    
    // 核心方法
    fun showDialog()                         // 显示弹窗
    fun addPicture(imagePath: String)        // 添加图片
    fun deletePicture(position: Int)         // 删除图片
    fun uploadPictures()                     // 上传图片
}
```

#### 4.1.3 图片展示网格组件
```kotlin
class PictureGridView : RecyclerView {
    var maxCount: Int = 5
    var pictureList: MutableList<PictureInfo> = mutableListOf()
    var onItemClickListener: ((position: Int) -> Unit)? = null
    var onDeleteClickListener: ((position: Int) -> Unit)? = null
    
    // 初始化空白占位符
    fun initEmptyPlaceholders()
    // 更新图片数据
    fun updatePictureData(pictures: List<PictureInfo>)
}
```

### 4.2 数据模型需求

#### 4.2.1 图片信息模型
```kotlin
data class PictureInfo(
    var id: String = "",                     // 图片ID
    var localPath: String = "",              // 本地路径
    var serverUrl: String = "",              // 服务器URL
    var isLocal: Boolean = false,            // 是否本地图片
    var isPlaceholder: Boolean = true,       // 是否占位符
    var uploadStatus: UploadStatus = UploadStatus.NONE,  // 上传状态
    var fileSize: Long = 0,                  // 文件大小
    var createTime: Long = System.currentTimeMillis()   // 创建时间
)

enum class UploadStatus {
    NONE,        // 未上传
    UPLOADING,   // 上传中
    SUCCESS,     // 上传成功
    FAILED       // 上传失败
}
```

#### 4.2.2 业务信息模型
```kotlin
data class BusinessInfo(
    var businessId: String = "",             // 业务单据ID
    var businessType: String = "",           // 业务类型
    var businessCode: String = "",           // 业务编码
    var businessName: String = "",           // 业务名称
    var relatedInfo: Map<String, String> = mapOf()  // 相关信息
)
```

#### 4.2.3 上传请求模型
```kotlin
data class PictureUploadRequest(
    var businessId: String,                  // 业务单据ID
    var businessType: String,                // 业务类型
    var pictures: List<PictureInfo>,         // 图片列表
    var uploadTime: Long = System.currentTimeMillis()
)

data class PictureUploadResponse(
    var success: Boolean,                    // 是否成功
    var message: String,                     // 响应消息
    var pictureUrls: List<String>,           // 上传后的图片URL列表
    var failedIndexes: List<Int> = listOf()  // 失败的图片索引
)
```

### 4.3 API接口需求

#### 4.3.1 图片上传接口
```kotlin
interface PictureUploadApi {
    /**
     * 上传异常图片
     * @param request 上传请求
     * @return 上传结果
     */
    @POST("api/exception/pictures/upload")
    @Multipart
    suspend fun uploadExceptionPictures(
        @Part("businessId") businessId: RequestBody,
        @Part("businessType") businessType: RequestBody,
        @Part pictures: List<MultipartBody.Part>
    ): ApiResponse<PictureUploadResponse>
    
    /**
     * 删除异常图片
     * @param businessId 业务ID
     * @param pictureUrls 图片URL列表
     */
    @POST("api/exception/pictures/delete")
    suspend fun deleteExceptionPictures(
        @Body request: PictureDeleteRequest
    ): ApiResponse<Boolean>
    
    /**
     * 查询异常图片
     * @param businessId 业务ID
     */
    @GET("api/exception/pictures/{businessId}")
    suspend fun getExceptionPictures(
        @Path("businessId") businessId: String
    ): ApiResponse<List<String>>
}
```

### 4.4 权限需求
```xml
<!-- 相机权限 -->
<uses-permission android:name="android.permission.CAMERA" />
<!-- 读取外部存储权限 -->
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<!-- 写入外部存储权限 -->
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<!-- 网络权限 -->
<uses-permission android:name="android.permission.INTERNET" />
```

## 5. 用户交互流程

### 5.1 图片上传流程
```
用户点击异常图片按钮
    ↓
检查是否有业务数据
    ↓
显示图片上传弹窗
    ↓
用户点击添加图片
    ↓
选择拍照或相册
    ↓
权限检查
    ↓
获取图片并压缩
    ↓
显示在网格中
    ↓
用户点击提交
    ↓
验证图片数量
    ↓
批量上传到服务器
    ↓
显示上传结果
    ↓
关闭弹窗并更新按钮状态
```

### 5.2 图片删除流程
```
用户点击图片删除按钮
    ↓
确认删除操作
    ↓
从列表中移除图片
    ↓
更新网格显示
    ↓
更新按钮计数
```


## 7. 配置参数

### 7.1 可配置项
```kotlin
object ExceptionPictureConfig {
    const val MAX_PICTURE_COUNT = 5          // 最大图片数量
    const val MAX_FILE_SIZE = 5 * 1024 * 1024  // 最大文件大小 5MB
    const val COMPRESS_QUALITY = 80          // 压缩质量
    const val UPLOAD_TIMEOUT = 30000         // 上传超时时间
    const val RETRY_COUNT = 3                // 重试次数
    
    val SUPPORTED_FORMATS = listOf("jpg", "jpeg", "png")  // 支持格式
}
```

## 8. 错误处理

### 8.1 常见错误场景
- 权限被拒绝
- 网络连接失败
- 文件大小超限
- 格式不支持
- 服务器错误
- 存储空间不足

### 8.2 错误处理策略
```kotlin
sealed class PictureError {
    object PermissionDenied : PictureError()
    object NetworkError : PictureError()
    object FileSizeExceeded : PictureError()
    object UnsupportedFormat : PictureError()
    object ServerError : PictureError()
    object StorageInsufficient : PictureError()
    data class Unknown(val message: String) : PictureError()
}
```
根据我的需求，把异常图片上传开发出来
网络请求，异常处理等基础搭建的功能不需要开发。凡是现有系统已经有的都无需开发
API 接口统一放入 ApiInAcceptServiceNew文件里面
数据类放入到D:\webproject\baicao\pda\feature-push-store-check-20250829\app\src\main\java\com\xyy\wms\pda\bean\instorage 里面
使用已有的类
CheckBillActivityNew
CheckAcceptBilPresenterNew
CheckAcceptBillContractNew
CheckAcceptBillModelNew