package com.xyy.wms.pda.bean.purchaseexit

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

/**
 * 购进退出商品复核
 */
@Parcelize
data class PurchaseExitReViewGoods(
    var actualRefundCount: String? = null, //   数量 需扫描数量
    var actualScatteredCount: String? = null,
    var actualWholeCount: String? = null,
    var approvalNumbers: String? = null,
    var beginTime: String? = null,
    var buildingCode: String? = null,
    var buildingName: String? = null,
    var channelCode: String? = null,
    var channelName: String? = null,
    var codeLevel: String? = null,
    var commonName: String? = null,
    var containerCode: String? = null,
    var createTime: String? = null,
    var createUser: String? = null,
    var deliveryMethod: String? = null,
    var djSort: String? = null,
    var dosageForm: String? = null,
    var endTime: String? = null,
    var erpRefundOrderCode: String? = null,
    var fileds: String? = null,
    var headers: String? = null,
    var id: String? = null,
    var largeCategory: String? = null, //       string    @mock =$order('内服','内服','内服','内服','内服')
    var largePackingNumber: String? = null,  //  件包装规格    string    @mock =$order('1','1','1','2','2')
    var lineNumber: String? = null,  //    number    @mock =$order(1,2,3,4,5)
    var manufacturer: String? = null,  //  厂家名称
    var marketAuthor: String? = null, //   上市许可持有人
    var mediumPackageBarCode: String? = null,
    var middlePackingNumber: String? = null,//   中包装规格
    var orderType: String? = null,
    var orgCode: String? = null,
    var ownerCode: String? = null,
    var packingUnit: String? = null, // 规 格
    var pickUpOrder: String? = null,
    var piecePackageBarCode: String? = null,
    var producingArea: String? = null,
    var productBatchCode: String? = null, //  批号
    var sterilizingBatchNumber: String? = null,//  灭菌批号
    var productCode: String? = null, // 商品编码
    var productCodeList: String? = null,
    var productContainTaxPrice: String? = null,
    var productName: String? = null, // 商品名称
    var productProduceDate: String? = null, //  生产日期
    var productType: Int? = 0,
    var productValidDate: String? = null,//   有效期
    var purchaseUser: String? = null,
    var recheckName: String? = null,
    var recheckTime: String? = null,
    var recheckUser: String? = null,
    var recheckUserName: String? = null,
    var refundAlwaysCount: String? = null,
    var refundCount: String? = null,
    var refundCountPlan: String? = null,
    var refundOrderCode: String? = null,
    var refundOrderStatus: String? = null,
    var refundOrderStatusDesc: String? = null,// 	状态 4未复核 6已复核
    var refundScatteredCount: String? = null,
    var refundType: String? = null,
    var refundTypeDesc: String? = null,
    var refundWholeCount: String? = null,
    var regulatoryCode: String? = null,
    var scannedNumber: String? = null,//   已扫描条目数/已扫描次数
    var scannedNumberLarge: String? = null,//   已扫描件包装数
    var scannedNumberMiddle: String? = null, // 已扫描中包装数
    var scannedNumberSmall: String? = null,//  已扫描小包装数
    var scannedNumberTotal: String? = null,//  已扫描总数量
    var secondRecheckUser: String? = null,
    var shelfGoodsAmount: String? = null,
    var shelfLocationCode: String? = null,
    var shelfName: String? = null,
    var shelfTime: String? = null,
    var shelfUser: String? = null,
    var shelfUserName: String? = null, // 拣货人
    var smallPackageBarCode: String? = null,
    var specifications: String? = null,
    var storageAreaCode: String? = null,
    var storageRoomCode: String? = null,
    var storageRoomName: String? = null,
    var storageType: String? = null,
    var storageTypeCode: String? = null,
    var storageTypeName: String? = null,
    var storeCode: String? = null,
    var storeName: String? = null,
    var supplierCode: String? = null,
    var supplierCodeList: String? = null,
    var supplierName: String? = null,
    var updateName: String? = null,
    var updateTime: String? = null,
    var updateUser: String? = null,
    var warehouseCode: String? = null,
    var whetherRegulatory: String? = null //  是否为追溯码 1是 0 否
) : Parcelable {

  /**
   * 是不是特管商品
   */
  fun isSpecialProduct() : Boolean {
    return productType == 5 || productType == 6
  }
}