package com.xyy.wms.pda.presenter.inmanager.check

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.inmanager.check.CheckUpBean
import com.xyy.wms.pda.contract.inmanager.check.CheckUpContract.ICheckUpModel
import com.xyy.wms.pda.contract.inmanager.check.CheckUpContract.ICheckUpView
import com.xyy.wms.pda.helper.SimpleErrorConsumer
import com.xyy.wms.pda.helper.SimpleSuccessConsumer
import com.xyy.wms.pda.model.inmanager.check.CheckUpModel

/**
 * Created by zcj on 2018/11/8 16
 */
class CheckUpPresenter : BasePresenter<ICheckUpModel?, ICheckUpView?>() {

  override fun getModel(): ICheckUpModel {
    return CheckUpModel.newInstance()
  }

  fun checkUp(positionCode: Map<String, String?>, isEnter: Boolean) {
    if (mIModel == null || mIView == null) return
    mRxManager.register(mIModel!!.checkUp(positionCode).subscribe(
      object : SimpleSuccessConsumer<BaseResponseBean<List<CheckUpBean>>>(mIView) {
        override fun onSuccess(bean: BaseResponseBean<List<CheckUpBean>>) {
          if (mIView == null) return
          mIView!!.checkUpSuccess(bean.result, isEnter)
        }
      }, SimpleErrorConsumer(mIView)))
  }

  companion object {
    fun newInstance(): CheckUpPresenter {
      return CheckUpPresenter()
    }
  }
}
