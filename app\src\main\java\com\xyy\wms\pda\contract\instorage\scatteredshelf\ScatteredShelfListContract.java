package com.xyy.wms.pda.contract.instorage.scatteredshelf;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.shelf.InShelfResult;
import com.xyy.wms.pda.model.instorage.scatteredshelf.ScatteredShelfListModel;
import com.xyy.wms.pda.ui.activity.instorage.scatteredshelf.InScatteredShelfListActivity;

import io.reactivex.Observable;
/**
 * 零散上架列表
 */
public interface ScatteredShelfListContract {
    interface IScatteredShelfListModel extends IBaseModel {
        Observable<BaseResponseBean<InShelfResult>> getScatteredShelfList(String containerCode, int shelfType);
    }
    interface IScatteredShelfListView extends IBaseActivity {
        void getScatteredShelfListSuccess(BaseResponseBean<InShelfResult> requestBaseBean);
    }
    abstract class ScatteredShelfListPresenter extends BasePresenter<ScatteredShelfListModel, InScatteredShelfListActivity> {
        public abstract void getScatteredShelfList(String containerCode, int shelfType);
    }
}
