package com.xyy.wms.pda.contract.movestorage.car;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.common.CancelTaskBean;
import com.xyy.wms.pda.bean.common.MoveStorageFinishedBean;
import com.xyy.wms.pda.bean.common.RunningTaskResult;
import com.xyy.wms.pda.bean.moveStorage.AddTaskCarDownDetailBean;
import com.xyy.wms.pda.ui.activity.movestorage.car.CarUpListActivity;

import io.reactivex.Observable;

/**
 * 装车-列表
 */
public interface CarUpListContract {
    interface CarUpListModel extends IBaseModel {
        /**
         * 进行中的任务
         */
        Observable<BaseResponseBean<RunningTaskResult>> getRunningTask();
        /**
         * 完成
         */
        Observable<BaseResponseBean<Boolean>> setFinished(MoveStorageFinishedBean finishedBean);
        /**
         * 添加任务明细
         */
        Observable<BaseResponseBean<Boolean>> addTaskDetail(AddTaskCarDownDetailBean taskDetailBean);
        /**
         * 解除任务
         */
        Observable<BaseResponseBean<Boolean>> cancelTask(CancelTaskBean cancelTaskBean);
    }

    interface CarUpListView extends IBaseActivity {
        /**
         * 进行中任务
         */
        void getRunningTaskViewSuccess(BaseResponseBean<RunningTaskResult> requestBaseBean);

        /**
         * 添加任务明细
         */
        void addTaskDetailViewSuccess(BaseResponseBean<Boolean> requestBaseBean);

        /**
         * 完成
         */
        void setFinishedViewSuccess(BaseResponseBean<Boolean> requestBaseBean);

        /**
         * 移除任务
         */
        void cancelTaskViewSuccess(BaseResponseBean<Boolean> requestBaseBean);
    }
    abstract class CarUpListPresenter extends BasePresenter<CarUpListModel, CarUpListActivity> {
        /**
         * 进行中的任务
         */
        public abstract void getRunningTask();
        /**
         * 添加任务明细
         */
        public abstract void addTaskDetail(AddTaskCarDownDetailBean taskDetailBean);
        /**
         * 完成
         */
        public abstract void setFinished(MoveStorageFinishedBean finishedBean);
        /**
         * 移除任务
         */
        public abstract void cancelTask(CancelTaskBean cancelTaskBean);
    }
}
