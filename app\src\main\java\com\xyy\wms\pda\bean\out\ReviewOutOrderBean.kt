package com.xyy.wms.pda.bean.out

import android.os.Parcelable
import com.xyy.wms.pda.bean.out.outsideReview.TemporaryArea
import kotlinx.android.parcel.Parcelize

import java.io.Serializable

/**
 * 出库——外复核  获取 订单数据
 */
@Parcelize
data class ReviewOutOrderBean (
    var orderCode: String? = null,// 出库单号
    var erpOrderCode: String? = null,// 销售单号
    var isCancel: String? = null,// 0-否,998-内部取消(wms取消) 999-外部取消(erp取消)
    var clientName: String? = null,// 客户名称
    var shippingAddr: String? = null,// 地址
    var modeOfDistribution: String? = null,// 配送方式
    var distributeArea: String? = null,// 配送区域
    var workingAreaBegin: String? = null,// 暂存区起始号
    var workingAreaEnd: String? = null,// 暂存区终止号
    var reviewsNumber: String? = null,// 已复核箱数
    var totalNumber: String? = null,// 总箱数
    var wholeNum: String? = null,// 整箱数量
    var retailNum: String? = null,// 拼箱数量
    var dictName: String? = null,// 配送路线
    var buildingCode: String? = null,// 当前建筑物
    var taskOutReviewCode: String? = null,// 外复核任务单
    var workingAreaList: List<TemporaryArea>? = null// 订单下所有暂存区
) : Parcelable
