package com.xyy.wms.pda.contract.inmanager.check;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.inmanager.check.AddCheckBean;
import com.xyy.wms.pda.bean.inmanager.check.CheckStorageAreaBean;

import java.util.List;

import io.reactivex.Observable;

public interface AddCheckContract {
    abstract class AddCheckPresenter extends BasePresenter<IAddCheckModel, IAddCheckView> {
        /**
         * 上传盘点计划
         */
        public abstract void addWarehouseCheck(AddCheckBean bean);

        /**
         * 获取盘点库别列表
         */
        public abstract void findStorageType();

        /**
         * 获取盘点库区列表
         */
        public abstract void findStorageArea(String storageTypeId);

        /**
         * 获取盘点单号
         */
        public abstract void getCheckNo();
    }

    interface IAddCheckModel extends IBaseModel {
        /**
         * 上传盘点计划
         */
        Observable<BaseResponseBean> addWarehouseCheck(AddCheckBean bean);

        Observable<BaseResponseBean<List<CheckStorageAreaBean>>> findStorageType();

        Observable<BaseResponseBean<List<CheckStorageAreaBean>>> findStorageArea(String storageTypeId);

        Observable<BaseResponseBean<String>> getCheckNo();
    }

    interface IAddCheckView extends IBaseActivity {
        /**
         * 上传成功
         */
        void addWarehouseCheckSuccess(BaseResponseBean bean);

        /**
         * 获取盘点单号成功
         */
        void getCheckNoSuccess(BaseResponseBean<String> bean);

        /**
         * 获取盘点库别成功
         */
        void findStorageTypeSuccess(BaseResponseBean<List<CheckStorageAreaBean>> bean);

        /**
         * 获取盘点库区成功
         */
        void findStorageAreaSuccess(BaseResponseBean<List<CheckStorageAreaBean>> beab);

        /**
         * 显示网络错误
         */
        void showNetworkError();

    }
}