package com.xyy.wms.pda.presenter.inmanager.check;


import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.inmanager.check.CheckTaskListBean;
import com.xyy.wms.pda.contract.inmanager.check.CheckTaskListContract;
import com.xyy.wms.pda.helper.SimpleErrorConsumer;
import com.xyy.wms.pda.helper.SimpleSuccessConsumer;
import com.xyy.wms.pda.model.inmanager.check.CheckListModel;

public class CheckTaskListPresenter extends CheckTaskListContract.CheckTaskListPresenter {
    public static CheckTaskListPresenter newInstance() {
        return new CheckTaskListPresenter();
    }


    @Override
    protected CheckTaskListContract.ICheckTaskListModel getModel() {
        return CheckListModel.newInstance();
    }


    @Override
    public void getCheckTask(String checkPlanType) {
        if (mIModel == null || mIView == null) {
            return;
        }
        mRxManager.register(mIModel.getCheckTask(checkPlanType).subscribe(new SimpleSuccessConsumer<BaseResponseBean<CheckTaskListBean>>(mIView) {
            @Override
            public void onSuccess(BaseResponseBean<CheckTaskListBean> bean) {
                if (mIView == null)
                    return;
                mIView.getCheckTaskSuccess(bean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }

    @Override
    public void taskComplete(String checkTaskType, String checkTaskNo) {
        if (mIModel == null || mIView == null) {
            return;
        }
        mRxManager.register(mIModel.taskComplete(checkTaskType, checkTaskNo).subscribe(new SimpleSuccessConsumer<BaseResponseBean>(mIView) {
            @Override
            public void onSuccess(BaseResponseBean bean) {
                if (mIView == null)
                    return;
                mIView.taskCompleteSuccess(bean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }

}
