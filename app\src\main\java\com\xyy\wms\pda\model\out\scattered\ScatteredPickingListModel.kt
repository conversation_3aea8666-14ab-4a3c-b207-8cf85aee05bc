package com.xyy.wms.pda.model.out.scattered

import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.out.PickingTaskListConfirmBean
import com.xyy.wms.pda.bean.out.ScatteredPickingListBean
import com.xyy.wms.pda.bean.out.SubmitResult
import com.xyy.wms.pda.bean.out.pick.ReviewStageListBean
import com.xyy.wms.pda.contract.out.scattered.ScatteredPickingListContract.IScatteredPickingListModel
import com.xyy.wms.pda.model.ServiceModel
import io.reactivex.Observable

/**
 * 出库——拆零拣货
 */
class ScatteredPickingListModel : ServiceModel(), IScatteredPickingListModel {

  override fun getScatteredPickingList(map: Map<String, String?>): Observable<BaseResponseBean<List<ScatteredPickingListBean>>> {
    return apiOutManagerService.getScatteredPickingList(map).compose(RxHelper.rxSchedulerHelper())
  }

  override fun submitPickingList(bean: PickingTaskListConfirmBean?): Observable<BaseResponseBean<SubmitResult>> {
    return apiOutManagerService.submitScatteredPickingList(bean).compose(RxHelper.rxSchedulerHelper())
  }

  override fun getReviewStageInfo(map: Map<String, String>): Observable<BaseResponseBean<ReviewStageListBean>> {
    return apiOutManagerService.getReviewStageInfo(map).compose(RxHelper.rxSchedulerHelper())
  }

  companion object {
    @JvmStatic
    fun newInstance(): ScatteredPickingListModel {
      return ScatteredPickingListModel()
    }
  }
}
