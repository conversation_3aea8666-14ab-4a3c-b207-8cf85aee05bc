package com.xyy.wms.pda.model.out.purchaseexit;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitBillBean;
import com.xyy.wms.pda.contract.out.purchaseexit.PurchaseExitBillFragmentContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.List;
import java.util.Map;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 01/13/2020 10:50
 */
public class PurchaseExitBillFragmentModel extends BaseModel implements PurchaseExitBillFragmentContract.IPurchaseExitBillModel {

    public static PurchaseExitBillFragmentModel newInstance() {
        return new PurchaseExitBillFragmentModel();
    }

    @Override
    public Observable<BaseResponseBean<List<PurchaseExitBillBean>>> getPurchaseExitBillList(Map<String, Object> map) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).getPurchaseExitBillList(map).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean> queryContainerValidity(Map<String, String> map) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).queryContainerValidity(map).compose(RxHelper.rxSchedulerHelper());
    }
}