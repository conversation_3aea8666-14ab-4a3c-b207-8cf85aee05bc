package com.xyy.wms.pda.contract.movestorage.wholeshelf;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.shelf.InShelfResult;
import com.xyy.wms.pda.bean.moveStorage.TaskBean;
import com.xyy.wms.pda.model.instorage.wholeshelf.WholeShelfListModel;
import com.xyy.wms.pda.ui.activity.movestorage.wholeshelf.MoveStorageWholeShelfListActivity;
import com.xyy.wms.pda.ui.activity.movestorage.wholeshelf.MoveStorageWholeShelfListActivity2;

import java.util.List;

import io.reactivex.Observable;
/**
 * 整件上架列表
 */
public interface WholeShelfListContract {
    interface IWholeShelfListModel extends IBaseModel {
        Observable<BaseResponseBean<InShelfResult>> getWholeShelfList(String containerCode, int containerType);
    }

    interface IIWholeShelfListView extends IBaseActivity {
        void getWholeShelfListSuccess(BaseResponseBean<InShelfResult> requestBaseBean);
    }
    abstract class WholeShelfPresenter extends BasePresenter<WholeShelfListModel, MoveStorageWholeShelfListActivity> {
        public abstract void getWholeShelfList(String containerCode, int containerType);

        public abstract void self(int taskType,String palletNo);
        public abstract void getRunningTask();

        //零货上架巷道预览
        public abstract void getPalletOnPreview(String palletNo);
    }


    abstract class WholeShelfListPresenter extends BasePresenter<WholeShelfListModel, MoveStorageWholeShelfListActivity2> {
        public abstract void getWholeShelfList(String containerCode, int containerType);



        public abstract void getShelfLogic(String palletNo);//转运托盘逻辑区域查询

        public abstract void getPalletGoods(String palletNo);//托盘商品查询

        public abstract void addTaskDetail(Number taskType, Number taskId, String palletNo,String descAllocation, List<TaskBean> items);//添加任务明细
        public abstract void addTaskDetailOfBulk(Number taskType, Number taskId, String palletNo,String sourceAllocation,String descAllocation);//添加任务明细

        public abstract void finish(Number taskId);

        public abstract void getRunningTask();
    }

}
