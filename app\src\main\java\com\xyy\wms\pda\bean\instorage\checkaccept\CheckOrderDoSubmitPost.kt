package com.xyy.wms.pda.bean.instorage.checkaccept

import java.io.Serializable

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/16
 */
data class CheckOrderDoSubmitPost (
    var checkOrderCode : String,         // 验收单号
    var secondCheckUser : String,        // 验收人2的oaID（当需要二次校验时必传）
    var currentRejects : String,         // 是否当场拒收（当验收单明细包含拒收是必填）
    var rejectRemark : String           // 拒收备注
) : Serializable
