package com.xyy.wms.pda.presenter.inmanager.search

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.inmanager.search.SearchGoodPositionBean
import com.xyy.wms.pda.contract.inmanager.search.SearchBatchContract.ISearchBatchModel
import com.xyy.wms.pda.contract.inmanager.search.SearchBatchContract.ISearchBatchView
import com.xyy.wms.pda.helper.SimpleErrorConsumer
import com.xyy.wms.pda.helper.SimpleSuccessConsumer
import com.xyy.wms.pda.model.inmanager.search.SearchBatchModel

/**
 * Created by XyyMvpSportTemplate on 03/26/2019 19:11
 */
class SearchBatchPresenter : BasePresenter<ISearchBatchModel, ISearchBatchView>() {

  override fun getModel(): SearchBatchModel {
    return SearchBatchModel.newInstance()
  }

  /**
   * 根据批号查询信息列表
   */
  fun findGoodsPositionCode(batchNumber: String?, productCode: String?) {
    if (mIModel == null || mIView == null) return
    mRxManager.register(mIModel!!.findGoodsPositionCode(batchNumber, productCode).subscribe(
      object : SimpleSuccessConsumer<BaseResponseBean<List<SearchGoodPositionBean>>>(mIView) {
        override fun onSuccess(baseBean: BaseResponseBean<List<SearchGoodPositionBean>>) {
          if (mIView == null) return
          mIView!!.findGoodsPositionCodeSuccess(baseBean.result)
        }
      }, SimpleErrorConsumer(mIView)))
  }

  companion object {
    fun newInstance(): SearchBatchPresenter {
      return SearchBatchPresenter()
    }
  }
}
