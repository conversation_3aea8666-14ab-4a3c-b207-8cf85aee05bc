package com.xyy.wms.pda.bean.instorage.checkaccept

import java.io.Serializable

data class CheckAcceptBill(
        val buildingCode: String? = null,//	建筑物编码	string	@mock=01
        val buildingName: String? = null,//	建筑物名称	string	@mock=主仓
        val checkOrderCode: String? = null,//验收单号	string	@mock=YSD201912200028
        val checkOrderStatus: String? = null,//验收单状态	string	@mock=1
        val orgCode: String? = null,//	机构编码	string
        val ownerCode: String? = null,//业主编码	string	@mock=009
        val receiveOrderCode: String? = null,//	收货单号	string	@mock=SHD201912200001
        val receiveUser: String? = null,//	收货员id	string	@mock=781
        val receiveUserName: String? = null,//	收货员姓名	string	@mock=张泽宇
        val supplierCode: String? = null,//供应商编码	string	@mock=GPFJIX052001
        val warehouseCode: String? = null,//	仓库编码	string	@mock=WMS360000_1
        val checkUser: String? = null,//	验收员id	string	@mock=WMS360000_1
        var supplierName: String? = null,      //供应商名称
        var containerCodes: String? = null      //验收单容器编号（逗号分隔）
) : Serializable