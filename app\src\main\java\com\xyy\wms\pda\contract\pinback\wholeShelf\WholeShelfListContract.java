package com.xyy.wms.pda.contract.pinback.wholeShelf;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.pinback.ProductBean;
import com.xyy.wms.pda.model.pinback.wholeShelf.WholeShelfListModel;
import com.xyy.wms.pda.ui.activity.pinback.whole.WholeShelfListActivity;

import java.util.List;

import io.reactivex.Observable;
/**
 * 整件上架
 */
public interface WholeShelfListContract {

    interface IWholeShelfItemModel extends IBaseModel {

        Observable<BaseResponseBean<List<ProductBean>>> getWholeShelfList(String containerCode, int type);

    }

    interface IWholeShelfListView extends IBaseActivity {
        void getWholeShelfItemListSuccess(BaseResponseBean<List<ProductBean>> baseResponseBean);

    }

    abstract class WholeShelfListPresenter extends BasePresenter<WholeShelfListModel, WholeShelfListActivity> {

        public abstract void getWholeShelfList(String containerCode,int type);

    }

}
