package com.xyy.wms.pda.contract.out.purchaseexit;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitElectronicCodeBean;

import java.util.List;

import io.reactivex.Observable;

/**
 * Created by lwj on 2020-02-24.
 * <EMAIL>
 * 电子监管码查询
 */
public interface PurchaseExitElectronicCodeContract {


    interface IPurchaseExitElectronicCodeModel extends IBaseModel {
        /**
         * 电子监管码查询
         *
         * @param startDate    起始日期
         * @param endDate      截止日期
         * @param receiptsNum  单据编号
         * @param supplierName 供应商
         */
        Observable<BaseResponseBean<List<PurchaseExitElectronicCodeBean>>> queryReceipts(String startDate, String endDate, String scanStatus, String receiptsNum, String supplierName);

    }


    interface IPurchaseExitElectronicCodeView extends IBaseActivity {

        void queryReceiptsSuccess(BaseResponseBean<List<PurchaseExitElectronicCodeBean>> bean);
    }

    abstract class IPurchaseExitElectronicCodePresenter extends BasePresenter<IPurchaseExitElectronicCodeModel, IPurchaseExitElectronicCodeView> {

        /**
         * 电子监管码查询
         *
         * @param startDate    起始日期
         * @param endDate      截止日期
         * @param receiptsNum  单据编号
         * @param scanStatus   1未扫描2已扫描
         * @param supplierName 供应商
         */
        public abstract void queryReceipts(String startDate, String endDate, String scanStatus, String receiptsNum, String supplierName);

    }


}
