package com.xyy.wms.pda.contract.out.scattered

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.out.ReviewStageBean
import io.reactivex.Observable

/**
 * 拆零拣货 - 复核台
 */
interface ReviewStageContract {
    interface IReviewStageModel : IBaseModel {
        fun recordSettingTime(map: Map<String, String>): Observable<BaseResponseBean<*>>
    }

    interface IReviewStageView : IBaseActivity {
        fun recordSettingTimeSuccess(position: Int, bean: ReviewStageBean)
    }
}
