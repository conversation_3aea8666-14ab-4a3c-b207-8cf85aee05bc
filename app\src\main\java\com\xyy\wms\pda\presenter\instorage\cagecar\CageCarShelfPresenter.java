package com.xyy.wms.pda.presenter.instorage.cagecar;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.cageCar.CageCarShelfResult;
import com.xyy.wms.pda.bean.productCode.ProductCode;
import com.xyy.wms.pda.helper.SimpleErrorConsumer;
import com.xyy.wms.pda.helper.SimpleSuccessConsumer;
import com.xyy.wms.pda.model.instorage.cagecar.CageCarShelfModel;
import com.xyy.wms.pda.presenter.productCode.GetProductCodePresenter;
import com.xyy.wms.pda.ui.activity.instorage.cagecar.CageCarShelfListActivity;

import java.util.List;

/**
 * Created by XyyMvpPdaTemplate on 02/25/2020 12:00
 */
public class CageCarShelfPresenter extends BasePresenter<CageCarShelfModel, CageCarShelfListActivity> implements GetProductCodePresenter {

    public static CageCarShelfPresenter newInstance() {
        return new CageCarShelfPresenter();
    }

    @Override
    protected CageCarShelfModel getModel() {
        return CageCarShelfModel.newInstance();
    }

    public void getRollContainerShelfList(String rollContainerCode) {
        if (mRxManager == null || mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.getRollContainerShelfList(rollContainerCode).subscribe(new SimpleSuccessConsumer<BaseResponseBean<CageCarShelfResult>>(mIView) {
            @Override
            public void onSuccess(BaseResponseBean<CageCarShelfResult> baseResponseBean) {
                mIView.getRollContainerShelfListSuccess(baseResponseBean);
            }
            @Override
            public void onFailure(int code) {
                super.onFailure(code);
                mIView.getRollContainerShelfListFail();
            }
        }, new SimpleErrorConsumer(mIView)));
    }
    @Override
    public void getProductBarCode(String ownerCode, String packageBarCode) {
        if (mRxManager == null || mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.getProductBarCode(ownerCode, packageBarCode).subscribe(new SimpleSuccessConsumer<BaseResponseBean<List<ProductCode>>>(mIView) {
            @Override
            public void onSuccess(BaseResponseBean<List<ProductCode>> baseResponseBean) {
                mIView.getProductCodeSuccess(baseResponseBean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }
}
