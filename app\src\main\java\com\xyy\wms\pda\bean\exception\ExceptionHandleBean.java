package com.xyy.wms.pda.bean.exception;

import java.io.Serializable;

/**
 * 异常问题
 */
public class ExceptionHandleBean implements Serializable {

    private long id;
    private String allocationCode;    //分配单号	string
    private String batchNumber;//	批号	string	@mock=p020
    private String sterilizingBatchNumber;//	灭菌批号	string	@mock=p020
    private String commitUserName;//	提交人	string	@mock=WMS07
    private int dealMode;    //处理方式(1.确认冲红,2.找到货-补捡,3.已补货-补捡)	number	@mock=2
    //    dealTime    //处理时间
    private String dealUserName;    //处理人	string	@mock=WMS07
    private int exceptionCause;    //异常类型 （1.少货 2.多货）	number	@mock=1
    private int exceptionNumber;    //差异数量	number	@mock=1
    private String exceptionTaskCode;    //异常任务单号	string	@mock=YC1904100005
    private int exceptionType;//	提交节点 (1.零货拣货 2.内复核 3.整件拣货)	number	@mock=3
    private String goodsAllocation;    //货位编码	string	@mock=QXZ01-05
    private int isAskFor;    //是否索取 pda预留字段（0 未索取 1 已索取）	number	@mock=1
    private String manufacturer;    //生产厂家	string	@mock=江苏鱼跃医疗设备股份有限公司
    private String orderCode;    //订单号	string	@mock=CKD190410000014
    private String ownerCode;    //业主编码	string	@mock=007
    private String passBoxCode;    //周转箱号	string	@mock=1190410000008
    private int pieceNumber;    //件包装数量	number	@mock=40
    private String productCode;    //商品编号	string	@mock=Q8001348
    private String productName;    //商品名称	string	@mock=指夹式脉搏血氧仪
    private String remark;    //备注	string	@mock=
    private String reviewStage;//	复核台	string	@mock=
    private String specifications;    //规格	string	@mock=YX300
    private String status;    //'处理状态 1 未处理 2 处理完成',	string	@mock=1
    private String tagCode;    //标签条码
    private String yn;    //是否有效 1 是 0 否	number	@mock=1
    private int rushCause = -1;//	冲红原因	number
    private String rushCauseStr;    //冲红原因字典	string
    private int rushNumber = -1;    //冲红数量	number


    // 新加字段
    private String erpOrderCode;
    private String customerCode;
    private String customerName;
    private String orgCode;
    private String channelCode;
    private String warehouseCode;
    private String buildingCode;
    private String storageAreaCode;
    private String storageRoomCode;
    private String storageTypeCode;
    private String productionTime; // 生产日期
    private String produceTime; //生产日期-新
    private String valiperiodValidity ; // 有效期
    private String validDate; //有效期-新
    private String exceptionReasonName;  //异常原因

    private String statusDesc;  //处理结果
    private int pickNumber; //补件数量-已处理

    private String rushCause8tr; //冲红原因

    public String getRushCause8tr() {
        return rushCause8tr;
    }

    public void setRushCause8tr(String rushCause8tr) {
        this.rushCause8tr = rushCause8tr;
    }

    public int getPickNumber() {
        return pickNumber;
    }

    public void setPickNumber(int pickNumber) {
        this.pickNumber = pickNumber;
    }

    public String getExceptionReasonName() {
        return exceptionReasonName;
    }

    public void setExceptionReasonName(String exceptionReasonName) {
        this.exceptionReasonName = exceptionReasonName;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public String getValidDate(){
        return this.validDate;
    }
    public void setValidDate(String validDate) {
        this.validDate = validDate;
    }

    public String getProduceTime(){
        return this.produceTime;
    }
    public void setProduceTime(String produceTime) {
        this.produceTime = produceTime;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getErpOrderCode() {
        return erpOrderCode;
    }

    public void setErpOrderCode(String erpOrderCode) {
        this.erpOrderCode = erpOrderCode;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getBuildingCode() {
        return buildingCode;
    }

    public void setBuildingCode(String buildingCode) {
        this.buildingCode = buildingCode;
    }

    public String getStorageAreaCode() {
        return storageAreaCode;
    }

    public void setStorageAreaCode(String storageAreaCode) {
        this.storageAreaCode = storageAreaCode;
    }

    public String getStorageRoomCode() {
        return storageRoomCode;
    }

    public void setStorageRoomCode(String storageRoomCode) {
        this.storageRoomCode = storageRoomCode;
    }

    public String getStorageTypeCode() {
        return storageTypeCode;
    }

    public void setStorageTypeCode(String storageTypeCode) {
        this.storageTypeCode = storageTypeCode;
    }

    public String getDealUserName() {
        return dealUserName;
    }

    public void setDealUserName(String dealUserName) {
        this.dealUserName = dealUserName;
    }

    public int getRushCause() {
        return rushCause;
    }

    public void setRushCause(int rushCause) {
        this.rushCause = rushCause;
    }

    public String getRushCauseStr() {
        return rushCauseStr;
    }

    public void setRushCauseStr(String rushCauseStr) {
        this.rushCauseStr = rushCauseStr;
    }

    public int getRushNumber() {
        return rushNumber;
    }

    public void setRushNumber(int rushNumber) {
        this.rushNumber = rushNumber;
    }

    public String getAllocationCode() {
        return allocationCode;
    }

    public void setAllocationCode(String allocationCode) {
        this.allocationCode = allocationCode;
    }

    public String getBatchNumber() {
        return batchNumber;
    }

    public void setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
    }

    public String getSterilizingBatchNumber() {
        return sterilizingBatchNumber;
    }

    public void setSterilizingBatchNumber(String sterilizingBatchNumber) {
        this.sterilizingBatchNumber = sterilizingBatchNumber;
    }

    public String getCommitUserName() {
        return commitUserName;
    }

    public void setCommitUserName(String commitUserName) {
        this.commitUserName = commitUserName;
    }

    public int getDealMode() {
        return dealMode;
    }

    public void setDealMode(int dealMode) {
        this.dealMode = dealMode;
    }

    public int getExceptionCause() {
        return exceptionCause;
    }

    public void setExceptionCause(int exceptionCause) {
        this.exceptionCause = exceptionCause;
    }

    public int getExceptionNumber() {
        return exceptionNumber;
    }

    public void setExceptionNumber(int exceptionNumber) {
        this.exceptionNumber = exceptionNumber;
    }

    public String getExceptionTaskCode() {
        return exceptionTaskCode;
    }

    public void setExceptionTaskCode(String exceptionTaskCode) {
        this.exceptionTaskCode = exceptionTaskCode;
    }

    public int getExceptionType() {
        return exceptionType;
    }

    public void setExceptionType(int exceptionType) {
        this.exceptionType = exceptionType;
    }

    public String getGoodsAllocation() {
        return goodsAllocation;
    }

    public void setGoodsAllocation(String goodsAllocation) {
        this.goodsAllocation = goodsAllocation;
    }

    public int getIsAskFor() {
        return isAskFor;
    }

    public void setIsAskFor(int isAskFor) {
        this.isAskFor = isAskFor;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getPassBoxCode() {
        return passBoxCode;
    }

    public void setPassBoxCode(String passBoxCode) {
        this.passBoxCode = passBoxCode;
    }

    public int getPieceNumber() {
        return pieceNumber;
    }

    public void setPieceNumber(int pieceNumber) {
        this.pieceNumber = pieceNumber;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getReviewStage() {
        return reviewStage;
    }

    public void setReviewStage(String reviewStage) {
        this.reviewStage = reviewStage;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTagCode() {
        return tagCode;
    }

    public void setTagCode(String tagCode) {
        this.tagCode = tagCode;
    }

    public String getYn() {
        return yn;
    }

    public void setYn(String yn) {
        this.yn = yn;
    }

    public String getProductionTime() {
        return productionTime;
    }

    public void setProductionTime(String productionTime) {
        this.productionTime = productionTime;
    }

    public String getValiperiodValidity() {
        return valiperiodValidity;
    }

    public void setValiperiodValidity(String valiperiodValidity) {
        this.valiperiodValidity = valiperiodValidity;
    }
}
