package com.xyy.wms.pda.contract.out.purchaseexit;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.productCode.ProductCode;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseRefundOrderDetailsBean;

import java.util.List;

import io.reactivex.Observable;

/**
 * Created by zcj on 2018/11/17 11
 * 购进退出电子追溯码
 */
public interface PurchaseExitElectronicContract {

    interface IPurchaseExitElectronicModel extends IBaseModel {

        /**
         * 通过容器编号查询
         */
        Observable<BaseResponseBean<List<PurchaseRefundOrderDetailsBean>>> queryUnScannedGoods(String containerCode);

        /**
         * 整单解锁
         */
        Observable<BaseResponseBean> updateWholeSingleUnlock(String refundOrderCode);

        /**
         * 根据商品条码查询商品信息
         */

        Observable<BaseResponseBean<List<ProductCode>>> selectProductPda(String ownerCode, String packageBarCode);
    }

    interface IPurchaseExitElectronicView extends IBaseActivity {

        void queryUnScannedGoodsSuccess(BaseResponseBean<List<PurchaseRefundOrderDetailsBean>> bean);

        void updateWholeSingleUnlockSuccess(BaseResponseBean baseBean);

        void selectProductPdaSuccess(BaseResponseBean<List<ProductCode>> baseBean);
    }

    abstract class PurchaseExitElectronicPresenter extends BasePresenter<IPurchaseExitElectronicModel, IPurchaseExitElectronicView> {

        public abstract void queryUnScannedGoods(String containerCode);

        public abstract void updateWholeSingleUnlock(String refundOrderCode);

        public abstract void selectProductPda(String ownerCode, String packageBarCode);
    }
}
