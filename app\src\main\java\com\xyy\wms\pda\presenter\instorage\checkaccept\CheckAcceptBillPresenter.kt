package com.xyy.wms.pda.presenter.instorage.checkaccept

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckAcceptBill
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckAcceptGoodsResult
import com.xyy.wms.pda.contract.instorage.checkaccept.CheckAcceptBillContract
import com.xyy.wms.pda.helper.SimpleErrorConsumer
import com.xyy.wms.pda.helper.SimpleSuccessConsumer
import com.xyy.wms.pda.model.instorage.checkaccept.CheckAcceptBillModel

/**
 * Created by XyyMvpPdaTemplate on 03/04/2020 15:56
 */
class CheckAcceptBillPresenter : BasePresenter<CheckAcceptBillContract.ICheckAcceptBillModel, CheckAcceptBillContract.ICheckAcceptBillView>() {

  override fun getModel(): CheckAcceptBillModel {
    return CheckAcceptBillModel.newInstance()
  }

  fun getCheckOrderList() {
    mRxManager.register(mIModel.getCheckOrder().subscribe(object : SimpleSuccessConsumer<BaseResponseBean<MutableList<CheckAcceptBill>>>(mIView) {
      override fun onSuccess(baseResponseBean: BaseResponseBean<MutableList<CheckAcceptBill>>) {
        mIView.getCheckOrderSuccess(baseResponseBean)
      }
    }, SimpleErrorConsumer(mIView)))
  }

  fun doUnbindingForOrderCode(checkOrderCode: String) {
    mRxManager.register(mIModel.doUnbindingForOrderCode(checkOrderCode).subscribe(object : SimpleSuccessConsumer<BaseResponseBean<Any>>(mIView) {
      override fun onSuccess(baseResponseBean: BaseResponseBean<Any>) {
        mIView.doUnbindingForOrderCodeSuccess(baseResponseBean)
      }
    }, SimpleErrorConsumer(mIView)))
  }

  companion object {
    fun newInstance(): CheckAcceptBillPresenter {
      return CheckAcceptBillPresenter()
    }
  }

  fun getDetailsByOrderCode(checkOrderCode: String) {
    mRxManager.register(mIModel.getDetailsByOrderCode(checkOrderCode).subscribe(object : SimpleSuccessConsumer<BaseResponseBean<CheckAcceptGoodsResult>>(mIView) {
      override fun onSuccess(baseResponseBean: BaseResponseBean<CheckAcceptGoodsResult>) {
        mIView.getDetailsByOrderCodeStatus(baseResponseBean,checkOrderCode)
      }
    }, SimpleErrorConsumer(mIView)))
  }
}
