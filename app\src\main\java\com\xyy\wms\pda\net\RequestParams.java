package com.xyy.wms.pda.net;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.util.Map;
import java.util.TreeMap;

import okhttp3.MediaType;
import okhttp3.RequestBody;

/**
 * 网络请求参数封装
 */
public class RequestParams {

    //数据请求类型
    public final static String CONTENT_TYPE_JSON = "Content-type:application/json;charset=UTF-8";
    public final static String CONTENT_TYPE_FORM = "Content-type:application/x-www-form-urlencoded;charset=UTF-8";
    public final static String CONTENT_TYPE_MULTI = "Content-type:multipart/form-data;charset=UTF-8";

    private static Gson gson = new GsonBuilder().serializeNulls().create();

    private RequestParams() {
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {

        public Builder() {
        }

        //所有参数集合(最后会包括需要签名的参数) data数据
        Map<String, Object> paramsMap = new TreeMap<>();

        /**
         * 添加参数
         *
         * @param key
         * @param value
         * @return
         */
        public Builder add(String key, Object value) {
            if (!TextUtils.isEmpty(key) && value != null) {
                paramsMap.put(key, value);
            }
            return this;
        }

        /**
         * 请求体默认为表单格式
         * 如需JSON使用请使用  {@link #jsonBody}
         * 如需使用GET请求请使用
         *
         * @return
         */
        public RequestBody body() {
            String body = gson.toJson(paramsMap);
            return RequestBody.create(MediaType.parse(CONTENT_TYPE_FORM), body);
        }

        /**
         * 请求体默认为表单格式
         * 如需表单请使用  {@link #body} 方法
         * 如需使用GET请求请使用
         *
         * @return
         */
        public RequestBody jsonBody() {
            String body = gson.toJson(paramsMap);
            return RequestBody.create(MediaType.parse(CONTENT_TYPE_JSON), body);
        }
    }
}
