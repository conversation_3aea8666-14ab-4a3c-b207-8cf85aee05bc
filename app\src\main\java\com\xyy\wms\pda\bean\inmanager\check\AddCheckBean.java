package com.xyy.wms.pda.bean.inmanager.check;

/**
 * 新增盘点
 */
public class AddCheckBean {
    /**
     * billSource             number	固定值2，代表盘点来源PDA
     * checkNo                string	初盘计划单号
     * checkPersonSum         number	盘点人数，必须为大于0的整数
     * checkPlanType          number	固定值1代表初盘，盘点类型分初盘，复盘，终盘
     * checkType              number	盘点类型 1静态盘点2动态盘点
     * checkWay               number	盘点方式 1明盘2盲盘
     * dynamicCheckSource     number	动盘来源 空代表全部，1代表出库，2代表入库，3代表移库，4代表货位调整，5代表损溢
     * endGoodsPositionCode   string	截止货位
     * startGoodsPositionCode string	起始货位
     * storageAreaId          number    库区id（必传）
     * storageTypeId          number    库别id（必传）
     */
    private String billSource;
    private String checkNo;
    private String checkPersonSum;
    private String checkPlanType;
    private String checkType;
    private String checkWay;
    private String dynamicCheckSource;
    private String endGoodsPositionCode;
    private String startGoodsPositionCode;
    private String storageAreaId;
    private String storageTypeId;

    public String getBillSource() {
        return billSource;
    }

    public void setBillSource(String billSource) {
        this.billSource = billSource;
    }

    public String getCheckNo() {
        return checkNo;
    }

    public void setCheckNo(String checkNo) {
        this.checkNo = checkNo;
    }

    public String getCheckPersonSum() {
        return checkPersonSum;
    }

    public void setCheckPersonSum(String checkPersonSum) {
        this.checkPersonSum = checkPersonSum;
    }

    public String getCheckPlanType() {
        return checkPlanType;
    }

    public void setCheckPlanType(String checkPlanType) {
        this.checkPlanType = checkPlanType;
    }

    public String getCheckType() {
        return checkType;
    }

    public void setCheckType(String checkType) {
        this.checkType = checkType;
    }

    public String getCheckWay() {
        return checkWay;
    }

    public void setCheckWay(String checkWay) {
        this.checkWay = checkWay;
    }

    public String getDynamicCheckSource() {
        return dynamicCheckSource;
    }

    public void setDynamicCheckSource(String dynamicCheckSource) {
        this.dynamicCheckSource = dynamicCheckSource;
    }

    public String getEndGoodsPositionCode() {
        return endGoodsPositionCode;
    }

    public void setEndGoodsPositionCode(String endGoodsPositionCode) {
        this.endGoodsPositionCode = endGoodsPositionCode;
    }

    public String getStartGoodsPositionCode() {
        return startGoodsPositionCode;
    }

    public void setStartGoodsPositionCode(String startGoodsPositionCode) {
        this.startGoodsPositionCode = startGoodsPositionCode;
    }

    public String getStorageAreaId() {
        return storageAreaId;
    }

    public void setStorageAreaId(String storageAreaId) {
        this.storageAreaId = storageAreaId;
    }

    public String getStorageTypeId() {
        return storageTypeId;
    }

    public void setStorageTypeId(String storageTypeId) {
        this.storageTypeId = storageTypeId;
    }

    @Override
    public String toString() {
        return "AddCheckBean{" +
                "billSource='" + billSource + '\'' +
                ", checkNo='" + checkNo + '\'' +
                ", checkPersonSum='" + checkPersonSum + '\'' +
                ", checkPlanType='" + checkPlanType + '\'' +
                ", checkType='" + checkType + '\'' +
                ", checkWay='" + checkWay + '\'' +
                ", dynamicCheckSource='" + dynamicCheckSource + '\'' +
                ", endGoodsPositionCode='" + endGoodsPositionCode + '\'' +
                ", startGoodsPositionCode='" + startGoodsPositionCode + '\'' +
                ", storageAreaId='" + storageAreaId + '\'' +
                ", storageTypeId='" + storageTypeId + '\'' +
                '}';
    }
}
