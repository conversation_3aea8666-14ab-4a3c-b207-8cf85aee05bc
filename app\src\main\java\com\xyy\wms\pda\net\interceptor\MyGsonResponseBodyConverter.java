package com.xyy.wms.pda.net.interceptor;

import com.google.gson.Gson;
import com.google.gson.TypeAdapter;
import com.xyy.wms.pda.bean.base.BaseResponseBean;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.lang.reflect.Type;

import okhttp3.ResponseBody;
import retrofit2.Converter;

/**
 * Created by xyy on 2018/10/19.
 */

final class MyGsonResponseBodyConverter<T> implements Converter<ResponseBody, T> {
    private final Gson gson;
    private final TypeAdapter<T> adapter;
    /**
     * type用于方便JSON的解析
     */
    private Type mType;

    MyGsonResponseBodyConverter(Gson gson, TypeAdapter<T> adapter, Type type) {
        this.gson = gson;
        this.adapter = adapter;
        mType = type;
    }

    @Override
    public T convert(ResponseBody value) throws IOException {
        String resultStr = value.string();
        T result;
        try {
            result = new Gson().fromJson(resultStr, mType);
        } catch (Exception e) {
            e.printStackTrace();
            result = parseByJSONObject(resultStr);
        }
        return result;
    }

    /**
     * 用JSOObject来解析  因为后台很可能不规范  例如后台返回的result不是一个对象  而是"" ，会导致gons解析失败的。
     * 因此出此下策
     */
    private T parseByJSONObject(String resultStr) {
        try {
            JSONObject jsonObject = new JSONObject(resultStr);
            int code = jsonObject.optInt("code");
            String msg = jsonObject.optString("msg");
            BaseResponseBean baseBean = new BaseResponseBean();
            baseBean.setCode(code);
            baseBean.setMsg(msg);
            return (T) baseBean;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        BaseResponseBean b = new BaseResponseBean();
        b.setCode(10);
        b.setMsg("无法解析数据");
        return (T) b;
    }


}