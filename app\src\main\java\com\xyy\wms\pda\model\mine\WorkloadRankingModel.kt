package com.xyy.wms.pda.model.mine


import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.mine.WorkloadRankingBean
import com.xyy.wms.pda.contract.mine.WorkloadRankingContract
import com.xyy.wms.pda.model.ServiceModel
import io.reactivex.Observable

class WorkloadRankingModel : ServiceModel(), WorkloadRankingContract.IWorkloadRankingModel {

  override fun getWorkloadRankingList(map: Map<String, Any>): Observable<BaseResponseBean<List<WorkloadRankingBean>>> {
    return apiOutManagerService.getWorkloadRankingList(map).compose(RxHelper.rxSchedulerHelper())
  }

  companion object {
    @JvmStatic
    fun newInstance(): WorkloadRankingModel {
      return WorkloadRankingModel()
    }
  }
}
