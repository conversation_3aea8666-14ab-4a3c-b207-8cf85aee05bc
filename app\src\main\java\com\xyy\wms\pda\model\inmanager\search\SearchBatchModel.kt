package com.xyy.wms.pda.model.inmanager.search

import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.inmanager.search.SearchGoodPositionBean
import com.xyy.wms.pda.contract.inmanager.search.SearchBatchContract.ISearchBatchModel
import com.xyy.wms.pda.model.ServiceModel
import io.reactivex.Observable

/**
 * Created by XyyMvpSportTemplate on 03/26/2019 19:11
 */
class SearchBatchModel : ServiceModel(), ISearchBatchModel {

  override fun findGoodsPositionCode(batchNumber: String?, productCode: String?): Observable<BaseResponseBean<List<SearchGoodPositionBean>>> {
    return apiInManagerService.findGoodsPositionCode(batchNumber, productCode).compose(RxHelper.rxSchedulerHelper())
  }

  companion object {
    @JvmStatic
    fun newInstance(): SearchBatchModel {
      return SearchBatchModel()
    }
  }
}
