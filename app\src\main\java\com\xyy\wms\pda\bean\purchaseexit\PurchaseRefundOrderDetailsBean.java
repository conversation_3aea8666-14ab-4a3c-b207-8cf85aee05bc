package com.xyy.wms.pda.bean.purchaseexit;

import java.io.Serializable;

/**
 * Created by zcj on 2018/11/19 10
 * 购进退出
 */
public class PurchaseRefundOrderDetailsBean implements Serializable {
    /**
     * actualScatteredCount		;//	实际退货零散数
     * actualWholeCount			;//	实际退货整件数
     * containerCode			;//	容器编号	string	@mock=$order('861543','861538','861538')
     * dosageForm				;//	剂型	string	@mock=$order('1','1','1')
     * id						;//	id	number	@mock=$order(10597,10594,10595)
     * largeCategory			;//	商品大类	string	@mock=$order('内服','针剂','内服')
     * largePackingNumber		;//	件包装数量	string	@mock=$order('80','80','100')
     * lineNumber				;//	行号	number	@mock=$order(1,1,1)
     * manufacturer				;//	生产厂家	string	@mock=$order('安徽威尔曼制药有限公司','山西云鹏制药有限公司','鲁南厚普制药有限公司')
     * mediumPackageBarCode		;//	中包装条码	string	@mock=$order('21211221231232','564565465645654','21211221231232')
     * middlePackingNumber		;//	中包装数量
     * packingUnit				;//	包装单位	string	@mock=$order('盒','支','盒')
     * piecePackageBarCode		;//	件包装条码	string	@mock=$order('323635655656','232656232323','45645654')
     * producingArea			;//	产地	string	@mock=$order('安徽','山西','广东')
     * productBatchCode			;//	商品批号	string	@mock=$order('A00201-002','A00214-001','A00210-002')
     * productCode				;//	商品编号	string	@mock=$order('A00201','A00214','A00210')
     * productContainTaxPrice	;//	商品含税单价	string	@mock=$order('22','11','14')
     * productName				;//	商品名称	string	@mock=$order('复方鱼腥草软胶囊','右旋糖酐铁口服液','盐酸克林霉素胶囊')
     * productProduceDate		;//	商品生产日期
     * productValidDate			;//	商品有效期至
     * refundAlwaysCount		;//	单行商品退货总量
     * refundCount				;//	退货商品数量	number	@mock=$order(0,0,0)
     * refundCountPlan			;//	预计退货数量	number	@mock=$order(60,5,2)
     * refundOrderCode			;//	采购退出单编号	string	@mock=$order('GTR201811050002','GTR201811030001','GTR201811030001')
     * refundScatteredCount		;//	退货零散数	number	@mock=$order(0,0,0)
     * refundType				;//	商品退货类型(0:破损;1:正常采退;2:质量召回;3:其他)	string	@mock=$order('2','2','2')
     * refundWholeCount			;//	退货整件数	number	@mock=$order(0,0,0)
     * shelfGoodsAmount			;//	货位库存
     * shelfLocationCode		;//	货位编号
     * smallPackageBarCode		;//	小包装条码
     * specifications			;//	商品规格
     * storageAreaCode			;//	库区编号
     * storeCode				;//	库别编号
     * storeName				;//	库别名称
     * supplierCode				;//	供应商编号	string	@mock=$order('GYS001','DW001','DW001')
     * supplierName				;//	供应商名称	string	@mock=$order('供应商1','undefined','undefined')
     * whetherRegulatory		;//	是否监管（1 是 0否）	string	@mock=$order('0','1','0')
     */
    /**
     * post 所需
     */
    private String actualRefundCount;
    private String actualScatteredCount;
    private String actualWholeCount;
    private String approvalNumbers;
    private String containerCode;
    private String dictName;
    private String dosageForm;
    private String id;
    private String largeCategory;
    private String largePackingNumber;
    private String lineNumber;
    private String manufacturer;
    private String mediumPackageBarCode;
    private String packingUnit;
    private String piecePackageBarCode;
    private String producingArea;
    private String productBatchCode;
    private String productCode;
    private String productContainTaxPrice;
    private String productName;
    private String productProduceDate;
    private String productValidDate;
    private String refundAlwaysCount;
    private int refundCountPlan;
    private String refundOrderCode;
    private int refundScatteredCount;
    private String refundType;
    private int refundWholeCount;
    private String shelfGoodsAmount;
    private String smallPackageBarCode;
    private String supplierCode;
    private String supplierName;

    /**
     * 列表返回（包含上面的）
     */
    private String middlePackingNumber;
    private String refundCount;
    private String shelfLocationCode;
    private String specifications;
    private String storageAreaCode;
    private String storeCode;
    private String storeName;
    private String whetherRegulatory;

    /**
     * 用于记录操作状态
     */
    private int opereationStatus;
    /**
     * 电子追溯码扫描单状态 1:进行中，2：已完成
     */
    private int scanStatus;

    public int getRefundWholeCount() {
        return refundWholeCount;
    }

    public void setRefundWholeCount(int refundWholeCount) {
        this.refundWholeCount = refundWholeCount;
    }

    public int getScanStatus() {
        return scanStatus;
    }

    public void setScanStatus(int scanStatus) {
        this.scanStatus = scanStatus;
    }

    public String getActualScatteredCount() {
        return actualScatteredCount;
    }

    public void setActualScatteredCount(String actualScatteredCount) {
        this.actualScatteredCount = actualScatteredCount;
    }

    public String getActualWholeCount() {
        return actualWholeCount;
    }

    public void setActualWholeCount(String actualWholeCount) {
        this.actualWholeCount = actualWholeCount;
    }

    public String getApprovalNumbers() {
        return approvalNumbers;
    }

    public void setApprovalNumbers(String approvalNumbers) {
        this.approvalNumbers = approvalNumbers;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public void setContainerCode(String containerCode) {
        this.containerCode = containerCode;
    }

    public String getDictName() {
        return dictName;
    }

    public void setDictName(String dictName) {
        this.dictName = dictName;
    }

    public String getDosageForm() {
        return dosageForm;
    }

    public void setDosageForm(String dosageForm) {
        this.dosageForm = dosageForm;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLargeCategory() {
        return largeCategory;
    }

    public void setLargeCategory(String largeCategory) {
        this.largeCategory = largeCategory;
    }

    public String getLargePackingNumber() {
        return largePackingNumber;
    }

    public void setLargePackingNumber(String largePackingNumber) {
        this.largePackingNumber = largePackingNumber;
    }

    public String getLineNumber() {
        return lineNumber;
    }

    public void setLineNumber(String lineNumber) {
        this.lineNumber = lineNumber;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getMediumPackageBarCode() {
        return mediumPackageBarCode;
    }

    public void setMediumPackageBarCode(String mediumPackageBarCode) {
        this.mediumPackageBarCode = mediumPackageBarCode;
    }

    public String getPackingUnit() {
        return packingUnit;
    }

    public void setPackingUnit(String packingUnit) {
        this.packingUnit = packingUnit;
    }

    public String getPiecePackageBarCode() {
        return piecePackageBarCode;
    }

    public void setPiecePackageBarCode(String piecePackageBarCode) {
        this.piecePackageBarCode = piecePackageBarCode;
    }

    public String getProducingArea() {
        return producingArea;
    }

    public void setProducingArea(String producingArea) {
        this.producingArea = producingArea;
    }

    public String getProductBatchCode() {
        return productBatchCode;
    }

    public void setProductBatchCode(String productBatchCode) {
        this.productBatchCode = productBatchCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductContainTaxPrice() {
        return productContainTaxPrice;
    }

    public void setProductContainTaxPrice(String productContainTaxPrice) {
        this.productContainTaxPrice = productContainTaxPrice;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductProduceDate() {
        return productProduceDate;
    }

    public void setProductProduceDate(String productProduceDate) {
        this.productProduceDate = productProduceDate;
    }

    public String getProductValidDate() {
        return productValidDate;
    }

    public void setProductValidDate(String productValidDate) {
        this.productValidDate = productValidDate;
    }

    public String getRefundAlwaysCount() {
        return refundAlwaysCount;
    }

    public void setRefundAlwaysCount(String refundAlwaysCount) {
        this.refundAlwaysCount = refundAlwaysCount;
    }

    public int getRefundCountPlan() {
        return refundCountPlan;
    }

    public void setRefundCountPlan(int refundCountPlan) {
        this.refundCountPlan = refundCountPlan;
    }

    public String getRefundOrderCode() {
        return refundOrderCode;
    }

    public void setRefundOrderCode(String refundOrderCode) {
        this.refundOrderCode = refundOrderCode;
    }

    public int getRefundScatteredCount() {
        return refundScatteredCount;
    }

    public void setRefundScatteredCount(int refundScatteredCount) {
        this.refundScatteredCount = refundScatteredCount;
    }

    public String getRefundType() {
        return refundType;
    }

    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }

    public String getShelfGoodsAmount() {
        return shelfGoodsAmount;
    }

    public void setShelfGoodsAmount(String shelfGoodsAmount) {
        this.shelfGoodsAmount = shelfGoodsAmount;
    }

    public String getSmallPackageBarCode() {
        return smallPackageBarCode;
    }

    public void setSmallPackageBarCode(String smallPackageBarCode) {
        this.smallPackageBarCode = smallPackageBarCode;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getMiddlePackingNumber() {
        return middlePackingNumber;
    }

    public void setMiddlePackingNumber(String middlePackingNumber) {
        this.middlePackingNumber = middlePackingNumber;
    }

    public String getRefundCount() {
        return refundCount;
    }

    public void setRefundCount(String refundCount) {
        this.refundCount = refundCount;
    }

    public String getShelfLocationCode() {
        return shelfLocationCode;
    }

    public void setShelfLocationCode(String shelfLocationCode) {
        this.shelfLocationCode = shelfLocationCode;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getStorageAreaCode() {
        return storageAreaCode;
    }

    public void setStorageAreaCode(String storageAreaCode) {
        this.storageAreaCode = storageAreaCode;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getWhetherRegulatory() {
        return whetherRegulatory;
    }

    public void setWhetherRegulatory(String whetherRegulatory) {
        this.whetherRegulatory = whetherRegulatory;
    }

    public int getOpereationStatus() {
        return opereationStatus;
    }

    public void setOpereationStatus(int opereationStatus) {
        this.opereationStatus = opereationStatus;
    }

    public String getActualRefundCount() {
        return actualRefundCount;
    }

    public void setActualRefundCount(String actualRefundCount) {
        this.actualRefundCount = actualRefundCount;
    }

    @Override
    public String toString() {
        return "PurchaseRefundOrderDetailsBean{" +
                "actualRefundCount='" + actualRefundCount + '\'' +
                ", actualScatteredCount='" + actualScatteredCount + '\'' +
                ", actualWholeCount='" + actualWholeCount + '\'' +
                ", approvalNumbers='" + approvalNumbers + '\'' +
                ", containerCode='" + containerCode + '\'' +
                ", dictName='" + dictName + '\'' +
                ", dosageForm='" + dosageForm + '\'' +
                ", id='" + id + '\'' +
                ", largeCategory='" + largeCategory + '\'' +
                ", largePackingNumber='" + largePackingNumber + '\'' +
                ", lineNumber='" + lineNumber + '\'' +
                ", manufacturer='" + manufacturer + '\'' +
                ", mediumPackageBarCode='" + mediumPackageBarCode + '\'' +
                ", packingUnit='" + packingUnit + '\'' +
                ", piecePackageBarCode='" + piecePackageBarCode + '\'' +
                ", producingArea='" + producingArea + '\'' +
                ", productBatchCode='" + productBatchCode + '\'' +
                ", productCode='" + productCode + '\'' +
                ", productContainTaxPrice='" + productContainTaxPrice + '\'' +
                ", productName='" + productName + '\'' +
                ", productProduceDate='" + productProduceDate + '\'' +
                ", productValidDate='" + productValidDate + '\'' +
                ", refundAlwaysCount='" + refundAlwaysCount + '\'' +
                ", refundCountPlan=" + refundCountPlan +
                ", refundOrderCode='" + refundOrderCode + '\'' +
                ", refundScatteredCount='" + refundScatteredCount + '\'' +
                ", refundType='" + refundType + '\'' +
                ", refundWholeCount='" + refundWholeCount + '\'' +
                ", shelfGoodsAmount='" + shelfGoodsAmount + '\'' +
                ", smallPackageBarCode='" + smallPackageBarCode + '\'' +
                ", supplierCode='" + supplierCode + '\'' +
                ", supplierName='" + supplierName + '\'' +
                ", middlePackingNumber='" + middlePackingNumber + '\'' +
                ", refundCount='" + refundCount + '\'' +
                ", shelfLocationCode='" + shelfLocationCode + '\'' +
                ", specifications='" + specifications + '\'' +
                ", storageAreaCode='" + storageAreaCode + '\'' +
                ", storeCode='" + storeCode + '\'' +
                ", storeName='" + storeName + '\'' +
                ", whetherRegulatory='" + whetherRegulatory + '\'' +
                ", opereationStatus=" + opereationStatus +
                '}';
    }
}
