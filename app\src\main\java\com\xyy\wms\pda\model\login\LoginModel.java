package com.xyy.wms.pda.model.login;

import androidx.annotation.NonNull;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.user.LoginInfo;
import com.xyy.wms.pda.contract.login.LoginContract;
import com.xyy.wms.pda.model.ServiceModel;
import com.xyy.wms.pda.net.RetrofitCreateHelper;
import com.xyy.wms.pda.bean.req.LoginReq;

import io.reactivex.Observable;

public class LoginModel extends ServiceModel implements LoginContract.ILoginModel {

    @NonNull
    public static LoginModel newInstance() {
        return new LoginModel();
    }

    @Override
    public Observable<BaseResponseBean<String>> login(LoginReq req) {
        return getApiService().login(req).compose(RxHelper.rxSchedulerHelper());
    }

}
