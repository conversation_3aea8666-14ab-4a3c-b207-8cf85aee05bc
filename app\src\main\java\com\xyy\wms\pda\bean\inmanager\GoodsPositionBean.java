package com.xyy.wms.pda.bean.inmanager;

/**
 * 货位信息
 */
public class GoodsPositionBean {
    /**
     * goodsAddressCode	货位地址	string	@mock=LHKA01010101
     * goodsPositionCode	货位编码	string	@mock=A01-010101
     * id	id	number	@mock=1
     * storageAreaCode	库区编码	string	@mock=A
     * storageAreaId	库区标识	string
     * storageTypeCode	库别编码	string	@mock=LHK
     * storageTypeName	库别名称	string	@mock=零货库
     */


    private String goodsAddressCode;//	货位地址	string	@mock=LHKA01010101
    private String goodsPositionCode;//	货位编码	string	@mock=A01-010101
    private String id;//id	number	@mock=1
    private String storageAreaCode;//	库区编码	string	@mock=A
    private String storageAreaId;//	库区标识	string
    private String storageTypeCode;//	库别编码	string	@mock=LHK
    private String storageTypeName;//	库别名称	string	@mock=零货库

    public String getGoodsAddressCode() {
        return goodsAddressCode;
    }

    public void setGoodsAddressCode(String goodsAddressCode) {
        this.goodsAddressCode = goodsAddressCode;
    }

    public String getGoodsPositionCode() {
        return goodsPositionCode;
    }

    public void setGoodsPositionCode(String goodsPositionCode) {
        this.goodsPositionCode = goodsPositionCode;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStorageAreaCode() {
        return storageAreaCode;
    }

    public void setStorageAreaCode(String storageAreaCode) {
        this.storageAreaCode = storageAreaCode;
    }

    public String getStorageAreaId() {
        return storageAreaId;
    }

    public void setStorageAreaId(String storageAreaId) {
        this.storageAreaId = storageAreaId;
    }

    public String getStorageTypeCode() {
        return storageTypeCode;
    }

    public void setStorageTypeCode(String storageTypeCode) {
        this.storageTypeCode = storageTypeCode;
    }

    public String getStorageTypeName() {
        return storageTypeName;
    }

    public void setStorageTypeName(String storageTypeName) {
        this.storageTypeName = storageTypeName;
    }

    @Override
    public String toString() {
        return "GoodsPositionBean{" +
                "goodsAddressCode='" + goodsAddressCode + '\'' +
                ", goodsPositionCode='" + goodsPositionCode + '\'' +
                ", id='" + id + '\'' +
                ", storageAreaCode='" + storageAreaCode + '\'' +
                ", storageAreaId='" + storageAreaId + '\'' +
                ", storageTypeCode='" + storageTypeCode + '\'' +
                ", storageTypeName='" + storageTypeName + '\'' +
                '}';
    }
}
