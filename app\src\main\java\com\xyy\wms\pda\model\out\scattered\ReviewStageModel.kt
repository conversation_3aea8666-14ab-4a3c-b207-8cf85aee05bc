package com.xyy.wms.pda.model.out.scattered

import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.contract.out.scattered.ReviewStageContract.IReviewStageModel
import com.xyy.wms.pda.model.ServiceModel
import io.reactivex.Observable

/**
 * 拆零拣货 - 复核台
 */
class ReviewStageModel : ServiceModel(), IReviewStageModel {

    override fun recordSettingTime(map: Map<String, String>): Observable<BaseResponseBean<*>> {
        return apiOutManagerService.recordSettingTime(map).compose(RxHelper.rxSchedulerHelper())
    }

    companion object {
        fun newInstance(): ReviewStageModel {
            return ReviewStageModel()
        }
    }
}
