package com.xyy.wms.pda.bean.out.storegoods
import java.io.Serializable
/**
 * created by  lian<PERSON><PERSON>  on 2019-12-12.
 */
data class StoreGoodsInfo(
        var clientName: String?, //客户名称    string
        var consolidationCode: String?,   // 拼箱号    string    如果是零货，显示拼箱号。整件为空
        var erpOrderCode: String?,   //    销售单号    string
        var orderCode: String?,   //    出库单号    string
        var secondConfirm: Int,   //   是否需2次确认发货区扫码    string    是否需2次确认发货区扫码 0：否，1：是
        var tagCode: String?,   //  标签条码    string
        var taskOutReviewCode: String?,   //   外复核任务单号    string
        var taskType: Int,   //   整散标识    number    1 整件 2 零货
        var workingAreaBegin: String?,   //  发货区起始号    string
        var workingAreaEnd: String?,  //   发货区终止号    string
        var workingAreaList: List<String>? //发货区 货位 校验集合
) : Serializable