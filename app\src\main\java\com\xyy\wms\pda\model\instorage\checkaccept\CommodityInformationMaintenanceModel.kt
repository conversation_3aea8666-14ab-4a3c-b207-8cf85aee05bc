package com.xyy.wms.pad.instorage.model.newinstorage

import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.api.ApiInAcceptService
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.GetUpperLowerProductInfoMaintainBean
import com.xyy.wms.pda.bean.instorage.checkaccept.GetUpperLowerProductInfoMaintainPost
import com.xyy.wms.pda.bean.instorage.checkaccept.LogicalRegionProductInfoMaintainBean
import com.xyy.wms.pda.bean.instorage.checkaccept.LogicalRegionProductInfoMaintainPost
import com.xyy.wms.pda.bean.instorage.checkaccept.ProductInfoMaintainQueryBean
import com.xyy.wms.pda.bean.instorage.checkaccept.ProductInfoMaintainQueryPost
import com.xyy.wms.pda.bean.instorage.checkaccept.SaveProductInfoMaintainPost
import com.xyy.wms.pda.contract.instorage.checkaccept.CommodityInformationMaintenanceContract
import com.xyy.wms.pda.net.RetrofitCreateHelper
import io.reactivex.Observable
import java.util.ArrayList

class CommodityInformationMaintenanceModel : CommodityInformationMaintenanceContract.CommodityInformationMaintenanceModel {
    companion object {
        fun newInstance() : CommodityInformationMaintenanceModel {
            return CommodityInformationMaintenanceModel()
        }
    }
    override fun queryProductInfoMaintain(productInfoMaintainQueryPost: ProductInfoMaintainQueryPost): Observable<BaseResponseBean<ProductInfoMaintainQueryBean>> {
      return RetrofitCreateHelper.createApi(ApiInAcceptService::class.java).queryProductInfoMaintain(productInfoMaintainQueryPost)
        .compose(RxHelper.rxSchedulerHelper())
    }
    override fun getUpperLowerProductInfoMaintain(getUpperLowerProductInfoMaintainPost: GetUpperLowerProductInfoMaintainPost): Observable<BaseResponseBean<GetUpperLowerProductInfoMaintainBean>> {
      return RetrofitCreateHelper.createApi(ApiInAcceptService::class.java).getUpperLowerProductInfoMaintain(getUpperLowerProductInfoMaintainPost)
        .compose(RxHelper.rxSchedulerHelper())
    }
    override fun logicalRegionProductInfoMaintain(logicalRegionProductInfoMaintainPost: LogicalRegionProductInfoMaintainPost): Observable<BaseResponseBean<ArrayList<LogicalRegionProductInfoMaintainBean>>> {
      return RetrofitCreateHelper.createApi(ApiInAcceptService::class.java).logicalRegionProductInfoMaintain(logicalRegionProductInfoMaintainPost)
        .compose(RxHelper.rxSchedulerHelper())
    }
    override fun saveProductInfoMaintain(saveProductInfoMaintainPost: SaveProductInfoMaintainPost): Observable<BaseResponseBean<String>> {
      return RetrofitCreateHelper.createApi(ApiInAcceptService::class.java).saveProductInfoMaintain(saveProductInfoMaintainPost)
        .compose(RxHelper.rxSchedulerHelper())
    }
}
