package com.xyy.wms.pda.presenter.instorage.checkaccept

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.common.DictListByNameAndType
import com.xyy.wms.pda.bean.instorage.checkaccept.WaitCheckInfoBySupplierBean
import com.xyy.wms.pda.bean.instorage.checkaccept.WaitCheckInfoBySupplierPost
import com.xyy.wms.pda.contract.instorage.checkaccept.CheckAcceptBillContractNew
import com.xyy.wms.pda.model.instorage.checkaccept.CheckAcceptBillModelNew
import com.xyy.wms.pda.helper.SimpleSuccessConsumer
import com.xyy.wms.pda.helper.SimpleErrorConsumer

/**
 * 入库验收单（New）Presenter：使用新 Model/Api，直接返回新 Bean
 */
class CheckAcceptBilPresenterNew : BasePresenter<CheckAcceptBillContractNew.ICheckAcceptBillModelNew, CheckAcceptBillContractNew.ICheckAcceptBillViewNew>() {
    override fun getModel(): CheckAcceptBillModelNew {
        return CheckAcceptBillModelNew.newInstance()
    }
    companion object {
        fun newInstance(): CheckAcceptBilPresenterNew = CheckAcceptBilPresenterNew()
    }
    fun getWaitCheckInfoBySupplier(waitCheckInfoBySupplierPost : WaitCheckInfoBySupplierPost) {
      mRxManager.register(mIModel.getWaitCheckInfoBySupplier(waitCheckInfoBySupplierPost)
        .subscribe(object :
          SimpleSuccessConsumer<BaseResponseBean<MutableList<WaitCheckInfoBySupplierBean>>>(mIView, "请求中···") {
          override fun onSuccess(responseBean : BaseResponseBean<MutableList<WaitCheckInfoBySupplierBean>>) {
            mIView.getWaitCheckInfoBySupplierSuccess(responseBean)
          }
        }, SimpleErrorConsumer(mIView))
      )
    }
}
