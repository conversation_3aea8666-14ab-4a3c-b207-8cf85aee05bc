package com.xyy.wms.pda.bean.common;

import com.xyy.wms.pda.bean.inmanager.MovementListBean;

import java.io.Serializable;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import kotlin.reflect.jvm.internal.impl.name.FqNameUnsafe;

/**
 * 进行中任务-返回值
 */
public class RunningTaskResult implements Serializable {
    private Boolean hasTask;
    private String taskId;
    private String taskType;
    private TaskContent taskContent;
    private List<TaskItemListResult> items;

    public static class TaskContent implements Serializable{
        String palletNo="";            //托盘编码
        String carNo="";               //车牌号
        String carNumber="";
        String logicalRegion="";       //逻辑区编码
        String sourceAllocation="";    //原货位
        String logicalRegionName="";  //逻辑区名称
        Number itemCnt;  //条目数
        Number productCnt;  //商品数

        public void setCarNumber(String carNumber){this.carNumber = carNumber;}
        public String getCarNumber(){return carNumber;}
        public void setPalletNo(String palletNo){this.palletNo = palletNo;}
        public String getPalletNo(){return palletNo;}
        public void setCarNo(String carNo){this.carNo = carNo;}
        public String getCarNo(){return carNo;}
        public void setLogicalRegion(String logicalRegion){this.logicalRegion = logicalRegion;}
        public String getLogicalRegion(){return logicalRegion;}
        public void setSourceAllocation(String sourceAllocation){this.sourceAllocation = sourceAllocation;}
        public String getSourceAllocation(){return sourceAllocation;}
        public void setLogicalRegionName(String logicalRegionName){this.logicalRegionName = logicalRegionName;}
        public String getLogicalRegionName(){return logicalRegionName;}
    }
    public void setTaskContent(TaskContent taskContent){
        this.taskContent = taskContent;
    }
    public TaskContent getTaskContent(){return taskContent;}
    public static class TaskItemListResult implements Serializable{
        String productCode;
        Number detailId;
        String productName;
        String batchNumber;
        String palletNo;   //托盘号

        String descAllocation; //目标货位

        String sourceAllocation;//原货位
        Number amount;
        Number status;
        Number productCnt;
        Number itemCnt;

        Boolean selected;
        Boolean downSelected;  //零货下架选中

        public void setPalletNo(String palletNo){this.palletNo = palletNo;}
        public String getPalletNo(){return palletNo;}
        public  void  setDetailId(Number detailId){
            this.detailId = detailId;
        }
        public Number getDetailId(){
            if(detailId != null){
                return detailId;
            }else{
                return null;
            }
        }
        public void setItemCnt(Number itemCnt){
            this.itemCnt = itemCnt;
        }
        public Number getItemCnt(){
            return itemCnt;
        }
        public void setProductCnt(Number productCnt) {
            this.productCnt = productCnt;
        }
        public Number getProductCnt(){
            return productCnt;
        }

        public String getProductCode() {
            return productCode;
        }

        public void setProductCode(String productCode) {
            this.productCode = productCode;
        }

        public String getBatchNumber() {
            return batchNumber;
        }


        public void setBatchNumber(String batchNumber) {
            this.batchNumber = batchNumber;
        }

        public String getSourceAllocation() {
            return sourceAllocation;
        }

        public void setSourceAllocation(String sourceAllocation) {
            this.sourceAllocation = sourceAllocation;
        }

        public String getDescAllocation() {
            return descAllocation;
        }

        public void setDescAllocation(String descAllocation) {
            this.descAllocation = descAllocation;
        }
        public Number getStatus() {
            return status;
        }

        public void setStatus(Number status) {
            this.status = status;
        }

        public void setProductName(String productName) {
            this.productName = productName;
        }
        public String getProductName(){return productName;}

        public void setSelected(Boolean selected){
            this.selected = selected;
        }

        public Boolean getSelected(){
            if(selected != null){
                return selected;
            }else{
                return false;
            }
        }

        public void setDownSelected(Boolean selected){this.downSelected = selected;}
        public Boolean getDownSelected(){
            if(downSelected!=null){
                return downSelected;
            }else{
                return false;
            }
        }
        public void setAmount(Number amount) {
            this.amount = amount;
        }
        public Number getAmount(){return amount;}
    }
    public void setList(List<TaskItemListResult> items) {
        this.items = items;
    }
    public List<TaskItemListResult> getList() {
        return items;
    }
    public void setHasTask(Boolean hasTask){this.hasTask=hasTask;}
    public Boolean getHasTask(){return hasTask;}
    public void setTaskId(String taskId){this.taskId = taskId;}
    public String getTaskId(){return taskId;}
    public void setTaskType(String taskType){this.taskType = taskType;}
    public String getTaskType(){return taskType;}
    public void sortItemsByStatus() {
        Collections.sort(items, new Comparator<TaskItemListResult>() {
            @Override
            public int compare(TaskItemListResult o1, TaskItemListResult o2) {
                return o1.getStatus().intValue() - o2.getStatus().intValue();
            }
        });
    }
}
