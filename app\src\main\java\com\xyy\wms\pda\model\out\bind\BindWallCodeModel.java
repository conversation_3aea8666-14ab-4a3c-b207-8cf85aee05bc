package com.xyy.wms.pda.model.out.bind;

import android.text.TextUtils;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.exception.ExceptionHandleListBean;
import com.xyy.wms.pda.bean.out.bind.CheckResult;
import com.xyy.wms.pda.contract.out.bind.BindWallCodeContract;
import com.xyy.wms.pda.contract.out.exception.ExceptionHandleContract;
import com.xyy.wms.pda.model.ServiceModel;

import java.util.Map;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 04/10/2019 14:59
 */
public class BindWallCodeModel extends ServiceModel implements BindWallCodeContract.IBindWallCodeModel {

    public static BindWallCodeModel newInstance() {
        return new BindWallCodeModel();
    }

    @Override
    public Observable<BaseResponseBean<CheckResult>> checkWallCode(Map<String, Object> params) {
        return getApiOutManagerService().checkWallCode(params).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<CheckResult>> checkErpOrderCode(Map<String, Object> params) {
        return getApiOutManagerService().checkErpOrderCode(params).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<CheckResult>> bindErpOrderCode(Map<String, Object> params) {
        return getApiOutManagerService().bindErpOrderCode(params).compose(RxHelper.rxSchedulerHelper());
    }
}
