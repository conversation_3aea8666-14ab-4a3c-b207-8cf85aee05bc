package com.xyy.wms.pda.bean.instorage

import com.xyy.wms.pda.utils.PickerDialogManager
import java.io.Serializable
import kotlin.jvm.Throws

/**
 * Created by sean on 3/27/20.
 */

data class CheckAcceptGoodsResult(
    val buildingCode: String,//	建筑物编码	string	@mock=01
    val buildingName: String,//	建筑物名称	string	@mock=主仓
    val checkOrderCode: String,//验收单号	string	@mock=YSD202002270051
    val checkOrderStatus: String,//	验收单状态	string	@mock=1
    val checkUser: String,//验收员id	string	@mock=781
    val id: String,//	单据主键id	string	@mock=9181
    val occupyType: Int,//	number	@mock=1
    val orgCode: String,//机构编码	string	@mock=D547
    val ownerCode: String,//	业主编码	string	@mock=009
    val purchaseCheckOrderDetailVos: ArrayList<CheckOrderDetail>,//	验收单明细	array<object>
    val receiveOrderCode: String,//收货单号	string	@mock=SHD202002270007
    val receiveUser: String,//	收货员id	string	@mock=781
    val receiveUserName: String,//收货员姓名	string	@mock=张泽宇
    val supplierCode: String,//供应商编号	string	@mock=GPFJIX052001
    val updateTime: Long,//	number	@mock=1583825985321
    val updateUser: String,//	string	@mock=781
    val warehouseCode: String,//	仓库编码	string	@mock=WMS360000_1
    val yn: Int,//	number	@mock=1
    val ysfsTypeDictCode: String,//		string	@mock=YSFS
    val ysgjTypeDictCode: String,//	string	@mock=YSGJ
    val receiveFinishTime: Long // 收货时间	number
)

class CheckOrderDetail : Serializable, Cloneable, Comparable<CheckOrderDetail> {
    override fun compareTo(other: CheckOrderDetail): Int {
        //验收中
        if (onShelfCount != 0 && shelfCount > onShelfCount && other.onShelfCount != 0
            && other.shelfCount > other.onShelfCount
        ) {
            return 0
        }
        // 未验收
        if (onShelfCount == 0 && other.onShelfCount == 0) {
            return 0
        }
        //验收完成
        if (onShelfCount == shelfCount && other.onShelfCount == other.shelfCount) {
            return 0
        }

        if (onShelfCount != 0 && shelfCount > onShelfCount
            && other.onShelfCount == 0
        ) {
            return -1
        }

        if (onShelfCount == 0 && other.onShelfCount == other.shelfCount) {
            return -1
        }

        if (onShelfCount != 0 && shelfCount > onShelfCount && other.onShelfCount == other.shelfCount) {
            return -1
        }
        return 0
    }

    var approvalNumbers: String? = null //	批准文号 string	@mock=国药准字Z32021182
    var arrivalDate: String? = null//	string	@mock=2020-02-27 00:00:00.0
    var buildingCode: String? = null//	建筑物编码	string	@mock=01
    var casualInspectionCount: Int? = -1//		number	@mock=300
    var channelCode: String? = null//	业务类型编码	string	@mock=2
    var checkDate: String? = null//	string	@mock=1970-01-01 08:01:00.0
    var checkOrderCode: String? = null//	string	@mock=YSD202002270051
    var checkOrderStatus: Int? = -1//	验收明细状态	number	@mock=1
    var checkOrderStatusMes: String? = null//	string	@mock=进行中
    var checkQualifiedQty: Int? = -1//	number	@mock=0
    var checkRemark: String? = null//	string	@mock=
    var checkResult: Int? = -1//	number	@mock=0
    var checkUser: Int? = -1//验收员id	string	@mock=781
    var commonName: String? = null//商品通用名称	string	@mock=板蓝根颗粒
    var containerCode: String? = null//容器编号	string	@mock=100110
    var containerCodeTemp: String? = null//	string	@mock=100110
    var createTime: String? = null//创建时间	string	@mock=2020-02-27 17:21:26
    var departCode: String? = null//部门编码	string	@mock=JXG0103
    var dosageForm: String? = null//剂型	string	@mock=颗粒剂
    var id: Long? = -1//明细主键id	number	@mock=1
    var largeCategory: String? = null//	商品大类	string	@mock=内服
    var lineNumber: Int? = -1//	行号	number	@mock=1
    var lineNumberSplitOrigin: Int? = -1//原始行号	number	@mock=1
    var loginUser: String? = null//	string	@mock=781
    var manufacturer: String? = null//生产厂家	string	@mock=江苏苏南药业实业有限公司
    var manufacturerName: String? = null//	string	@mock=1
    var marketAuthor: String? = null//上市许可持有人	string	@mock=上市许可持有人
    var occupyType: Int? = 1//	number	@mock=1
    var onWayCount: Int? = -1//	number	@mock=90
    var orderName: String? = null//	string	@mock=验收单
    var orderType: Int? = -1//	number	@mock=108
    var orgCode: String? = null//机构编码	string	@mock=D547
    var ownerCode: String? = null//业主编码	string	@mock=009
    var packingUnit: String? = null//	包装单位	string	@mock=包
    var prescriptionClassification: String? = null//处方分类	string	@mock=乙类非处方
    var producingArea: String? = null//产地	string	@mock=江苏
    var productBatchCode: String? = null//	批号	string	@mock=190508
    var productCheckCount: Int? = -1//商品抽检数量	number	@mock=0
    var productCode: String? = null//商品编码	string	@mock=Y3007433
    var productCountBig: String? = null//string	@mock=0  //   收货件数
    var productCountScatter: String? = null//	收货零散数	string	@mock=300
    var productCountSmall: Int? = null//	收货数量(以小包装为单位)	string	@mock=300
    var productPackUnitBigSpecification: Int? = null//	件包装规格	string	@mock=80
    var storageTypeCode: String? = null//	库别编码	string	@mock=LHK  ZJK 整件  LHK 零散  ZSHY 整散合一
    var productCountBigOld: String? = null//	string	@mock=0
    var productCountBigReal: Int? = -1//number	@mock=0
    var productCountBigcount: String? = null//		string	@mock=0
    var productCountScatterCount: String? = null//		string	@mock=300
    var productCountScatterReal: Int? = -1//真实收货零散数	number	@mock=300
    var productCountSmallReal: Int? = -1//	真实收货数量(以小包装为单位)	number	@mock=300
    var productManufactureDate: String? = null//	生产日期	string	@mock=2019-05-15
    var productName: String? = null//	商品名称	string	@mock=板蓝根颗粒
    var productPackUnitMediumSpecification: String? = null//中包装规格	string	@mock=1
    var productPackUnitSmall: String? = null//包装单位	string	@mock=包
    var productRealCheckCount: Int? = -1//商品实际验收数量	number	@mock=300
    var productSupervise: Int? = -1//追溯码采集(1:是;0:否)	number	@mock=0
    var productSuperviseMes: String? = null//	string	@mock=否
    var productTaxPrice: Double? = 0.0//	number	@mock=4.5
    var temporaryLocation: String? = null// 临存位

    // 特管，药品
    var productType: Int? = -1//商品类型	number	@mock=0   productType = 5或6 特管
    var productValidDate: String? = null//有效期至	string	@mock=2021-04-30
    var purchaseLineNumber: Int? = -1//采购单行号	number	@mock=3
    var purchaseOrderCode: String? = null//	采购单编号	string	@mock=CGD1911003245
    var purchaseUser: String? = null//采购员id	string	@mock=2105
    var receiveLineNumber: Int? = -1//收货单行号	number	@mock=1
    var receiveOrderCode: String? = null//	收货单号	string	@mock=SHD202002270007
    var receiveUser: String? = null//收货员id	string	@mock=781
    var receiveUserName: String? = null//收货员名字	string	@mock=781
    var registrationCertificate: String? = null//	进口注册证号
    var rejectReason: String? = null//	string	@mock=
    var rejectReasonSupplement: String? = null//	string	@mock=
    var scopeOfOperation: String? = null//	所属经营范围	string	@mock=中成药
    var shadingProperty: Int? = 0//	number	@mock=0
    var specifications: String? = null//商品规格	string	@mock=10g*20袋
    var storageAreaCode: String? = null//	string	@mock=
    var storageBhk: Int? = -1//是否入备货库(0:否;1:是)	number	@mock=0
    var storageClassification: String? = null//	存储分类	string	@mock=0（ 0 整散分开 1 整散合一）
    var storageConditions: String? = null//存储条件	string	@mock=7
    var storageConditionsName: String? = null//	页面展示 存储条件	string	@mock=常温
    var storageRoomCode: String? = null//库房编码	string	@mock=LHK
    var supplierCode: String? = null//	supplierName	string
    var supplierName: String? = null//	supplierName	string
    var updateTime: String? = null//string	@mock=2020-02-27 17:27:34
    var updateUser: String? = null//	string	@mock=781
    var usableOnWayCount: Int? = 0//	number	@mock=-222
    var warehouseCode: String? = null//	仓库编码	string	@mock=WMS360000_1
    var sterilizingBatchNumber: String? = null // 灭菌批号
    var yn: Int? = 0//逻辑删除,	number	@mock=1（0:删除;1:未删除）
    var spiltItems: ArrayList<AcceptOrderSpiltItem> = ArrayList()
    var hasReject: Boolean = false

    // 需验收数量
    var shelfCount: Int = 0

    // 已经验收数量
    var onShelfCount: Int = 0

    @Throws(CloneNotSupportedException::class)// 克隆失败抛出异常
    public override fun clone(): CheckOrderDetail {
        return super.clone() as CheckOrderDetail
    }
}

class AcceptOrderSpiltItem : Serializable {
    var acceptType: String? = null
    var acceptPackingNum: Int = 0
    var acceptScatteredNum: Int = 0
    var acceptNum: Int = 0
    var containerCode: String? = null
    var acceptResult: PickerDialogManager.SelectItemModel<AcceptResult> =
        PickerDialogManager.SelectItemModel(
            "",
            AcceptResult(AcceptOrderSpiltItem.NONE_ACCEPT_RESULT, "", "", -1, mutableMapOf())
        ) // 默认未选择
    var unqualified: String = ""
    var acceptNote: String = ""
    var acceptStatus = UNACCEPT_STATUS
    var typeTag: Int = -1
    var productManufactureDate: String? = null    //	生产日期	string	@mock=$order('','')
    var productValidDate: String? = null    //有效期至	string	@mock=$order('','')
    var productBatchCode: String? = null


    companion object {
        val NONE_ACCEPT_RESULT = Int.MIN_VALUE
        val QUALIFIED_ACCEPT_RESULT = 1   //合格
        val UNQUALIFIED_ACCEPT_RESULT = 4  //不合格
        val REFUSE_ACCEPT_RESULT = 2  //拒收
        val WAIT_ACCEPT_RESULT = 3   //待处理

        val ZJK_ACCEPT_TYPE = "ZJK"
        val LHK_ACCEPT_TYPE = "LHK"
        val ZSHY_ACCEPT_TYPE = "ZSHY"

        val ACCEPT_STATUS = 1
        val UNACCEPT_STATUS = 2
        val CONTAINER_USED = 3

        val IN_QUALIFIED = 1 // 入合格库
        val REJECTED = 2 // 拒收
        val REVIEW = 3 // 复查
        val IN_NOT_QUALIFIED = 4 // 入不合格库
        val IN_RETURN = 5 // 入退货库

    }
}

data class AcceptResult(
    var checkResult: Int,// 类型
    var checkResultDes: String, // 验收结论描述
    var handleDes: String, // 处理措施描述
    var handleDesType: Int, // 处理措施
    var handleItems: MutableMap<Int, String>
) : Serializable

data class OptionsResult(
    var name: String,// 显示的文字
    var value: String // 值
) : Serializable


class AcceptDetailListPost : Serializable {
    var checkOrderCode: String? = null//验收单号	string	@mock=YSD202003030071
    var checkOrderDetailVos: ArrayList<AcceptCommitBean>? = null//	验收单明细对应的拆分行	array<object>
    var currentRejects: String? = null//	是否当场拒收 (是"1" 否"2")	string	@mock=1
    var receiveOrderCode: String? = null//收货单号	string	@mock=
    var receiveUser: String? = null//收货人	string	@mock=
    var rejectRemark: String? = null//	拒收备注	string	@mock=拒收原因
    var secondCheckUser: String? = null//		string	@mock=拒收原因
}

class AcceptCommitBean : Serializable {
    var checkOrderCode: String? =
        null //验收单号	string	@mock=$order('YSD202003030071','YSD202003030071')
    var checkOrderDetailSplitVoList: ArrayList<AcceptCommitSplitItemBean> = ArrayList()
    var containerCode: String? = null //    容器编号    string    @mock =
    var lineNumber: Int? = null //   行号    number    @mock = 1
    var productCode: String? = null //   商品编号    string    @mock =
    var productName: String? = null //   商品名称    string    @mock =
    var sortNumber: Int? = null //  明细界面 排序后的序号 用来提示错误行号    number    @mock = 0
}

class AcceptCommitSplitItemBean : Serializable {
    var checkOrderCode: String? =
        null //验收单号	string	@mock=$order('YSD202003030071','YSD202003030071')
    var checkRemark: String? = null    //验收备注	string	@mock=$order('验收备注1','验收备注2')
    var checkResult: Int? = -1    //验收结论	number	@mock=$order(1,2)
    var containerCode: String? = null    //	容器编号	string	@mock=$order('100125','100125')
    var lineNumber: Int? = -1    //行号	number	@mock=$order(1,1)
    var logContainerCode: String? = null    //	原容器编号	string	@mock=$order('','')
    var productCountBig: String? = null    //收货整件数	string	@mock=$order('0','0')
    var productCountScatter: String? = null    //收货零散数	string	@mock=$order('4','6')
    var productCountSmall: String? = null    //	收货数量（收货件数*件包装规格+收货零散数）	string	@mock=$order('4','6')
    var productManufactureDate: String? = null    //	生产日期	string	@mock=$order('','')
    var productValidDate: String? = null    //有效期至	string	@mock=$order('','')
    var productBatchCode: String? = null
    var rejectReasonSupplement: String? = null
    var storageClassification: String? = null
    var sortNumber: Int? = -1//明细排序后的序号，提示错误行用	number	@mock=$order(2,2)
    var treatmentMeasures: Int? = -1//处理措施1.入合格库2.拒收 3.复查 4.入不合格库 5.入退货库
}

data class SecondLoginPost(
    val account: String?,    //	账号	string	liuyao1
    val password: String?,   //	密码	string	xyy@ybm100
    val sourceType: String    //  资源类型（固定值 btn:wms:inStorageDoubleCheck）	string	btn:wms:inStorageDoubleCheck

) : Serializable

data class SecondLoginResult(
    val account: String?,//	账号	string	@mock=liuyao1
    val id: String?,//用户id	string	@mock=4418
    val userName: String?//	用户名	string	@mock=刘瑶
) : Serializable


data class InspectionSubmitResult(
    val lineNum: Int,
    val errorTag: String,
    val tagType: Int
)
