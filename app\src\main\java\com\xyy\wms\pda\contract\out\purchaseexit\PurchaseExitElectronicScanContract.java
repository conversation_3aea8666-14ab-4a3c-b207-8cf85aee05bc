package com.xyy.wms.pda.contract.out.purchaseexit;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseFragment;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseCodeScanResultBean;

import io.reactivex.Observable;

/**
 * Created by lwj on 2020-02-24.
 * <EMAIL>
 * 电子监管码扫描
 */
public interface PurchaseExitElectronicScanContract {


    interface IPurchaseExitElectronicScanModel extends IBaseModel {

        Observable<BaseResponseBean<PurchaseCodeScanResultBean>> findDrugRegulatoryCode(
                String actualRefundCount,
                String codeLevel,
                String lineNumber,
                String pickUpOrder,
                String productCode,
                String regulatoryCode
        );
    }


    interface IPurchaseExitElectronicScanView extends IBaseFragment {

        void findDrugRegulatoryCodeSuccess(BaseResponseBean<PurchaseCodeScanResultBean> bean);

    }

    abstract class IPurchaseExitElectronicScanPresenter extends BasePresenter<IPurchaseExitElectronicScanModel, IPurchaseExitElectronicScanView> {

        /**
         * 电子监管码扫描单
         *
         * @param pickUpOrder 单据编号
         */
        public abstract void findDrugRegulatoryCode(String actualRefundCount,
                                                    String codeLevel,
                                                    String lineNumber,
                                                    String pickUpOrder,
                                                    String productCode,
                                                    String regulatoryCode);

    }


}
