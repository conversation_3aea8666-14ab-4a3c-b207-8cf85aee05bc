package com.xyy.wms.pda.model.instorage.wholeshelf;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.api.ApiService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.common.RunningTaskResult;
import com.xyy.wms.pda.bean.instorage.shelf.InShelfResult;
import com.xyy.wms.pda.bean.moveStorage.AddTaskBean;
import com.xyy.wms.pda.bean.moveStorage.LogicBean;
import com.xyy.wms.pda.bean.moveStorage.LogicReqBean;
import com.xyy.wms.pda.bean.moveStorage.PalletGoodsBean;
import com.xyy.wms.pda.bean.moveStorage.PalletOnPreviewResult;
import com.xyy.wms.pda.bean.moveStorage.StartTaskBean;
import com.xyy.wms.pda.bean.moveStorage.StartTaskResult;
import com.xyy.wms.pda.bean.productCode.ProductCode;
import com.xyy.wms.pda.contract.instorage.wholeshelf.WholeShelfListContract;
import com.xyy.wms.pda.model.productCode.GetProductCodeModel;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.List;

import io.reactivex.Observable;

/**
 * 整件列表上架
 */
public class WholeShelfListModel extends BaseModel implements WholeShelfListContract.IWholeShelfListModel ,GetProductCodeModel {

    public static WholeShelfListModel newInstance() {
        return new WholeShelfListModel();
    }

    /**
     * 代码的主要作用是调用API接口的getStorageOrder
     * @param containerCode
     * @param containerType
     * @return
     */
    @Override
    public Observable<BaseResponseBean<InShelfResult>> getWholeShelfList(String containerCode, int containerType) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).getStorageOrder(containerCode, containerType)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<StartTaskResult>> self(Number taskType, String palletNo) {
        return RetrofitCreateHelper.createApi(ApiService.class).self(new StartTaskBean(String.valueOf(taskType), palletNo))
            .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<LogicBean>> getShelfLogic(String palletNo) {
        return RetrofitCreateHelper.createApi(ApiService.class).getLogic(new LogicReqBean(palletNo))
            .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<List<PalletGoodsBean>>> getPalletGoods(String palletNo) {
        return  RetrofitCreateHelper.createApi(ApiService.class).getPalletGoods(new LogicReqBean(palletNo))
            .compose(RxHelper.rxSchedulerHelper());
    }


    public Observable<BaseResponseBean> addTaskDetail(AddTaskBean taskBean) {
        return RetrofitCreateHelper.createApi(ApiService.class).addTaskDetail(taskBean)
            .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean> commitTask(StartTaskResult taskBean) {
        return  RetrofitCreateHelper.createApi(ApiService.class).commitTask(taskBean)
            .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<RunningTaskResult>> getRunningTask() {
        return RetrofitCreateHelper.createApi(ApiService.class).getRunningTask()
            .compose(RxHelper.rxSchedulerHelper());
    }



    @Override
    public Observable<BaseResponseBean<List<ProductCode>>> getProductBarCode(String ownerCode, String packageBarCode) {
        return RetrofitCreateHelper.createApi(ApiService.class).selectProductPda(ownerCode, packageBarCode)
                .compose(RxHelper.rxSchedulerHelper());
    }

    //零货上架巷道预览
    @Override
    public Observable<BaseResponseBean<PalletOnPreviewResult>> getPalletOnPreview(String palletNo) {
        return RetrofitCreateHelper.createApi(ApiService.class).getPalletOnPreview(new LogicReqBean(palletNo))
            .compose(RxHelper.rxSchedulerHelper());
    }
}
