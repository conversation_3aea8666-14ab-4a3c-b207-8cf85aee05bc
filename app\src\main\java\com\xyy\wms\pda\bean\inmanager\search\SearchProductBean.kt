package com.xyy.wms.pda.bean.inmanager.search

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

/**
 * 查询商品信息
 */
@Parcelize
data class SearchProductBean(
  var batchNumberList: List<String>? = null,// 批号列表
  var commonName: String? = null,// 通用名
  var manufacturer: String? = null,// 生产厂家名称
  var productCode: String? = null,// 商品编码
  var productName: String? = null,// 商品名称
  var specifications: String? = null,// 规格
  var productBarCode: String? = null// 商品条码
) : Parcelable
