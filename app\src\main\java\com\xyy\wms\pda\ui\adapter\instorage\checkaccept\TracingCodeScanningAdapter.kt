package com.xyy.wms.pda.ui.adapter.instorage.checkaccept

import android.widget.Button
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.xyy.wms.pda.R
import com.xyy.wms.pda.bean.instorage.checkaccept.ConfirmDetailBean

class TracingCodeScanningAdapter(
    layout: Int,
    private val onDeleteClick: (position: Int) -> Unit = {}
) : BaseQuickAdapter<ConfirmDetailBean, BaseViewHolder>(layout) {
  override fun convert(helper: BaseViewHolder?, item: ConfirmDetailBean?) {
    val position = helper?.layoutPosition ?: 0
    helper?.setText(R.id.tv_index, (position + 1).toString())
    helper?.setText(R.id.tv_bar_code, item?.barcode)
    helper?.setText(R.id.tv_packaging_type, item?.barcodeTypeStr)
    helper?.setText(R.id.tv_count_hint3, item?.count.toString())
    helper?.getView<Button>(R.id.btn_delete)?.setOnClickListener {
      onDeleteClick(position)
      mData.removeAt(position)
      notifyDataSetChanged()
    }
  }
}

