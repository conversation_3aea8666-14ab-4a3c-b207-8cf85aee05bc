package com.xyy.wms.pda.model.pinback.unity

import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.api.ApiPinBackService
import com.xyy.wms.pda.api.ApiService
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.pinback.ProductBean
import com.xyy.wms.pda.bean.productCode.ProductCode
import com.xyy.wms.pda.model.pinback.common.UnityPieceListModel
import com.xyy.wms.pda.model.productCode.GetProductCodeModel
import com.xyy.wms.pda.net.RetrofitCreateHelper
import io.reactivex.Observable

class PinBackUnityShelfListModel : UnityPieceListModel, GetProductCodeModel {
    override fun getProductBarCode(ownerCode: String, packageBarCode: String): Observable<BaseResponseBean<MutableList<ProductCode>>> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).selectProductPda(ownerCode, packageBarCode)
                .compose(RxHelper.rxSchedulerHelper())
    }

    override fun getPieceShelfList(containerCode: String, type: Int): Observable<BaseResponseBean<List<ProductBean>>> {
        return RetrofitCreateHelper.createApi(ApiPinBackService::class.java).getPieceListByContainerCode(containerCode, type).compose(RxHelper.rxSchedulerHelper())
    }

}