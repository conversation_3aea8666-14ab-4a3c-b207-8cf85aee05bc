package com.xyy.wms.pda.bean.purchaseexit;

import java.io.Serializable;

/**
 * Created by lwj on 2020-02-24.
 * <EMAIL>
 */
public class PurchaseExitElectronicCodeBean implements Serializable {

    public String buildingCode;
    public String buildingName;
    public String createTime;
//    public String delivery;
//    public String deliveryMethod;
//    public String deliveryMode;
//    public String deliveryModeDesc;
//    public String deliveryPhone;
//    public String erpRefundOrderCode;
    public String id;
    public String orgCode;
    public String ownerCode;
//    public String ownerName;
    public String pickUpOrder;
    public String productType;
//    public String purchaseUser;
//    public String purchaseUserName;
//    public String recheckUser;
//    public String recheckUserName;
    public String refundOrderCode;
//    public String reviewRemark;
    public int scanStatus;

    public int getScanStatus() {
        return scanStatus;
    }

    public void setScanStatus(int scanStatus) {
        this.scanStatus = scanStatus;
    }

    //    public String storageAddress;
//    public String supplierCode;
    public String supplierName;


    public String getBuildingCode() {
        return buildingCode;
    }

    public void setBuildingCode(String buildingCode) {
        this.buildingCode = buildingCode;
    }

    public String getBuildingName() {
        return buildingName;
    }

    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getPickUpOrder() {
        return pickUpOrder;
    }

    public void setPickUpOrder(String pickUpOrder) {
        this.pickUpOrder = pickUpOrder;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getRefundOrderCode() {
        return refundOrderCode;
    }

    public void setRefundOrderCode(String refundOrderCode) {
        this.refundOrderCode = refundOrderCode;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }
}

