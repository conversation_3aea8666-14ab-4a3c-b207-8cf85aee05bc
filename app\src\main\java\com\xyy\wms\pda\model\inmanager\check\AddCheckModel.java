package com.xyy.wms.pda.model.inmanager.check;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.inmanager.check.AddCheckBean;
import com.xyy.wms.pda.bean.inmanager.check.CheckStorageAreaBean;
import com.xyy.wms.pda.contract.inmanager.check.AddCheckContract;
import com.xyy.wms.pda.model.ServiceModel;

import java.util.List;

import io.reactivex.Observable;

/**
 * Created by zcj on 2018/11/7 11
 */
public class AddCheckModel extends ServiceModel implements AddCheckContract.IAddCheckModel {

    public static AddCheckModel newInstance() {
        return new AddCheckModel();
    }

    @Override
    public Observable<BaseResponseBean> addWarehouseCheck(AddCheckBean bean) {
        return getApiInManagerService().addWarehouseCheck(bean).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<List<CheckStorageAreaBean>>> findStorageType() {
        return getApiInManagerService().findStorageType().compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<List<CheckStorageAreaBean>>> findStorageArea(String storageTypeId) {
        return getApiInManagerService().findStorageArea(storageTypeId).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<String>> getCheckNo() {
        return getApiInManagerService().getCheckNo().compose(RxHelper.rxSchedulerHelper());
    }
}
