package com.xyy.wms.pda.contract.instorage.checkaccept

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderDetailListBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderDetailListPost
import io.reactivex.Observable
import retrofit2.http.Body

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/4/11
 */
interface DetailsOfAcceptedGoodsContract {

    interface DetailsOfAcceptedGoodsContractModel : IBaseModel {
        fun getCheckOrderDetailList(checkOrderDetailListPost : CheckOrderDetailListPost) : Observable<BaseResponseBean<List<CheckOrderDetailListBean>>>
    }

    interface DetailsOfAcceptedGoodsContractView : IBaseActivity {
        fun getCheckOrderDetailListSuccess(checkOrderDetailListBean : BaseResponseBean<List<CheckOrderDetailListBean>>)
    }

}
