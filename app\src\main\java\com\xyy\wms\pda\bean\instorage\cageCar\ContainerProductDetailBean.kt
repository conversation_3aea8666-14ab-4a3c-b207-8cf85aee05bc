package com.xyy.wms.pda.bean.instorage.cageCar

import java.io.Serializable

data class ContainerProductDetailBean(
        val approvalStatus: Int?,//	审批状态	number	@mock=0
        val buildingCode: String?, //	建筑物编码	string	@mock=01
        val channelCode: String?, //	业务类型编码	string	@mock=2
        val containerCode: String?, //容器号	string	@mock=325045
        val createTime: String?, //创建时间	string	@mock=2020-02-27 12:25:35
        val createUser: String?, //	创建人id	string	@mock=4908
        val departCode: String?, //部门编号	string	@mock=JXG0103
        val erpStorageOrderCode: String?, //	回传erp入库单号	string	@mock=RKDD547202002270014
        val fromLineNumber: Int?,//	来源单据行号	number	@mock=2
        val id: Int?,//ID	number	@mock=10488
        val lineNumber: Int?,//	行号	number	@mock=2
        val lineNumberSplitOrigin: Int?,//	原始行号	number	@mock=2
        val onShelvesCount: Int?,//上架计数(agv上架用)	number	@mock=0
        val orgCode: String?, //	机构编码	string	@mock=D547
        val ownerCode: String?, //业主编号	string	@mock=009
        val pdaProductDetailLineId: String?,//pda商品列表明细页唯一标识	string	@mock=325045P6009008-1
        val pdaProductLineId: String?,//pda商品列表页唯一标识	string	@mock=325045P6009008
        val pickingPath: Int?,//	拣货路径（显示货位的）	number	@mock=1
        val producingArea: String?,//商品产地	string	@mock=苏州
        val productBatchCode: String?,//商品批号	string	@mock=-
        val productCode: String?,//商品编码	string	@mock=P6009008
        val productCountBig: Int?,//收货件数	number	@mock=0
        val productCountScatter: Int?,//收货零散数	number	@mock=2
        val productCountSmall: Int?,//	收货数量	number	@mock=2
        val productManufactureDate: String?,//	生产日期	string	@mock=2020-02-26
        val productName: String?,//商品名称	string
        val productPackingBigNumber: Int?,//	件包装数量	number	@mock=200
        val productPackingId: String?,//	商品件包装规格id	string	@mock=
        val productPackingMiddleNumber: Int?,//中包装数量	number	@mock=1
        val productSupervise: Int?,//追溯码采集描述	number	@mock=0
        val productTaxPrice: Double?=0.0,//商品单价	number	@mock=1
        val productType: Int?,//商品类型(0:普通药品;1:中药;2:器械;3:赠品;4:非药;5:二精;6:蛋肽;7:冷藏;8:冷	number	@mock=3
        val productValidDate: String?,//有效期至	string	@mock=2050-12-31
        val purchaseLineNumber: Int?,//采购订单行号	number	@mock=8
        val purchaseOrderCode: String?,//采购单号	string	@mock=CGD1911003245
        val purchaseUser: String?,//	采购员工id	string	@mock=2105
        val receiveLineNumber: Int?,//收货行号	number	@mock=2
        val rejectReasonSupplement: String?,//不合格事项	string	@mock=
        val rollContainerCode: String?,//笼车编码	string	@mock=LC0529
        val rollContainerLocationCode: String?,//笼车位置号	string	@mock=01
        val shelfReceiveTime: String?,//	上架领取时间	string	@mock=1970-01-01 08:01:00
        val shelfUser: String?,//上架员	string	@mock=
        val shelvesCountBig: Int?,//	上架件数	number	@mock=0
        val shelvesCountScatter: Int?,//	上架零散数	number	@mock=2
        val shelvesCountSmall: Int?,//上架数量	number	@mock=2
        val showPosition: String?,//	显示货位	string	@mock=
        val storageAreaCode: String?,////库区编号	string	@mock=
        val storageAreaType: String?,//	库区类型。默认1-普通，2-AGV	string	@mock=1
        val storageBhk: Int?, //是否入备货库(0:否;1:是)	number	@mock=0
        val storageClassification: String?,//存储分类(0 整散分开 1整散合一)	string	@mock=1
        val storageOrderCode: String?,//入库单单据编号	string	@mock=SJDD547202002270040
        val storageOrderStatus: Int?,//状态	number	@mock=1
        val storageRoomCode: String?,//库房编码	string	@mock=ZPK
        val storageTypeArea: String?,//上架区域	string	@mock=
        val storageTypeCode: String?,//库别编码	string	@mock=ZSHY
        val updateUser: String?,//更新人	string	@mock=4695
        val warehouseCode: String?,//	仓库编号	string	@mock=WMS360000_1
        val sterilizingBatchNumber:String?,// 灭菌批号
        val yn: Int?//是否删除	number	@mock=1
) : Serializable