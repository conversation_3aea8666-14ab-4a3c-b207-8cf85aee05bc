package com.xyy.wms.pda.bean.out

import java.io.Serializable

/**
 * 出库——外复核扫描标签条码
 */
class ReviewOutScanBean : Serializable {
    /**
     * 箱子类型 1-整箱 2-零货
     */
    var packageType: String? = null
    /**
     * 包装说明
     */
    var packageInstruction: String? = null
    /**
     * 商品名称
     */
    var productName: String? = null
    /**
     * 商品编号
     */
    var productCode: String? = null
    /**
     * 包装规格
     */
    var pieceNumber: String? = null
    /**
     * 批号
     */
    var batchNumber: String? = null
    /**
     * 规格
     */
    var specification: String? = null
    /**
     * 生产日期
     */
    var productionDate: Long = 0
    /**
     * 有效期至
     */
    var validUntil: Long = 0
    /**
     * 拣货货位
     */
    var soldOut: String? = null
    /**
     * 总件数
     */
    var totalNumber: String? = null
    /**
     * 已复核件数
     */
    var reviewsNumber: String? = null
    /**
     * 商品总数量（拼箱的展示）
     */
    var totalQuantityGoods: String? = null
    /**
     * 复核台
     */
    var reviewStage: String? = null
    /**
     * 是否是监管码商品 (1:是;0:否)
     */
    var productSupervise: String? = null
    /***
     * 外复核任务单
     */
    var taskOutReviewCode: String? = null
}
