package com.xyy.wms.pda.model.moveStorage.car;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiMoveStorageService;
import com.xyy.wms.pda.api.ApiService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.common.MoveStorageFinishedBean;
import com.xyy.wms.pda.bean.common.RunningTaskResult;
import com.xyy.wms.pda.bean.instorage.shelf.InShelfResult;
import com.xyy.wms.pda.bean.moveStorage.AddTaskCarDownDetailBean;
import com.xyy.wms.pda.bean.moveStorage.AddTaskDetailBean;
import com.xyy.wms.pda.bean.moveStorage.PalletGetGoodsBean;
import com.xyy.wms.pda.bean.moveStorage.PalletGetGoodsResult;
import com.xyy.wms.pda.bean.productCode.ProductCode;
import com.xyy.wms.pda.contract.movestorage.car.CarDownListContract;
import com.xyy.wms.pda.contract.movestorage.scattered.ScatteredDownListContract;
import com.xyy.wms.pda.contract.movestorage.wholeshelf.WholeShelfDownListContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.List;

import io.reactivex.Observable;

/**
 * 零货下架-列表
 */
public class CarDownListModel implements CarDownListContract.CarDownListModel{

    public static CarDownListModel newInstance() {
        return new CarDownListModel();
    }

    /**
     * 进行中任务
     */
    public Observable<BaseResponseBean<RunningTaskResult>> getRunningTask(){
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).getRunningTask()
            .compose(RxHelper.rxSchedulerHelper());
    }

    /**
     * 添加任务明细
     */
    public Observable<BaseResponseBean<Boolean>> addTaskDetail(AddTaskCarDownDetailBean addTaskDetailBean){
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).addTaskCarDownDetail(addTaskDetailBean)
            .compose(RxHelper.rxSchedulerHelper());
    }
    /**
     * 下架完成
     */
    public Observable<BaseResponseBean<Boolean>> setFinished(MoveStorageFinishedBean finishedBean){
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).setFinished(finishedBean)
            .compose(RxHelper.rxSchedulerHelper());
    }
}
