package com.xyy.wms.pda.contract.instorage.cagecar;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.cageCar.ScanCageCar;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 02/25/2020 11:58
 */
public interface ScanningCageCarContract {

    interface IScanningCageCarModel extends IBaseModel {
        Observable<BaseResponseBean<ScanCageCar>> getStorageContainersInfo(String containerCode, String rollContainerCode);
    }

    interface IScanningCageCarView extends IBaseActivity {
        void getStorageContainersInfoSuccess(BaseResponseBean<ScanCageCar> baseResponseBean);
    }

}
