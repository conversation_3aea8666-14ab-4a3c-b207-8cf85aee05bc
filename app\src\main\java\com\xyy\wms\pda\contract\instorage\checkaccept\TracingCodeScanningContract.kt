package com.xyy.wms.pda.contract.instorage.checkaccept

import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckPermissionBystaffNumBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckPermissionBystaffNumPost
import com.xyy.wms.pda.bean.instorage.checkaccept.ConfirmCodeScanOrderPost
import com.xyy.wms.pda.bean.instorage.checkaccept.FindPurchaseCodeScanOrderBean
import com.xyy.wms.pda.bean.instorage.checkaccept.FindPurchaseCodeScanOrderPost
import com.xyy.wms.pda.bean.instorage.checkaccept.UnlockCodeScanOrder
import io.reactivex.Observable
import com.xyy.utilslibrary.base.IBaseActivity

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/4/11
 */
interface TracingCodeScanningContract {

    interface TracingCodeScanningContractModel : IBaseModel {
        fun findPurchaseCodeScanOrder(findPurchaseCodeScanOrderPost : FindPurchaseCodeScanOrderPost) : Observable<BaseResponseBean<FindPurchaseCodeScanOrderBean>>
        fun confirmCodeScanOrder(confirmCodeScanOrderPost : ConfirmCodeScanOrderPost) : Observable<BaseResponseBean<String>>
        fun checkPermissionBystaffNum(checkPermissionBystaffNumPost : CheckPermissionBystaffNumPost) : Observable<BaseResponseBean<CheckPermissionBystaffNumBean>>
        fun unlockCodeScanOrder(unlockCodeScanOrder : UnlockCodeScanOrder) : Observable<BaseResponseBean<String>>
    }

    interface TracingCodeScanningContractView : IBaseActivity {
        fun findPurchaseCodeScanOrderSuccess(findPurchaseCodeScanOrderBean : BaseResponseBean<FindPurchaseCodeScanOrderBean>)
        fun confirmCodeScanOrderSuccess(str : BaseResponseBean<String>)
        fun checkPermissionBystaffNumSuccess(str : BaseResponseBean<CheckPermissionBystaffNumBean>)
        fun unlockCodeScanOrderSucces(str : BaseResponseBean<String>)
    }
}
