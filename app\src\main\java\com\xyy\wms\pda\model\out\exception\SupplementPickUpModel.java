package com.xyy.wms.pda.model.out.exception;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.utilslibrary.utils.JsonUtils;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.exception.FillPick;
import com.xyy.wms.pda.bean.exception.PickUseStorageBean;
import com.xyy.wms.pda.bean.exception.PickUseStorageBeanNew;
import com.xyy.wms.pda.contract.out.exception.SupplementPickUpContract;
import com.xyy.wms.pda.model.ServiceModel;

import java.util.List;

import io.reactivex.Observable;
/**
 * Created by XyyMvpPdaTemplate on 12/14/2019 11:54
 */
public class SupplementPickUpModel extends ServiceModel implements SupplementPickUpContract.ISupplementPickUpModel {

    public static SupplementPickUpModel newInstance() {
        return new SupplementPickUpModel();
    }

    @Override
    public Observable<BaseResponseBean<List<PickUseStorageBean>>> getExceptionHandleUseStorage(String id) {
        return getApiOutManagerService().getExceptionHandleUseStorage(id).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<BaseResponseBean>> commitPickUseStorage(PickUseStorageBeanNew pickUseStorageBean) {
        return getApiOutManagerService().commitPickUseStorage(pickUseStorageBean).compose(RxHelper.rxSchedulerHelper());
    }
}
