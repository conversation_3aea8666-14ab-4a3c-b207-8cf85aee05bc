package com.xyy.wms.pda.contract.login;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.user.LoginInfo;
import com.xyy.wms.pda.bean.req.LoginReq;

import io.reactivex.Observable;
public interface LoginContract {

    abstract class LoginPresenter extends BasePresenter<ILoginModel, ILoginView> {
        /**
         * 登录
         *
         */
        public abstract void login(LoginReq req);

    }

    interface ILoginModel extends IBaseModel {

        /**
         * 请求登录接口
         */
        Observable<BaseResponseBean<String>> login(LoginReq req);
    }

    interface ILoginView extends IBaseActivity {

        /**
         * 登录成功
         */
        void loginSuccess(String loginInfo);
    }
}
