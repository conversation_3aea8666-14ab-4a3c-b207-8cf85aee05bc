package com.xyy.wms.pda.net.req;

import com.google.gson.GsonBuilder;

import okhttp3.MediaType;
import okhttp3.RequestBody;

/**
 * Created by lwj on 2020/3/31
 * <EMAIL>
 */

public interface IJsonReq extends IPostReq {

    @Override
    default RequestBody getRequestBody() {
        return RequestBody.create(
                MediaType.parse("application/json; charset=UTF-8"),
                new GsonBuilder().serializeNulls().create().toJson(this)
        );
    }

}
