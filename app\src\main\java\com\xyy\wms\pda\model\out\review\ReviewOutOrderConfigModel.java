package com.xyy.wms.pda.model.out.review;

import androidx.annotation.NonNull;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiOutManagerService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.contract.out.review.ReviewOutOrderCommitContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import io.reactivex.Observable;
/**
 * 出库——外复核订单
 */
public class ReviewOutOrderConfigModel extends BaseModel implements ReviewOutOrderCommitContract.IReviewOutOrderModel {

    @NonNull
    public static ReviewOutOrderConfigModel newInstance() {
        return new ReviewOutOrderConfigModel();
    }
    @Override
    public Observable<BaseResponseBean> reviewOutConfirm(String orderCode, String buildingCode, String taskOutReviewCode) {
        return RetrofitCreateHelper.createApi(ApiOutManagerService.class).reviewOutConfirm(orderCode,buildingCode,taskOutReviewCode)
                .compose(RxHelper.rxSchedulerHelper());
    }
}
