<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:gravity="center_vertical"
    android:orientation="horizontal">
    <TextView
        android:id="@+id/tv_index"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:gravity="center_horizontal"
        android:layout_weight="0.5"
        android:textColor="@color/color_ff_102442"
        android:textSize="@dimen/sp14"
        tools:text="序号" />
    <TextView
        android:id="@+id/tv_bar_code"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:gravity="center_horizontal"
        android:layout_weight="1"
        android:textColor="@color/color_ff_102442"
        android:textSize="@dimen/sp14"
        tools:text="条码" />
    <TextView
        android:id="@+id/tv_packaging_type"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:gravity="center_horizontal"
        android:layout_weight="1"
        android:textColor="@color/color_ff_102442"
        android:textSize="@dimen/sp14"
        tools:text="包装类型" />
    <TextView
        android:id="@+id/tv_count_hint3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:gravity="center_horizontal"
        android:layout_weight="1"
        android:textColor="@color/color_ff_102442"
        android:textSize="@dimen/sp14"
        tools:text="数量" />
    <Button
        android:id="@+id/btn_delete"
        android:text="删除"
        android:layout_width="0dp"
        android:layout_height="30dp"
        android:layout_weight="0.5"
        android:textColor="@color/white"
        android:background="@drawable/btn_submit_bg"
        style="?android:attr/borderlessButtonStyle"
        android:gravity="center"/>
</LinearLayout>