package com.xyy.wms.pda.constant

import com.xyy.wms.pda.BuildConfig

/**
 * 全局唯一的常量管理类
 */
interface Const {

  /**
   * 路由常量
   */
  interface ARouterPath {
    companion object {
      // 首页
      const val mainActivity: String = "/app/mainActivity"

      // 登录页
      const val loginActivity: String = "/app/loginActivity"

      // 出库 —— 拆零捡货任务列表
      const val scatteredPickingListActivity: String = "/pick/scatteredPickingListActivity"

      // 出库 - 复核台
      const val reviewStageActivity: String = "/pick/reviewStageActivity"

      // 出库 - 补拣
      const val fillPickActivity: String = "/pick/fillPickActivity"

      // 出库 - 货位库存信息
      const val goodsStockActivity: String = "/pick/goodsStockActivity"

      // 库内 - 业务类型调整列表
      const val channelMoveListActivity = "/inside/channelMoveListActivity"

      // 库内 - 业务类型调整明细列表
      const val channelMoveInfoActivity = "/inside/channelMoveInfoActivity"

      // 库内 - 业务类型调整详情
      const val channelMoveDetailActivity = "/inside/channelMoveDetailActivity"

      // 出库 - 按箱外复核
      const val reviewOutBoxActivity = "/review/reviewOutBoxActivity"
      // 出库 - 按箱外复核列表
      const val reviewOutBoxListActivity = "/review/reviewOutBoxListActivity"
    }
  }

  /**
   * Code常量
   */
  interface CodeConstant {
    companion object {
      /**
       * 补货类型  1整件下架 2:零货上架
       */
      const val REPLENISHMENT_DOWN = 1
      const val REPLENISHMENT_UP = 2
      /**
       * 购进退出单 状态  2 未执行  4 执行中
       */
      const val PurchaseExitBill_UN_DO = 2
      const val PurchaseExitBill_DOING = 4

      // 整件上架
      const val PIN_BACK_WHOLE_SHELF_PRODUCT_RESULT = 107

      //零散上架
      const val PIN_BACK_SCATTERED_SHELF_PRODUCT_RESULT = 108

      // 整件上架 requestCode
      const val PIN_BACK_WHOLE_SHELF_PRODUCT_REQUEST = 109

      // 零散上架 requestCode
      const val PIN_BACK_SCATTERED_SHELF_PRODUCT_REQUEST = 1010

      //整散合一
      const val PIN_BACK_UNIT_SHELF_PRODUCT_REQUEST = 1011

      const val PIN_BACK_UNITY_SHELF_PRODUCT_RESULT = 1012

      // 整单完成
      const val PIN_BACK_UNITY_ALL_COMPLETE = 1013

      const val PIN_BACK_SCATTERED_ALL_COMPLETE = 1014

      const val PIN_BACK_WHOLE_ALL_COMPLETE = 1015
    }
  }

  /**
   * 全局常量
   */
  interface GlobalConstants {
    companion object {
      // 下载的安装包名称
      const val APK_NAME = "XyyPda.apk"

      // 清单文件中FileProvider的authorities
      const val AUTHORITY = BuildConfig.PROVIDER_NAME

      // 分页加载数量
      var PAGE_SIZE = 10
    }
  }

  /**
   * 库别常量
   */
  interface StorageType {
    companion object {
      /**
       * 整件库
       * 中药整库
       * 器械整库
       * 备货整库
       */
      const val STORAGE_TYPE_ZJK = "ZJK"
//      const val STORAGE_TYPE_ZYZ = "ZYZ"
//      const val STORAGE_TYPE_QXZ = "QXZ"
//      const val STORAGE_TYPE_BHK = "BHK"
    }
  }
}
