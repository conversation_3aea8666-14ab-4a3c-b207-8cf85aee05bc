<resources xmlns:tools="http://schemas.android.com/tools">

    <!-- Base application theme. -->
    <style name="AppTheme" parent="CompatAppTheme">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <!--优化冷启动-->
    <style name="FlashTheme" parent="AppTheme">
        <item name="android:windowBackground">@drawable/flash_bg</item>
    </style>

    <style name="AppTheme.ToolbarPopupOverlay" parent="ThemeOverlay.AppCompat.Light">
        <item name="overlapAnchor">false</item>
    </style>

    <!--不可编辑的布局样式-->
    <style name="item_layout_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/item_height_40dp</item>
        <item name="android:layout_marginLeft">@dimen/page_margin</item>
        <item name="android:layout_marginRight">@dimen/page_margin</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:orientation">horizontal</item>
    </style>

    <!--占一半的布局样式-->
    <style name="item_layout_style_half">
        <item name="android:gravity">center_vertical</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:minHeight">@dimen/gl_inner_item_height</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>

    </style>

    <!--文本样式1-->
    <style name="item_text_style_333">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/text_color_333333</item>
        <item name="android:textSize">@dimen/sample_text_size</item>
    </style>

    <style name="item_text_style_999">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/text_color_999999</item>
        <item name="android:textSize">@dimen/sample_text_size</item>
    </style>
    <!--文本样式2-->
    <style name="text_width_60">
        <item name="android:layout_width">60dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/text_color_333333</item>
        <item name="android:textSize">@dimen/sample_text_size</item>
    </style>
    <!--可编辑的文本样式-->
    <style name="item_edit_text_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">@dimen/dp5</item>
        <item name="android:layout_marginStart">@dimen/dp5</item>
        <item name="android:background">@null</item>
        <item name="android:textColor">@color/text_color_333333</item>
        <item name="android:textSize">@dimen/sp14</item>
    </style>

    <!--不可编辑的时间样式-->
    <style name="item_time_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/text_color_999999</item>
        <item name="android:textSize">@dimen/sp14</item>
    </style>

    <!--可编辑的时间样式-->
    <style name="item_edit_time_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/text_color_333333</item>
        <item name="android:textSize">@dimen/sp14</item>
    </style>

    <!--分割线的样式-->
    <style name="cut_off_line_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0.5dp</item>
        <item name="android:background">@color/ling_bg</item>
    </style>

    <style name="btn_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/dp40</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:clickable">true</item>
        <item name="android:textSize">@dimen/button_text_size</item>
        <item name="android:background">@drawable/btn_bg</item>
        <item name="android:stateListAnimator" tools:ignore="NewApi">@null</item>
    </style>

    <!--提交按钮样式(默认可点击)-->
    <style name="btn_submit_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/dp40</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:clickable">true</item>
        <item name="android:textSize">@dimen/button_text_size</item>
        <item name="android:background">@drawable/btn_submit_bg</item>
        <item name="android:stateListAnimator" tools:ignore="NewApi">@null</item>
    </style>

    <!--提交按钮样式(默认不可点击灰色背景)-->
    <style name="btn_unSubmit_style">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/dp40</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:clickable">false</item>
        <item name="android:textSize">@dimen/button_text_size</item>
        <item name="android:background">@drawable/btn_unsubmit_bg</item>
        <item name="android:stateListAnimator" tools:ignore="NewApi">@null</item><!--去掉阴影-->
    </style>

    <!--输入扫码结果-->
    <style name="et_scan_code_style">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
        <item name="android:ems">10</item>
        <item name="android:hint">@string/input_hint</item>
        <item name="android:textColorHint">@color/gray_ccc</item>
        <item name="android:background">@color/transparent</item>
        <item name="android:textColor">@color/text_color_333333</item>
        <item name="android:textSize">@dimen/small_text_size</item>
    </style>

    <!--输入货位条码-->
    <style name="et_goods_scan_code_style">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
        <item name="android:ems">10</item>
        <item name="android:textSize">@dimen/sample_text_size</item>
    </style>

    <!--解锁按钮样式-->
    <style name="btn_unlock_style">
        <item name="android:layout_margin">@dimen/dp5</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">@dimen/dp40</item>
        <item name="android:layout_weight">1</item>
        <item name="android:background">@drawable/btn_submit_bg</item>
    </style>

    <style name="style_review_out_tv">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/text_color_333333</item>
        <item name="android:textSize">@dimen/sample_text_size</item>
    </style>

    <style name="style_ll_item_layout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/item_height_40dp</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:background">@color/white</item>
        <item name="android:paddingLeft">@dimen/page_margin</item>
        <item name="android:paddingRight">@dimen/page_margin</item>
    </style>

    <style name="style_ll_item_layout.with_line">
        <item name="android:layout_marginTop">0.5dp</item>
    </style>

    <style name="style_whole_picking_left">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
        <item name="android:textColor">@color/text_color_333333</item>
        <item name="android:textSize">@dimen/sample_text_size</item>
    </style>

    <style name="style_tab_text">
        <item name="android:textSize">@dimen/sample_text_size</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="NoTitleDialog" parent="Theme.AppCompat.Light.Dialog">
        <!--没有标题-->
        <item name="windowNoTitle">true</item>
        <!--背景透明-->
        <item name="android:windowIsTranslucent">true</item>
        <!--是否有覆盖-->
        <item name="android:windowContentOverlay">@null</item>
        <!--是否浮动-->
        <item name="android:windowIsFloating">true</item>
        <!--边框-->
        <item name="android:windowFrame">@null</item>
        <!--背景：透明-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--dialog的整个屏幕的背景是否有遮障层-->
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
        <!--动画-->
        <!--<item name="android:windowAnimationStyle"> @style/AnimSlideBottom</item>-->
    </style>


    <style name="purchase_exit_electronic_code_key" parent="text_width_60">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:paddingTop">@dimen/dp8</item>
        <item name="android:paddingBottom">@dimen/dp8</item>
        <item name="android:paddingLeft">@dimen/page_margin</item>
    </style>

    <style name="purchase_exit_electronic_code_value" parent="text_width_60">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:paddingTop">@dimen/dp8</item>
        <item name="android:paddingBottom">@dimen/dp8</item>
    </style>

    <style name="purchase_exit_electronic_code_edit_value" parent="purchase_exit_electronic_code_value">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:background">@null</item>
        <item name="android:textColorHint">@color/gray_ccc</item>
    </style>

    <style name="purchase_exit_electronic_code_item">
        <item name="android:orientation">horizontal</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/item_height_40dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">@dimen/page_margin</item>
        <item name="android:paddingRight">@dimen/page_margin</item>
    </style>


    <style name="purchase_exit_electronic_code_item_title" parent="text_width_60">
        <item name="android:layout_width">wrap_content</item>
    </style>

    <style name="purchase_exit_electronic_code_item_title1" parent="purchase_exit_electronic_code_item_title">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="purchase_exit_electronic_code_item_title2" parent="purchase_exit_electronic_code_item_title">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="purchase_exit_electronic_code_item_title3" parent="purchase_exit_electronic_code_item_title">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:gravity">center_vertical</item>
    </style>


    <style name="fragment_purchase_exit_electronic_detail_key" parent="item_text_style_333">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:paddingTop">@dimen/dp8</item>
        <item name="android:paddingBottom">@dimen/dp8</item>
    </style>

    <style name="fragment_purchase_exit_electronic_detail_value" parent="fragment_purchase_exit_electronic_detail_key" />

    <style name="activity_dialog_style" parent="@style/AppTheme">
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:background">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:title">@null</item>
        <item name="android:dialogTitle">@null</item>
    </style>

    <style name="check_accept_product_key" parent="@style/check_accept_product_value" />

    <style name="check_accept_product_value">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/color_ff_333333</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingTop">@dimen/dp5</item>
        <item name="android:paddingBottom">@dimen/dp5</item>
    </style>


    <style name="check_accept_product_small_key" parent="@style/check_accept_product_small_value">

    </style>

    <style name="check_accept_product_small_value">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/color_ff_333333</item>
        <item name="android:paddingTop">@dimen/dp8</item>
        <item name="android:paddingBottom">@dimen/dp8</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
    </style>


    <style name="check_accept_product_bold_small_key" parent="check_accept_product_bold_small_key_base">
        <item name="android:ems">5</item>
    </style>

    <style name="check_accept_product_bold_small_key_base">
        <item name="android:paddingTop">@dimen/dp10</item>
        <item name="android:paddingBottom">@dimen/dp10</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/color_ff_333333</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="check_accept_product_bold_small_value">
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/color_ff_333333</item>
        <item name="android:textColorHint">@color/text_color_CCCCCC</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:layout_marginTop">@dimen/dp3</item>
        <item name="android:layout_marginBottom">@dimen/dp3</item>
        <item name="android:paddingLeft">@dimen/dp5</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:background">@drawable/bg_select_packing_type</item>
    </style>

    <style name="table_header_hint">
        <item name="android:gravity">center</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:minWidth">@dimen/dp80</item>
        <item name="android:textColor">@color/text_color_999999</item>
        <item name="android:textSize">@dimen/sp12</item>
    </style>

    <style name="table_item_hint" parent="table_header_hint">
        <item name="android:textColor">@color/text_color_000000</item>
    </style>

    <style name="style_gl_item">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:minHeight">@dimen/gl_inner_item_height</item>
        <item name="android:orientation">horizontal</item>
    </style>

    <!-- 全屏对话框样式 -->
    <style name="FullScreenDialog" parent="Theme.AppCompat.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:background">@android:color/transparent</item>
    </style>

</resources>
