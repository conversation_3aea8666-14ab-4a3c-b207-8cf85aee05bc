package com.xyy.wms.pda.contract.out.bind;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.out.bind.ConsolidationResult;
import com.xyy.wms.pda.bean.out.bind.ContainerCheckResult;

import java.util.Map;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 04/10/2019 14:59
 */
public interface BindCollectionAreaV2Contract {

    interface IBindCollectionAreaV2Model extends IBaseModel {
        //检验集货区
        Observable<BaseResponseBean<String>> checkStoreArea(Map<String, Object> params);
        //检验周转箱号
        Observable<BaseResponseBean<String>> checkBoxCode(Map<String, Object> params);
        // pda 由周转箱号获取集货信息
        Observable<BaseResponseBean<ConsolidationResult>> consolidationByBoxCode(Map<String, Object> params);
        // pda 绑定周转箱或者直接取货
        Observable<BaseResponseBean<String>> bindSubmit(Map<String, Object> params);
    }

    interface IBindCollectionAreaV2View extends IBaseActivity {
        void onCheckStoreAreaSuccess(BaseResponseBean<String> baseResponseBean);
        void onCheckStoreAreaFail(int code, String msg);

        void onCheckBoxCodeSuccess(BaseResponseBean<String> baseResponseBean);
        void onCheckBoxCodeFail(int code, String msg);

        void onConsolidationSuccess(BaseResponseBean<ConsolidationResult> baseResponseBean);
        void onConsolidationFail(int code, String msg);

        void onBindSuccess(BaseResponseBean<String> baseResponseBean);
        void onBindFail(int code, String msg);
    }
}
