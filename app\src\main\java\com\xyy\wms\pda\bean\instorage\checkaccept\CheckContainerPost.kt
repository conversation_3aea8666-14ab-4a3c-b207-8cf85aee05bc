package com.xyy.wms.pda.bean.instorage.checkaccept

import java.io.Serializable

class CheckContainerPost : Serializable {
    var checkResult: Int? = -1    //验收结论（1.合格，2.拒收，3待处理，4不合格）	number	@mock=1
    var containerCode: String? = null    //现在的容器编号	string	@mock=100117
    var lineNumber: Int? = -1    //行号（取原行号就行）	number	@mock=1
    var logContainerCode: String? = null    //原容器编号	string	@mock=100125
    var productCountBig: String? = null//	收货整件数	string	@mock=0
    var productCountScatter: String? = null    //收货零散数	string	@mock=10
    var productManufactureDate: String? = null//	生产日期	string	@mock=2018-01-10 00:00:00
    var productValidDate: String? = null//	有效期至	string	@mock=2020-12-31 00:00:00
    var storageClassification: String? = null    //存储分类 0 整散分开 1 整散合一	string	@mock=0
    var productBatchCode: String? = null //批号校验

}