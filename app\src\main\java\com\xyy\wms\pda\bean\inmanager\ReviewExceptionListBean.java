package com.xyy.wms.pda.bean.inmanager;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zcj on 2018/11/14 10
 */
public class ReviewExceptionListBean implements Serializable {
    /**
     * endRow		                     number	@mock=1
     * firstPage		                 number	@mock=1
     * hasNextPage		                 boolean	@mock=false
     * hasPreviousPage	                 boolean	@mock=false
     * isFirstPage		                 boolean	@mock=true
     * isLastPage		                 boolean	@mock=true
     * lastPage		                     number	@mock=1
     * list	                结果集	     array<object>
     * navigateFirstPage		         number	@mock=1
     * navigateLastPage 		         number	@mock=1
     * navigatePages	    	         number	@mock=8
     * navigatepageNums		             array<number>	@mock=1
     * nextPage		                     number	@mock=0
     * pageNum	            当前页	     number	@mock=1
     * pageSize	            每页的数量	 number	@mock=2
     * pages		                     number	@mock=1
     * prePage		                     number	@mock=0
     * size	                当前页的数量	 number	@mock=2
     * startRow		                     number	@mock=0
     * total		                     number	@mock=2
     */

    private String endRow;
    private String firstPage;
    private boolean hasNextPage;
    private boolean hasPreviousPage;
    private boolean isFirstPage;
    private boolean isLastPage;
    private String lastPage;
    private List<ReviewExceptionList> list;
    private String navigateFirstPage;
    private String navigateLastPage;
    private String navigatePages;
    private List<Integer> navigatepageNums;
    private String nextPage;
    private String pageNum;
    private String pageSize;
    private String pages;
    private String prePage;
    private String size;
    private String startRow;
    private String total;

    public static class ReviewExceptionList implements Serializable {
        /**
         * allocationCode	分配单	string
         * batchNumber	批号	string
         * exceptionCause	异常原因 （1.少货 2.多货）	number
         * exceptionNumber	异常数量	number
         * exceptionType	异常类型 (1.零货拣货 2.内复核)	number
         * goodsAllocation	货位编码	string
         * id	异常表主键	number
         * manufacturer	生产厂家	string
         * orderCode	订单号	string
         * passBoxCode	周转箱号	string
         * produceTime	生产日期	string
         * productCode	商品编码	string
         * productName	产品名称	string
         * remark	备注	string
         * specifications	规格	string
         * status	处理状态 1 异常 2 异常处理完成	number
         * validDate	有效期	string
         * dealMode   处理方式
         */

        private String allocationCode;
        private String batchNumber;
        private String exceptionCause;
        private String exceptionNumber;
        private String exceptionType;
        private String goodsAllocation;
        private String id;
        private String manufacturer;
        private String orderCode;
        private String passBoxCode;
        private String produceTime;
        private String productCode;
        private String productName;
        private String remark;
        private String specifications;
        private String validDate;
        private String status;
        private String dealMode;

        public String getDealMode() {
            return dealMode;
        }

        public void setDealMode(String dealMode) {
            this.dealMode = dealMode;
        }

        public String getAllocationCode() {
            return allocationCode;
        }

        public void setAllocationCode(String allocationCode) {
            this.allocationCode = allocationCode;
        }

        public String getBatchNumber() {
            return batchNumber;
        }

        public void setBatchNumber(String batchNumber) {
            this.batchNumber = batchNumber;
        }

        public String getExceptionCause() {
            return exceptionCause;
        }

        public void setExceptionCause(String exceptionCause) {
            this.exceptionCause = exceptionCause;
        }

        public String getExceptionNumber() {
            return exceptionNumber;
        }

        public void setExceptionNumber(String exceptionNumber) {
            this.exceptionNumber = exceptionNumber;
        }

        public String getExceptionType() {
            return exceptionType;
        }

        public void setExceptionType(String exceptionType) {
            this.exceptionType = exceptionType;
        }

        public String getGoodsAllocation() {
            return goodsAllocation;
        }

        public void setGoodsAllocation(String goodsAllocation) {
            this.goodsAllocation = goodsAllocation;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getManufacturer() {
            return manufacturer;
        }

        public void setManufacturer(String manufacturer) {
            this.manufacturer = manufacturer;
        }

        public String getOrderCode() {
            return orderCode;
        }

        public void setOrderCode(String orderCode) {
            this.orderCode = orderCode;
        }

        public String getPassBoxCode() {
            return passBoxCode;
        }

        public void setPassBoxCode(String passBoxCode) {
            this.passBoxCode = passBoxCode;
        }

        public String getProduceTime() {
            return produceTime;
        }

        public void setProduceTime(String produceTime) {
            this.produceTime = produceTime;
        }

        public String getProductCode() {
            return productCode;
        }

        public void setProductCode(String productCode) {
            this.productCode = productCode;
        }

        public String getProductName() {
            return productName;
        }

        public void setProductName(String productName) {
            this.productName = productName;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getSpecifications() {
            return specifications;
        }

        public void setSpecifications(String specifications) {
            this.specifications = specifications;
        }

        public String getValidDate() {
            return validDate;
        }

        public void setValidDate(String validDate) {
            this.validDate = validDate;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        @Override
        public String toString() {
            return "ReviewExceptionList{" +
                    "allocationCode='" + allocationCode + '\'' +
                    ", batchNumber='" + batchNumber + '\'' +
                    ", exceptionCause='" + exceptionCause + '\'' +
                    ", exceptionNumber='" + exceptionNumber + '\'' +
                    ", exceptionType='" + exceptionType + '\'' +
                    ", goodsAllocation='" + goodsAllocation + '\'' +
                    ", id='" + id + '\'' +
                    ", manufacturer='" + manufacturer + '\'' +
                    ", orderCode='" + orderCode + '\'' +
                    ", passBoxCode='" + passBoxCode + '\'' +
                    ", produceTime='" + produceTime + '\'' +
                    ", productCode='" + productCode + '\'' +
                    ", productName='" + productName + '\'' +
                    ", remark='" + remark + '\'' +
                    ", specifications='" + specifications + '\'' +
                    ", validDate='" + validDate + '\'' +
                    ", status='" + status + '\'' +
                    ", dealMode='" + dealMode + '\'' +
                    '}';
        }
    }

    public String getEndRow() {
        return endRow;
    }

    public void setEndRow(String endRow) {
        this.endRow = endRow;
    }

    public String getFirstPage() {
        return firstPage;
    }

    public void setFirstPage(String firstPage) {
        this.firstPage = firstPage;
    }

    public boolean isHasNextPage() {
        return hasNextPage;
    }

    public void setHasNextPage(boolean hasNextPage) {
        this.hasNextPage = hasNextPage;
    }

    public boolean isHasPreviousPage() {
        return hasPreviousPage;
    }

    public void setHasPreviousPage(boolean hasPreviousPage) {
        this.hasPreviousPage = hasPreviousPage;
    }

    public boolean isFirstPage() {
        return isFirstPage;
    }

    public void setFirstPage(boolean firstPage) {
        isFirstPage = firstPage;
    }

    public boolean isLastPage() {
        return isLastPage;
    }

    public void setLastPage(boolean lastPage) {
        isLastPage = lastPage;
    }

    public String getLastPage() {
        return lastPage;
    }

    public void setLastPage(String lastPage) {
        this.lastPage = lastPage;
    }

    public List<ReviewExceptionList> getList() {
        return list;
    }

    public void setList(List<ReviewExceptionList> list) {
        this.list = list;
    }

    public String getNavigateFirstPage() {
        return navigateFirstPage;
    }

    public void setNavigateFirstPage(String navigateFirstPage) {
        this.navigateFirstPage = navigateFirstPage;
    }

    public String getNavigateLastPage() {
        return navigateLastPage;
    }

    public void setNavigateLastPage(String navigateLastPage) {
        this.navigateLastPage = navigateLastPage;
    }

    public String getNavigatePages() {
        return navigatePages;
    }

    public void setNavigatePages(String navigatePages) {
        this.navigatePages = navigatePages;
    }

    public List<Integer> getNavigatepageNums() {
        return navigatepageNums;
    }

    public void setNavigatepageNums(List<Integer> navigatepageNums) {
        this.navigatepageNums = navigatepageNums;
    }

    public String getNextPage() {
        return nextPage;
    }

    public void setNextPage(String nextPage) {
        this.nextPage = nextPage;
    }

    public String getPageNum() {
        return pageNum;
    }

    public void setPageNum(String pageNum) {
        this.pageNum = pageNum;
    }

    public String getPageSize() {
        return pageSize;
    }

    public void setPageSize(String pageSize) {
        this.pageSize = pageSize;
    }

    public String getPages() {
        return pages;
    }

    public void setPages(String pages) {
        this.pages = pages;
    }

    public String getPrePage() {
        return prePage;
    }

    public void setPrePage(String prePage) {
        this.prePage = prePage;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getStartRow() {
        return startRow;
    }

    public void setStartRow(String startRow) {
        this.startRow = startRow;
    }

    public String getTotal() {
        return total;
    }

    public void setTotal(String total) {
        this.total = total;
    }

    @Override
    public String toString() {
        return "ReviewExceptionListBean{" +
                "endRow='" + endRow + '\'' +
                ", firstPage='" + firstPage + '\'' +
                ", hasNextPage=" + hasNextPage +
                ", hasPreviousPage=" + hasPreviousPage +
                ", isFirstPage=" + isFirstPage +
                ", isLastPage=" + isLastPage +
                ", lastPage='" + lastPage + '\'' +
                ", list=" + list +
                ", navigateFirstPage='" + navigateFirstPage + '\'' +
                ", navigateLastPage='" + navigateLastPage + '\'' +
                ", navigatePages='" + navigatePages + '\'' +
                ", navigatepageNums=" + navigatepageNums +
                ", nextPage='" + nextPage + '\'' +
                ", pageNum='" + pageNum + '\'' +
                ", pageSize='" + pageSize + '\'' +
                ", pages='" + pages + '\'' +
                ", prePage='" + prePage + '\'' +
                ", size='" + size + '\'' +
                ", startRow='" + startRow + '\'' +
                ", total='" + total + '\'' +
                '}';
    }
}
