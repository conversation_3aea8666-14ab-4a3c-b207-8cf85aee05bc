package com.xyy.wms.pda.bean.inmanager.check;

/**
 * Created by zcj on 2018/11/21 11
 */
public class CheckOverFlowingBean {
    private String id;
    private String name;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "CheckOverFlowingBean{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                '}';
    }
}
