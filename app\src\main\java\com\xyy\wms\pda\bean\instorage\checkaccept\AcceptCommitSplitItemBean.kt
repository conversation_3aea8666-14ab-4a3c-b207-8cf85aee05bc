package com.xyy.wms.pda.bean.instorage.checkaccept

import java.io.Serializable

/**
 *  created by  liang<PERSON>  on 2020-03-17.
 */
class AcceptCommitSplitItemBean : Serializable {
    var checkOrderCode: String? = null //验收单号	string	@mock=$order('YSD202003030071','YSD202003030071')
    var checkRemark: String? = null    //验收备注	string	@mock=$order('验收备注1','验收备注2')
    var checkResult: Int? = -1    //验收结论	number	@mock=$order(1,2)
    var containerCode: String? = null    //	容器编号	string	@mock=$order('100125','100125')
    var lineNumber: Int? = -1    //行号	number	@mock=$order(1,1)
    var logContainerCode: String? = null    //	原容器编号	string	@mock=$order('','')
    var productCountBig: String? = null    //收货整件数	string	@mock=$order('0','0')
    var productCountScatter: String? = null    //收货零散数	string	@mock=$order('4','6')
    var productCountSmall: String? = null    //	收货数量（收货件数*件包装规格+收货零散数）	string	@mock=$order('4','6')
    var productManufactureDate: String? = null    //	生产日期	string	@mock=$order('','')
    var productValidDate: String? = null    //有效期至	string	@mock=$order('','')
    var rejectReasonSupplement: String? = null
    var storageClassification: String? = null
    var productBatchCode: String? = null
    var sortNumber: Int? = -1//明细排序后的序号，提示错误行用	number	@mock=$order(2,2)
    var treatmentMeasures: Int? = -1//处理措施1.入合格库2.拒收 3.复查 4.入不合格库 5.入退货库

}