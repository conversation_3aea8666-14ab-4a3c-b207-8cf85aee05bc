package com.xyy.wms.pda.bean.instorage.checkaccept

import java.io.Serializable

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/14
 */
data class CheckOrderSavePost (
    var checkOrderCode : String,                       // 验收单号
    var supplierCode : String,                         // 供应商编码
    var productCode : String,                          // 商品编号
    var productBatchCode : String,                     // 批号
    var productManufactureDate : String,               // 生产日期
    var productValidDate : String,                     // 有效期至
    var productPackingBigNumber : Int,                 // 件包装数量
    var ownerCode : String,                            // 业主
    var buildingCode : String,                         // 建筑物
    var channelCode : String,                          // 渠道
    var sterilizingBatchNumber : String,               // 灭菌批号
    var productPackingMiddleNumber : Int,              // 中包装数量
    var productCountBig : String,                      // 收货件数
    var productCountScatter : String,                  // 收货零散数
    var checkResult : Int,                             // 验收结果(1:合格;2:拒收;3:待处理;4:不合格)
    var treatmentMeasures : Int,                       // 处理措施（1.入合格库2.拒收 3.复查 4.入不合格库 5.入退货库）
    var checkRemark : String,                          // 验收备注
    var productType : Int,                             // 商品类型(0:普通药品;1:中药;2:器械;3:赠品;4:非药;5:二精;6:蛋肽;7:冷藏;8:冷冻)
    var containerCode : String,                        // 容器编号
    var imgUrls : List<String>,                        // 异常图片url
    var rejectReasonSupplement : String                // 不合格事项
) : Serializable
