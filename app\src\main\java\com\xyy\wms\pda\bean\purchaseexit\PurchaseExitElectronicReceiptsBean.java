package com.xyy.wms.pda.bean.purchaseexit;

import java.io.Serializable;

/**
 * Created by lwj on 2020-02-24.
 * <EMAIL>
 */
public class PurchaseExitElectronicReceiptsBean implements Serializable {
    public int actualRefundCount;//   数量 需扫描数量    number    @mock=$order(32322)
    public String actualScatteredCount;//       number    @mock =$order(32322)
    public String actualWholeCount;//       number    @mock =$order(00000)
    public String approvalNumbers;//       string    @mock =$order('国药准字H20065203''国药准字H20065203''国药准字H20065203''国药准字Z20020112''国药准字Z20020112')
    public String beginTime;//
    public String buildingCode;//     string    @mock =$order('''''''''')
    public String buildingName;//
    public String channelCode;//      string    @mock =$order('''''''''')
    public String channelName;//
    public String codeLevel;//
    public String commonName;//     string    @mock =$order('阿莫西林颗粒''阿莫西林颗粒''阿莫西林颗粒''养血安神片''养血安神片')
    public String containerCode;//       string    @mock =$order('325030''325030''325030''325030''325030')
    public String createTime;//
    public String createUser;//
    public String deliveryMethod;//
    public String djSort;//
    public String dosageForm;//     string    @mock =$order('4''4''4''1''1')
    public String endTime;//
    public String erpRefundOrderCode;//
    public String fileds;//
    public String headers;//
    public String id;//  number    @mock =$order(613614615616617)
    public String largeCategory;//       string    @mock =$order('内服''内服''内服''内服''内服')
    public int largePackingNumber;//  件包装规格    string    @mock =$order('1''1''1''2''2')
    public String lineNumber;//    number    @mock =$order(12345)
    public String manufacturer;//  厂家名称    string    @mock =$order('四川依科制药有限公司''四川依科制药有限公司''四川依科制药有限公司''四川依科制药有限公司''四川依科制药有限公司')
    public String marketAuthor;//     string    @mock =$order('上市许可持有人''上市许可持有人''上市许可持有人''上市许可持有人''上市许可持有人')
    public String mediumPackageBarCode;//
    public int middlePackingNumber;//   中包装规格
    public String orderType;//
    public String orgCode;//
    public String ownerCode;//
    public String packingUnit;// 规 格    string    @mock =$order('盒''盒''盒''瓶''瓶')
    public String pickUpOrder;//
    public String piecePackageBarCode;//
    public String producingArea;//   string    @mock =$order('四川''四川''四川''四川''四川')
    public String productBatchCode;//  批号    string    @mock =$order('1901121''1901121''1901121''190302''190302')
    public String productCode;// 商品编码    string    @mock =$order('********''********''********''********''********')
    public String productCodeList;//
    public String productContainTaxPrice;//        string    @mock =$order('1.90''1.90''1.90''1.80''1.80')
    public String productName;// 商品名称    string    @mock =$order('阿莫西林颗粒''阿莫西林颗粒''阿莫西林颗粒''养血安神片''养血安神片')
    public String productProduceDate;//     string    @mock =$order('2019-01-25''2019-01-25''2019-01-25''2019-03-02''2019-03-02')
    public String productType;//    number    @mock =$order(00000)
    public String productValidDate;//      string    @mock =$order('2020-12-03''2020-12-03''2020-12-03''2021-02-28''2021-02-28')
    public String purchaseUser;//
    public String recheckName;//
    public String recheckTime;//
    public String recheckUser;//
    public String recheckUserName;//
    public String refundAlwaysCount;//      number    @mock =$order(00000)
    public String refundCount;//    number    @mock =$order(33322)
    public String refundCountPlan;//       number    @mock =$order(300300300359359)
    public String refundOrderCode;//       string    @mock =$order('GTR1907200002''GTR1907200002''GTR1907200002''GTR1907200002''GTR1907200002')
    public String refundOrderStatus;//
    public String refundOrderStatusDesc;//
    public String refundScatteredCount;//    number    @mock =$order(33322)
    public String refundType;//  string    @mock =$order('1''1''1''1''1')
    public String refundTypeDesc;//
    public String refundWholeCount;//      number    @mock =$order(00000)
    public String regulatoryCode;//
    public int scanStatus;// 状态
    public int scannedNumber;//   已扫描条目数/已扫描次数
    public int scannedNumberLarge;//   已扫描件包装数
    public int scannedNumberMiddle;// 已扫描中包装数
    public int scannedNumberSmall;//  已扫描小包装数
    public int scannedNumberTotal;//  已扫描总数量
    public String secondRecheckUser;//
    public String shelfGoodsAmount;//      number    @mock =$order(160160160117117)
    public String shelfLocationCode;//       string    @mock =$order('LHK01-020305''LHK01-020305''LHK01-020305''LHK01-020201''LHK01-020201')
    public String shelfName;//
    public String shelfTime;//
    public String shelfUser;//
    public String shelfUserName;//
    public String smallPackageBarCode;//
    public String specifications;//    string    @mock =$order('0.125g*12袋''0.125g*12袋''0.125g*12袋''0.25g*100s 糖衣''0.25g*100s 糖衣')
    public String storageAreaCode;//
    public String storageRoomCode;//       string    @mock =$order('''''''''')
    public String storageRoomName;//       string    @mock =$order('''''''''')
    public String storageType;//   string    @mock =$order('3''3''3''3''3')
    public String storageTypeCode;//     string    @mock =$order('THK''THK''THK''THK''THK')
    public String storageTypeName;//      string    @mock =$order('''''''''')
    public String storeCode;//   string    @mock =$order('THK''THK''THK''THK''THK')
    public String storeName;//
    public String supplierCode;//    string    @mock =$order('GPFJIX052002''GPFJIX052002''GPFJIX052002''GPFJIX052002''GPFJIX052002')
    public String supplierCodeList;//
    public String supplierName;//      string    @mock =$order('江西九州通药业有限公司''江西九州通药业有限公司''江西九州通药业有限公司''江西九州通药业有限公司''江西九州通药业有限公司')
    public String updateName;//
    public String updateTime;//
    public String updateUser;//
    public String warehouseCode;//
    public String whetherRegulatory;//      string    @mock =$order('1''1''1''1''1')

    public int status;//1:已被复核;2:追溯码;3:复核信息补充

    public int getActualRefundCount() {
        return actualRefundCount;
    }

    public void setActualRefundCount(int actualRefundCount) {
        this.actualRefundCount = actualRefundCount;
    }

    public String getActualScatteredCount() {
        return actualScatteredCount;
    }

    public void setActualScatteredCount(String actualScatteredCount) {
        this.actualScatteredCount = actualScatteredCount;
    }

    public String getActualWholeCount() {
        return actualWholeCount;
    }

    public void setActualWholeCount(String actualWholeCount) {
        this.actualWholeCount = actualWholeCount;
    }

    public String getApprovalNumbers() {
        return approvalNumbers;
    }

    public void setApprovalNumbers(String approvalNumbers) {
        this.approvalNumbers = approvalNumbers;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getBuildingCode() {
        return buildingCode;
    }

    public void setBuildingCode(String buildingCode) {
        this.buildingCode = buildingCode;
    }

    public String getBuildingName() {
        return buildingName;
    }

    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getCodeLevel() {
        return codeLevel;
    }

    public void setCodeLevel(String codeLevel) {
        this.codeLevel = codeLevel;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public void setContainerCode(String containerCode) {
        this.containerCode = containerCode;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getDeliveryMethod() {
        return deliveryMethod;
    }

    public void setDeliveryMethod(String deliveryMethod) {
        this.deliveryMethod = deliveryMethod;
    }

    public String getDjSort() {
        return djSort;
    }

    public void setDjSort(String djSort) {
        this.djSort = djSort;
    }

    public String getDosageForm() {
        return dosageForm;
    }

    public void setDosageForm(String dosageForm) {
        this.dosageForm = dosageForm;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getErpRefundOrderCode() {
        return erpRefundOrderCode;
    }

    public void setErpRefundOrderCode(String erpRefundOrderCode) {
        this.erpRefundOrderCode = erpRefundOrderCode;
    }

    public String getFileds() {
        return fileds;
    }

    public void setFileds(String fileds) {
        this.fileds = fileds;
    }

    public String getHeaders() {
        return headers;
    }

    public void setHeaders(String headers) {
        this.headers = headers;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLargeCategory() {
        return largeCategory;
    }

    public void setLargeCategory(String largeCategory) {
        this.largeCategory = largeCategory;
    }

    public int getLargePackingNumber() {
        return largePackingNumber;
    }

    public void setLargePackingNumber(int largePackingNumber) {
        this.largePackingNumber = largePackingNumber;
    }

    public String getLineNumber() {
        return lineNumber;
    }

    public void setLineNumber(String lineNumber) {
        this.lineNumber = lineNumber;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getMarketAuthor() {
        return marketAuthor;
    }

    public void setMarketAuthor(String marketAuthor) {
        this.marketAuthor = marketAuthor;
    }

    public String getMediumPackageBarCode() {
        return mediumPackageBarCode;
    }

    public void setMediumPackageBarCode(String mediumPackageBarCode) {
        this.mediumPackageBarCode = mediumPackageBarCode;
    }

    public int getMiddlePackingNumber() {
        return middlePackingNumber;
    }


    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getPackingUnit() {
        return packingUnit;
    }

    public void setPackingUnit(String packingUnit) {
        this.packingUnit = packingUnit;
    }

    public String getPickUpOrder() {
        return pickUpOrder;
    }

    public void setPickUpOrder(String pickUpOrder) {
        this.pickUpOrder = pickUpOrder;
    }

    public String getPiecePackageBarCode() {
        return piecePackageBarCode;
    }

    public void setPiecePackageBarCode(String piecePackageBarCode) {
        this.piecePackageBarCode = piecePackageBarCode;
    }

    public String getProducingArea() {
        return producingArea;
    }

    public void setProducingArea(String producingArea) {
        this.producingArea = producingArea;
    }

    public String getProductBatchCode() {
        return productBatchCode;
    }

    public void setProductBatchCode(String productBatchCode) {
        this.productBatchCode = productBatchCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductCodeList() {
        return productCodeList;
    }

    public void setProductCodeList(String productCodeList) {
        this.productCodeList = productCodeList;
    }

    public String getProductContainTaxPrice() {
        return productContainTaxPrice;
    }

    public void setProductContainTaxPrice(String productContainTaxPrice) {
        this.productContainTaxPrice = productContainTaxPrice;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductProduceDate() {
        return productProduceDate;
    }

    public void setProductProduceDate(String productProduceDate) {
        this.productProduceDate = productProduceDate;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getProductValidDate() {
        return productValidDate;
    }

    public void setProductValidDate(String productValidDate) {
        this.productValidDate = productValidDate;
    }

    public String getPurchaseUser() {
        return purchaseUser;
    }

    public void setPurchaseUser(String purchaseUser) {
        this.purchaseUser = purchaseUser;
    }

    public String getRecheckName() {
        return recheckName;
    }

    public void setRecheckName(String recheckName) {
        this.recheckName = recheckName;
    }

    public String getRecheckTime() {
        return recheckTime;
    }

    public void setRecheckTime(String recheckTime) {
        this.recheckTime = recheckTime;
    }

    public String getRecheckUser() {
        return recheckUser;
    }

    public void setRecheckUser(String recheckUser) {
        this.recheckUser = recheckUser;
    }

    public String getRecheckUserName() {
        return recheckUserName;
    }

    public void setRecheckUserName(String recheckUserName) {
        this.recheckUserName = recheckUserName;
    }

    public String getRefundAlwaysCount() {
        return refundAlwaysCount;
    }

    public void setRefundAlwaysCount(String refundAlwaysCount) {
        this.refundAlwaysCount = refundAlwaysCount;
    }

    public String getRefundCount() {
        return refundCount;
    }

    public void setRefundCount(String refundCount) {
        this.refundCount = refundCount;
    }

    public String getRefundCountPlan() {
        return refundCountPlan;
    }

    public void setRefundCountPlan(String refundCountPlan) {
        this.refundCountPlan = refundCountPlan;
    }

    public String getRefundOrderCode() {
        return refundOrderCode;
    }

    public void setRefundOrderCode(String refundOrderCode) {
        this.refundOrderCode = refundOrderCode;
    }

    public String getRefundOrderStatus() {
        return refundOrderStatus;
    }

    public void setRefundOrderStatus(String refundOrderStatus) {
        this.refundOrderStatus = refundOrderStatus;
    }

    public String getRefundOrderStatusDesc() {
        return refundOrderStatusDesc;
    }

    public void setRefundOrderStatusDesc(String refundOrderStatusDesc) {
        this.refundOrderStatusDesc = refundOrderStatusDesc;
    }

    public String getRefundScatteredCount() {
        return refundScatteredCount;
    }

    public void setRefundScatteredCount(String refundScatteredCount) {
        this.refundScatteredCount = refundScatteredCount;
    }

    public String getRefundType() {
        return refundType;
    }

    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }

    public String getRefundTypeDesc() {
        return refundTypeDesc;
    }

    public void setRefundTypeDesc(String refundTypeDesc) {
        this.refundTypeDesc = refundTypeDesc;
    }

    public String getRefundWholeCount() {
        return refundWholeCount;
    }

    public void setRefundWholeCount(String refundWholeCount) {
        this.refundWholeCount = refundWholeCount;
    }

    public String getRegulatoryCode() {
        return regulatoryCode;
    }

    public void setRegulatoryCode(String regulatoryCode) {
        this.regulatoryCode = regulatoryCode;
    }

    public int getScanStatus() {
        return scanStatus;
    }

    public void setScanStatus(int scanStatus) {
        this.scanStatus = scanStatus;
    }


    public int getScannedNumberLarge() {
        return scannedNumberLarge;
    }

    public void setScannedNumberLarge(int scannedNumberLarge) {
        this.scannedNumberLarge = scannedNumberLarge;
    }

    public void setMiddlePackingNumber(int middlePackingNumber) {
        this.middlePackingNumber = middlePackingNumber;
    }

    public int getScannedNumber() {
        return scannedNumber;
    }

    public void setScannedNumber(int scannedNumber) {
        this.scannedNumber = scannedNumber;
    }

    public int getScannedNumberMiddle() {
        return scannedNumberMiddle;
    }

    public void setScannedNumberMiddle(int scannedNumberMiddle) {
        this.scannedNumberMiddle = scannedNumberMiddle;
    }

    public int getScannedNumberSmall() {
        return scannedNumberSmall;
    }

    public void setScannedNumberSmall(int scannedNumberSmall) {
        this.scannedNumberSmall = scannedNumberSmall;
    }

    public int getScannedNumberTotal() {
        return scannedNumberTotal;
    }

    public void setScannedNumberTotal(int scannedNumberTotal) {
        this.scannedNumberTotal = scannedNumberTotal;
    }

    public String getSecondRecheckUser() {
        return secondRecheckUser;
    }

    public void setSecondRecheckUser(String secondRecheckUser) {
        this.secondRecheckUser = secondRecheckUser;
    }

    public String getShelfGoodsAmount() {
        return shelfGoodsAmount;
    }

    public void setShelfGoodsAmount(String shelfGoodsAmount) {
        this.shelfGoodsAmount = shelfGoodsAmount;
    }

    public String getShelfLocationCode() {
        return shelfLocationCode;
    }

    public void setShelfLocationCode(String shelfLocationCode) {
        this.shelfLocationCode = shelfLocationCode;
    }

    public String getShelfName() {
        return shelfName;
    }

    public void setShelfName(String shelfName) {
        this.shelfName = shelfName;
    }

    public String getShelfTime() {
        return shelfTime;
    }

    public void setShelfTime(String shelfTime) {
        this.shelfTime = shelfTime;
    }

    public String getShelfUser() {
        return shelfUser;
    }

    public void setShelfUser(String shelfUser) {
        this.shelfUser = shelfUser;
    }

    public String getShelfUserName() {
        return shelfUserName;
    }

    public void setShelfUserName(String shelfUserName) {
        this.shelfUserName = shelfUserName;
    }

    public String getSmallPackageBarCode() {
        return smallPackageBarCode;
    }

    public void setSmallPackageBarCode(String smallPackageBarCode) {
        this.smallPackageBarCode = smallPackageBarCode;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getStorageAreaCode() {
        return storageAreaCode;
    }

    public void setStorageAreaCode(String storageAreaCode) {
        this.storageAreaCode = storageAreaCode;
    }

    public String getStorageRoomCode() {
        return storageRoomCode;
    }

    public void setStorageRoomCode(String storageRoomCode) {
        this.storageRoomCode = storageRoomCode;
    }

    public String getStorageRoomName() {
        return storageRoomName;
    }

    public void setStorageRoomName(String storageRoomName) {
        this.storageRoomName = storageRoomName;
    }

    public String getStorageType() {
        return storageType;
    }

    public void setStorageType(String storageType) {
        this.storageType = storageType;
    }

    public String getStorageTypeCode() {
        return storageTypeCode;
    }

    public void setStorageTypeCode(String storageTypeCode) {
        this.storageTypeCode = storageTypeCode;
    }

    public String getStorageTypeName() {
        return storageTypeName;
    }

    public void setStorageTypeName(String storageTypeName) {
        this.storageTypeName = storageTypeName;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public String getSupplierCodeList() {
        return supplierCodeList;
    }

    public void setSupplierCodeList(String supplierCodeList) {
        this.supplierCodeList = supplierCodeList;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getWhetherRegulatory() {
        return whetherRegulatory;
    }

    public void setWhetherRegulatory(String whetherRegulatory) {
        this.whetherRegulatory = whetherRegulatory;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
      



