package com.xyy.wms.pda.model.moveStorage.delivery;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiMoveStorageService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.common.RunningTaskResult;
import com.xyy.wms.pda.bean.moveStorage.CarNumberBean;
import com.xyy.wms.pda.bean.moveStorage.CarNumberResult;
import com.xyy.wms.pda.bean.moveStorage.DeliveryBean;
import com.xyy.wms.pda.bean.moveStorage.StartTaskBean;
import com.xyy.wms.pda.bean.moveStorage.StartTaskResult;
import com.xyy.wms.pda.contract.movestorage.delivery.DeliveryContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * 下车 -  first step
 */
public class DeliveryModel extends BaseModel implements DeliveryContract.DeliveryModel {
    public static DeliveryModel newInstance() {
        return new DeliveryModel();
    }
    /**
     * 开启任务
     */
    public Observable<BaseResponseBean<Boolean>> registration(DeliveryBean deliveryBean){
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).delivery(deliveryBean)
            .compose(RxHelper.rxSchedulerHelper());
    }
}
