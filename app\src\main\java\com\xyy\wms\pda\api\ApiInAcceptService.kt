package com.xyy.wms.pda.api

import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.*
import io.reactivex.Observable
import retrofit2.http.*
import java.util.ArrayList

/**
 * 验收接口
 */
interface ApiInAcceptService {
    /**
     *  获取验收单集合
     */
    @GET("purchase/checkOrder/getCheckOrders")
    fun getCheckOrder(): Observable<BaseResponseBean<MutableList<CheckAcceptBill>>>

    /**
     *根据验收单号解绑验收单
     */
    @FormUrlEncoded
    @POST("purchase/checkOrder/doUnbindingForOrderCode")
    fun doUnbindingForOrderCode(@Field("checkOrderCode") checkOrderCode: String): Observable<BaseResponseBean<Any>>

    /**
     * 根据验收单号绑定并查询明细
     */
    @FormUrlEncoded
    @POST("purchase/checkOrder/getDetailsByOrderCode")
    fun getDetailsByOrderCode(@Field("checkOrderCode") checkOrderCode: String): Observable<BaseResponseBean<CheckAcceptGoodsResult>>

    /**
     *  验收提交
     */
    @POST("purchase/checkOrder/doSubmit")
    fun checkOrderSubmit(@Body acceptDetailListPost: AcceptDetailListPost?): Observable<BaseResponseBean<List<AcceptSubmitResult>>>

    /**
     * 验收完成校验容器校验效期
     */
    @POST("purchase/checkOrder/checkContiner")
    fun checkContainer(@Body checkContainerPost: CheckContainerPost): Observable<BaseResponseBean<Int>>

    /**
     * 验收完成校验容器校验效期
     */
    @POST("purchase/checkOrder/turnDown")
    fun turnDown(@Body turnDownPost: TurnDownPost): Observable<BaseResponseBean<Any>>

    /**
     *  验收特殊药品二次登录接口
     */
    @POST("purchase/checkOrder/secondLogin")
    fun secondLogin(@Body secondLoginPost: SecondLoginPost):Observable<BaseResponseBean<SecondLoginResult>>

    /**
     *  获取当前已入库验收商品明细
     */
    @POST("instock/wmsApp/checkOrder/getCheckOrderDetailList")
    fun getCheckOrderDetailList(@Body checkOrderDetailListPost : CheckOrderDetailListPost) : Observable<BaseResponseBean<List<CheckOrderDetailListBean>>>

    /**
     * APP->入库验收->20->PDA商品信息维护->信息查询
     */
    @POST("instock/wmsApp/checkOrder/productInfoMaintain/query")
    fun queryProductInfoMaintain(@Body productInfoMaintainQueryPost : ProductInfoMaintainQueryPost) : Observable<BaseResponseBean<ProductInfoMaintainQueryBean>>
    /**
     * 库存上下限
     */
    @POST("instock/wmsApp/checkOrder/productInfoMaintain/getUpperLower")
    fun getUpperLowerProductInfoMaintain(@Body getUpperLowerProductInfoMaintainPost : GetUpperLowerProductInfoMaintainPost) : Observable<BaseResponseBean<GetUpperLowerProductInfoMaintainBean>>
    /**
     * APP->入库验收->22->PDA商品信息维护->逻辑区多选弹窗
     */
    @POST("instock/wmsApp/checkOrder/productInfoMaintain/logicalregion")
    fun logicalRegionProductInfoMaintain(@Body logicalRegionProductInfoMaintainPost : LogicalRegionProductInfoMaintainPost) : Observable<BaseResponseBean<ArrayList<LogicalRegionProductInfoMaintainBean>>>
    /**
     * APP->入库验收->23->PDA商品信息维护->信息保存
     */
    @POST("instock/wmsApp/checkOrder/productInfoMaintain/save")
    fun saveProductInfoMaintain(@Body saveProductInfoMaintainPost : SaveProductInfoMaintainPost) : Observable<BaseResponseBean<String>>
}
