package com.xyy.wms.pda.ui.adapter.instorage.checkaccept

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.xyy.wms.pda.R
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderDetailListBean

class DetailsOfAcceptedGoodsAdapter(layout: Int) : BaseQuickAdapter<CheckOrderDetailListBean, BaseViewHolder>(layout) {
  override fun convert(helper: BaseViewHolder?, item: CheckOrderDetailListBean?) {
    val position = helper?.layoutPosition ?: 0
    helper?.setText(R.id.tv_no, (position + 1).toString())
    helper?.setText(R.id.tv_productCode, item?.productCode)
    helper?.setText(R.id.tv_productName, item?.productName)
    helper?.setText(R.id.tv_productBatchCode, item?.productBatchCode)
  }
}
