package com.xyy.wms.pda.presenter.inmanager.check;

import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.inmanager.check.AddCheckBean;
import com.xyy.wms.pda.bean.inmanager.check.CheckStorageAreaBean;
import com.xyy.wms.pda.contract.inmanager.check.AddCheckContract;
import com.xyy.wms.pda.helper.SimpleErrorConsumer;
import com.xyy.wms.pda.helper.SimpleSuccessConsumer;
import com.xyy.wms.pda.model.inmanager.check.AddCheckModel;

import java.util.List;

/**
 * Created by zcj on 2018/11/7 11
 */
public class AddCheckPresenter extends AddCheckContract.AddCheckPresenter {

    public static AddCheckPresenter newInstance() {
        return new AddCheckPresenter();
    }

    @Override
    protected AddCheckContract.IAddCheckModel getModel() {
        return AddCheckModel.newInstance();
    }

    @Override
    public void addWarehouseCheck(AddCheckBean bean) {
        if (mIModel == null || mIView == null) return;
        mRxManager.register(mIModel.addWarehouseCheck(bean).subscribe(new SimpleSuccessConsumer<BaseResponseBean>(mIView) {
            @Override
            public void onSuccess(BaseResponseBean bean) {
                if (mIView == null)
                    return;
                mIView.addWarehouseCheckSuccess(bean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }

    @Override
    public void findStorageType() {
        if (mIModel == null || mIView == null) return;
        mRxManager.register(mIModel.findStorageType().subscribe(new SimpleSuccessConsumer<BaseResponseBean<List<CheckStorageAreaBean>>>(mIView) {
            @Override
            public void onSuccess(BaseResponseBean<List<CheckStorageAreaBean>> bean) {
                if (mIView == null)
                    return;
                mIView.findStorageTypeSuccess(bean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }

    @Override
    public void findStorageArea(String storageTypeId) {
        if (mIModel == null || mIView == null) return;
        mRxManager.register(mIModel.findStorageArea(storageTypeId).subscribe(new SimpleSuccessConsumer<BaseResponseBean<List<CheckStorageAreaBean>>>(mIView) {
            @Override
            public void onSuccess(BaseResponseBean<List<CheckStorageAreaBean>> bean) {
                if (mIView == null)
                    return;
                mIView.findStorageAreaSuccess(bean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }

    @Override
    public void getCheckNo() {
        if (mIModel == null || mIView == null)
            return;
        mRxManager.register(mIModel.getCheckNo().subscribe(new SimpleSuccessConsumer<BaseResponseBean<String>>(mIView) {
            @Override
            public void onSuccess(BaseResponseBean<String> bean) {
                if (mIView == null)
                    return;
                mIView.getCheckNoSuccess(bean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }
}
