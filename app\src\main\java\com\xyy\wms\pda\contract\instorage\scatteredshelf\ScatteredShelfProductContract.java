package com.xyy.wms.pda.contract.instorage.scatteredshelf;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.shelf.CommitShelfResult;

/**
 * 零散的 商品上架
 */
public interface ScatteredShelfProductContract {
    interface IScatteredShelfProductView extends IBaseActivity {
        void commitStorageOrderDetailSuccess(BaseResponseBean<CommitShelfResult> baseResponseBean);
    }
}
