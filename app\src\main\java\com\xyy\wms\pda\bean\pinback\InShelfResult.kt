package com.xyy.wms.pda.bean.pinback

import java.io.Serializable

/**
 * Created by lx on 2018/11/20 10
 * E-Mail：<EMAIL>
 * 上架返回的，（result）列表数据对象
 */
class InShelfResult<T> : Serializable {

    //上架的单据列表。

    var arrivalDate: String? = null    //送货日期	string
    var checkUser: String? = null    //验收员id	string
    var checkUserName: String? = null    //验收员姓名	string
    var createTime: String? = null    //创建时间	number	@mock=1540993631000
    var createUser: String? = null    //创建人id	string	@mock=359
    var departNames: String? = null    //部门名称	string	会有多个，逗号分隔
    var fromOrderCode: String? = null//	来源单据编号	string	@mock=DXG201810310006
    var fromOrderType: Int = 0    //来源单据类型	number	1:验收单;2:待修改单;3:复查单
    var id: Long = 0    //主键id	number	@mock=128
    var isChineseMedicinal: Int = 0//	是否是中药	number	1 中药 0 西药'
    var occupyType: Int = 0    //单据占用类型	number	(1:PC端; 2:PDA)
    var orderType: String? = null    //入库单类型	string	(1:采购入库;2:购进退出入库) 无用
    var orgCode: String? = null    //机构编号	string	@mock=003
    var purchaseUsers: String? = null    //采购员名称	string
    var receiveOrderCode: String? = null    //收货单据编号	string	@mock=SHD1810310011
    var receiveUser: String? = null    //收货人id	string	@mock=359
    var receiveUserName: String? = null    //收货人姓名	string
    var recheckUser: String? = null//	复核员id	string
    var recheckUserName: String? = null    //复核员姓名	string
    var refundTaskOrderCode: String? = null    //采退任务单单据编号	string
    var scanOrderCode: String? = null    //监管码扫描单据编号	string	@mock=JGM201810310015
    var shelfUser: String? = null    //上架员id	string	@mock=
    var shelfUserName: String? = null//	上架员姓名	string
    var storageOrderCode: String? = null//	入库单据编号	string	@mock=RKD1810310005
    var storageOrderStatus: Int = 0//	入库单状态	number	1:进行中;2:已完成;3:异常关闭@mock=1
    var storageOrderStatusDesc: String? = null//	入库单状态描述	string
    var storageOrderType: Int = 0//	入库单类型	number	1:购进入库;2:购进退出入库@mock=1
    var supplierCode: String? = null    //供应商编号	string	@mock=GYS001
    var supplierName: String? = null    //供应商名称	string	@mock=供应商1
    var updateTime: String? = null    //更新时间	string
    var updateUser: String? = null//	更新用户id	string
    var yn: Int = 0    //逻辑删除	number	0:删除;1:未删除(有效)

    var purchaseStorageOrderDetailVoList: List<T>? = null

}
