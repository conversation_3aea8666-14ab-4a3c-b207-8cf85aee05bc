package com.xyy.wms.pda.constant

/**
 * 菜单标识
 */
object MenuConstant {
  //入库
  /**
   * 入库管理 - 整件上架
   */
  const val MENU_IN_WHOLE_SHELF = "menu:wmspda:storageWholeOrder"

  /**
   * 入库管理 - 零货上架
   */
  const val MENU_IN_SCATTERED_SHELF = "menu:wmspda:storageScatteredOrder"

  /**
   * 入库-  整散合一上架
   */
  const val MENU_IN_WHOLE_SCATTERED_SHELF = "menu:wmspda:storageGroupOrder"

  /**
   * 入库-  容器绑定 笼车
   */
  const val MENU_IN_CONTAINER_BIND_CAGE_CAR = "menu:wmspda:storageContainerBindCageCar"

  /**
   * 入库-  笼车上架
   */
  const val MENU_IN_CAGE_CAR_SHELF = "menu:wmspda:storageCageCarOrder"

  /**
   * 入库-  验收
   */
  const val MENU_IN_CHECK_ACCEPT = "menu:wmspda:storageCheckAccept"
  const val MENU_IN_QUERY_CONTAINER_STATUS = "menu:wmspda:storageQueryContainerStatus"

  /**
   * 销退入库 - 整件上架
   */
  const val MENU_SALES_RETURN_WHOLE = "menu:wmspda:salesReturnWholeOrder"

  /**
   * 销退入库 - 零货上架
   */
  const val MENU_SALES_RETURN_SCATTERED = "menu:wmspda:salesReturnScatteredOrder"

  /**
   * 销退-  整散合一上架
   */
  const val MENU_PIN_BACK_WHOLE_SCATTERED_SHELF = "menu:wmspda:salesReturnWholeBulkUnity"
  //库内
  /**
   * 库内管理 - 盘查
   */
  const val MENU_IN_MANAGER_CHECK_UP = "menu:wmspda:insideCheckUp"

  /**
   * 库内管理 - 移库上架
   */
  const val MENU_IN_MANAGER_MOVEMENT_UP = "menu:wmspda:insideMoveUp"

  /**
   * 库内管理 - 移库下架
   */
  const val MENU_IN_MANAGER_MOVEMENT_DOWN = "menu:wmspda:insideMoveDown"

  /**
   * 库内管理 - 货位调整
   */
  const val MENU_IN_MANAGER_LOCATION = "menu:wmspda:insideLocationAdjustment"

  /**
   * 库内管理 - 补货上架
   */
  const val MENU_IN_MANAGER_UP_SHELF = "menu:wmspda:insideReplenishmentUp"

  /**
   * 库内管理 - 补货下架
   */
  const val MENU_IN_MANAGER_DOWN_SHELF = "menu:wmspda:insideReplenishmentDown"

  /**
   * 库内管理 - 商品查询
   */
  const val MENU_IN_MANAGER_GOODS_QUERY = "menu:wmspda:insideProductQuery"

  /**
   * 库内管理 - 盘点
   */
  const val MENU_IN_INVENTORY = "menu:wmspda:insideInventory"

  /**
   * 库内管理 - 同品同批货位调整
   */
  const val MENU_IN_SAME_BATCH_LOCATION_ADJUSTMENT = "menu:wmspda:insideSameBatchLocationAdjustment"

  /**
   * 业务类型调整
   */
  const val MENU_IN_MANAGER_CHANNEL_MODIFY = "menu:wmspda:insideChannelMove"
  /**
   * 自由盘点
   */
  const val MENU_IN_MANAGER_CUSTOM_CHECK = "menu:wmspda:customCheck"
  /**
   * 高位移库
   */
  const val MENU_IN_MANAGER_HEIGHT_SHELVES = "menu:wmspda:heightShelves"
  //出库
  /**
   * 出库管理 - 拆零拣货
   */
  const val MENU_OUT_SCATTERED_PICKING = "menu:wmspda:outPickingTask"

  /**
   * 出库管理 - 2b拣货
   */
  const val MENU_OUT_2B_PICKING = "menu:wmspda:2bPickingTask"

  /**
   * 出库管理 - 外复核（按单）
   */
  const val MENU_OUT_REVIEW = "menu:wmspda:outPartsOutReview"

  /**
   * 出库管理 - 外复核（按箱）
   */
  const val MENU_OUT_REVIEW_BOX = "menu:wmspda:outPartsOutReviewBox"

  /**
   * 出库管理 - 随货同行单绑定
   */
  const val MENU_OUT_BIND_CODE = "menu:wmspda:outGoodsOrderBind"

  /**
   * 出库管理 - 集货区绑定
   */
  const val MENU_OUT_BIND_COLLECTION_AREA_CODE = "menu:wmspda:outGoodsStoreareaBind"

  /**
   * 出库管理 - 提总出库
   */
  const val MENU_OUT_OUT_REVIEW = "menu:wmspda:outMentionTotalReview"

  /**
   * 购进退出 - 购进退出
   */
  const val MENU_REFUND_PIN_BACK = "menu:wmspda:outPurchaseExit"

  /**
   * 购进退出 - 购进退出电子追溯码
   */
  const val MENU_REFUND_PIN_BACK_SUPERVISION = "menu:wmspda:outPurchaseExitScanned"

  /**
   * 购进退出-复核
   */
  const val MENU_REFUND_PIN_BACK_REVIEW = "menu:wmspda:outPurchaseExitReview"

  /**
   * 异常问题处理
   */
  const val MENU_EXCEPTION_PROBLEM_HANDLE = "menu:wmspda:outExceptionHandle"

  /**
   * 出库-- 集货扫码
   */
  const val MENU_Store_GOODS_SCAN = "menu:wmspda:outStoreGoodsScan"

  /****************搬仓****************/
  /**
   * 整件下架
   */
  const val MENU_MOVE_STORAGE_WHOLE_SHELF_DOWN = "menu:wmspda:moveStorageWholeShelfDown"

  /**
   * 整件上架
   */
  const val MENU_MOVE_STORAGE_WHOLE_SHELF_LIST = "menu:wmspda:moveStorageWholeShelfList"

  /**
   * 零获下架
   */
  const val MENU_MOVE_STORAGE_SCATTERED_DOWN = "menu:wmspda:moveStorageScatteredDown"

  /**
   * 零获上架
   */
  const val MENU_MOVE_STORAGE_SCATTERED_UP = "menu:wmspda:moveStorageScatteredUp"

  /**
   * 装车
   */
  const val MENU_MOVE_STORAGE_CAR_UP = "menu:wmspda:moveStorageCarUp"

  /**
   * 下车
   */
  const val MENU_MOVE_STORAGE_CAR_DOWN = "menu:wmspda:moveStorageCarDown"

  /**
   * 司机登记
   */
  const val MENU_MOVE_STORAGE_DRIVER_ADD = "menu:wmspda:moveStorageDriverAdd"
  /**
   * 司机登记
   */
  const val MENU_MOVE_STORAGE_DELIVERY = "menu:wmspda:moveStorageDelivery"
}
