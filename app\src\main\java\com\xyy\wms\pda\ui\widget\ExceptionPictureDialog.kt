package com.xyy.wms.pda.ui.widget

import android.Manifest
import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Environment
import android.provider.MediaStore
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.xyy.common.util.ToastUtils
import com.xyy.wms.pda.R
import com.xyy.wms.pda.bean.instorage.BusinessInfo
import com.xyy.wms.pda.bean.instorage.PictureInfo
import com.xyy.wms.pda.bean.instorage.UploadStatus
import com.xyy.wms.pda.config.ExceptionPictureConfig
import com.xyy.wms.pda.ui.adapter.PictureGridAdapter
import com.xyy.wms.pda.utils.ImageCompressUtils
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

/**
 * 异常图片上传弹窗组件
 * 支持拍照、选择图片、预览、删除和批量上传功能
 */
class ExceptionPictureDialog(private val context: Context) : Dialog(context, R.style.FullScreenDialog) {
    
    // 组件属性
    var maxCount: Int = ExceptionPictureConfig.MAX_PICTURE_COUNT
    var businessInfo: BusinessInfo? = null
    var onPictureUploadListener: ((count: Int) -> Unit)? = null
    
    // UI组件
    private lateinit var ivBack: ImageView
    private lateinit var tvSubmit: TextView
    private lateinit var tvBusinessCode: TextView
    private lateinit var tvRelatedInfo: TextView
    private lateinit var rvPictures: RecyclerView
    private lateinit var pictureAdapter: PictureGridAdapter
    
    // 图片数据
    private val pictureList: MutableList<PictureInfo> = mutableListOf()
    
    // 拍照相关
    private var currentPhotoPath: String = ""
    private val REQUEST_CAMERA_PERMISSION = 1001
    private val REQUEST_STORAGE_PERMISSION = 1002
    private val REQUEST_TAKE_PHOTO = 2001
    private val REQUEST_PICK_IMAGE = 2002
    
    init {
        initDialog()
    }
    
    /**
     * 初始化弹窗
     */
    private fun initDialog() {
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_exception_picture, null)
        setContentView(view)
        
        // 设置弹窗属性
        window?.setLayout(
            android.view.ViewGroup.LayoutParams.MATCH_PARENT,
            android.view.ViewGroup.LayoutParams.WRAP_CONTENT
        )
        
        initViews(view)
        setupRecyclerView()
        setupClickListeners()
        updateBusinessInfo()
    }
    
    /**
     * 初始化视图
     */
    private fun initViews(view: View) {
        ivBack = view.findViewById(R.id.iv_back)
        tvSubmit = view.findViewById(R.id.tv_submit)
        tvBusinessCode = view.findViewById(R.id.tv_business_code)
        tvRelatedInfo = view.findViewById(R.id.tv_related_info)
        rvPictures = view.findViewById(R.id.rv_pictures)
    }
    
    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        pictureAdapter = PictureGridAdapter()
        rvPictures.layoutManager = GridLayoutManager(context, 5)
        rvPictures.adapter = pictureAdapter
        
        // 设置适配器事件监听
        pictureAdapter.onItemClickListener = { position ->
            showImagePickerDialog()
        }
        
        pictureAdapter.onDeleteClickListener = { position ->
            showDeleteConfirmDialog(position)
        }
    }
    
    /**
     * 设置点击事件监听
     */
    private fun setupClickListeners() {
        ivBack.setOnClickListener {
            dismiss()
        }
        
        tvSubmit.setOnClickListener {
            uploadPictures()
        }
    }
    
    /**
     * 更新业务信息显示
     */
    private fun updateBusinessInfo() {
        businessInfo?.let { info ->
            tvBusinessCode.text = "业务单号：${info.businessCode}"
            
            val relatedInfoText = StringBuilder()
            info.relatedInfo.forEach { (key, value) ->
                if (relatedInfoText.isNotEmpty()) {
                    relatedInfoText.append("，")
                }
                relatedInfoText.append("$key：$value")
            }
            tvRelatedInfo.text = "相关信息：$relatedInfoText"
        }
    }
    
    /**
     * 显示图片选择对话框
     */
    private fun showImagePickerDialog() {
        val options = arrayOf("拍照", "从相册选择")
        AlertDialog.Builder(context)
            .setTitle("选择图片")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> checkCameraPermissionAndTakePhoto()
                    1 -> checkStoragePermissionAndPickImage()
                }
            }
            .show()
    }
    
    /**
     * 检查相机权限并拍照
     */
    private fun checkCameraPermissionAndTakePhoto() {
        if (ContextCompat.checkSelfPermission(context, Manifest.permission.CAMERA) 
            != PackageManager.PERMISSION_GRANTED) {
            // 请求相机权限
            if (context is Activity) {
                ActivityCompat.requestPermissions(
                    context,
                    arrayOf(Manifest.permission.CAMERA),
                    REQUEST_CAMERA_PERMISSION
                )
            }
        } else {
            takePhoto()
        }
    }
    
    /**
     * 检查存储权限并选择图片
     */
    private fun checkStoragePermissionAndPickImage() {
        if (ContextCompat.checkSelfPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE) 
            != PackageManager.PERMISSION_GRANTED) {
            // 请求存储权限
            if (context is Activity) {
                ActivityCompat.requestPermissions(
                    context,
                    arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE),
                    REQUEST_STORAGE_PERMISSION
                )
            }
        } else {
            pickImageFromGallery()
        }
    }
    
    /**
     * 拍照
     */
    private fun takePhoto() {
        val intent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
        if (intent.resolveActivity(context.packageManager) != null) {
            val photoFile = createImageFile()
            if (photoFile != null) {
                val photoURI = FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.fileprovider",
                    photoFile
                )
                intent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI)
                if (context is Activity) {
                    context.startActivityForResult(intent, REQUEST_TAKE_PHOTO)
                }
            }
        }
    }
    
    /**
     * 从相册选择图片
     */
    private fun pickImageFromGallery() {
        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
        if (context is Activity) {
            context.startActivityForResult(intent, REQUEST_PICK_IMAGE)
        }
    }
    
    /**
     * 创建图片文件
     */
    private fun createImageFile(): File? {
        return try {
            val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val imageFileName = "JPEG_${timeStamp}_"
            val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
            val image = File.createTempFile(imageFileName, ".jpg", storageDir)
            currentPhotoPath = image.absolutePath
            image
        } catch (ex: IOException) {
            null
        }
    }
    
    /**
     * 处理拍照结果
     */
    fun handleCameraResult() {
        if (currentPhotoPath.isNotEmpty()) {
            addPictureFromPath(currentPhotoPath)
        }
    }

    /**
     * 处理相册选择结果
     */
    fun handleGalleryResult(data: Intent?) {
        data?.data?.let { uri ->
            val path = getRealPathFromURI(uri)
            if (path != null) {
                addPictureFromPath(path)
            }
        }
    }

    /**
     * 处理权限请求结果
     */
    fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>, grantResults: IntArray) {
        when (requestCode) {
            REQUEST_CAMERA_PERMISSION -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    takePhoto()
                } else {
                    ToastUtils.showShortSafe("需要相机权限才能拍照")
                }
            }
            REQUEST_STORAGE_PERMISSION -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    pickImageFromGallery()
                } else {
                    ToastUtils.showShortSafe("需要存储权限才能选择图片")
                }
            }
        }
    }
    
    /**
     * 从URI获取真实路径
     */
    private fun getRealPathFromURI(uri: Uri): String? {
        return try {
            val cursor = context.contentResolver.query(uri, null, null, null, null)
            cursor?.use {
                if (it.moveToFirst()) {
                    val columnIndex = it.getColumnIndex(MediaStore.Images.Media.DATA)
                    if (columnIndex >= 0) {
                        it.getString(columnIndex)
                    } else {
                        // 如果无法获取路径，尝试复制文件到临时目录
                        copyUriToTempFile(uri)
                    }
                } else {
                    null
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    /**
     * 复制URI文件到临时目录
     */
    private fun copyUriToTempFile(uri: Uri): String? {
        return try {
            val inputStream = context.contentResolver.openInputStream(uri)
            val tempFile = File.createTempFile("temp_image", ".jpg", context.cacheDir)
            val outputStream = FileOutputStream(tempFile)

            inputStream?.use { input ->
                outputStream.use { output ->
                    input.copyTo(output)
                }
            }

            tempFile.absolutePath
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    /**
     * 从路径添加图片
     */
    private fun addPictureFromPath(imagePath: String) {
        if (pictureAdapter.getActualPictures().size >= maxCount) {
            ToastUtils.showShortSafe("最多只能上传${maxCount}张图片")
            return
        }
        
        // 压缩图片
        val compressedPath = try {
            ImageCompressUtils.compressImage(imagePath, ExceptionPictureConfig.COMPRESS_QUALITY)
        } catch (e: Exception) {
            // 如果压缩失败，使用原图片
            imagePath
        }
        
        // 检查文件大小
        val file = File(compressedPath ?: imagePath)
        if (file.length() > ExceptionPictureConfig.MAX_FILE_SIZE) {
            ToastUtils.showShortSafe("图片大小不能超过5MB")
            return
        }
        
        // 创建图片信息
        val pictureInfo = PictureInfo(
            id = UUID.randomUUID().toString(),
            localPath = compressedPath ?: imagePath,
            isLocal = true,
            isPlaceholder = false,
            uploadStatus = UploadStatus.NONE,
            fileSize = file.length(),
            createTime = System.currentTimeMillis()
        )
        
        pictureAdapter.addPicture(pictureInfo)
        pictureList.add(pictureInfo)
    }
    
    /**
     * 显示删除确认对话框
     */
    private fun showDeleteConfirmDialog(position: Int) {
        AlertDialog.Builder(context)
            .setTitle("确认删除")
            .setMessage("确定要删除这张图片吗？")
            .setPositiveButton("删除") { _, _ ->
                deletePicture(position)
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    /**
     * 删除图片
     */
    private fun deletePicture(position: Int) {
        pictureAdapter.removePicture(position)
        if (position < pictureList.size) {
            pictureList.removeAt(position)
        }
    }
    
    /**
     * 上传图片
     */
    private fun uploadPictures() {
        val actualPictures = pictureAdapter.getActualPictures()
        if (actualPictures.isEmpty()) {
            ToastUtils.showShortSafe("请先选择图片")
            return
        }
        
        // 这里应该调用实际的上传接口
        // 暂时模拟上传成功
        onPictureUploadListener?.invoke(actualPictures.size)
        ToastUtils.showShortSafe("图片上传成功")
        dismiss()
    }
}
