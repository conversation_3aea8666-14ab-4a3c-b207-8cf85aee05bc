package com.xyy.wms.pda.model.out.review

import com.xyy.utilslibrary.base.BaseModel
import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.api.ApiOutManagerService
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.out.ReviewOutListBean
import com.xyy.wms.pda.bean.out.outsideReview.Building
import com.xyy.wms.pda.contract.out.review.ReviewOutBoxListContract
import com.xyy.wms.pda.net.RetrofitCreateHelper
import io.reactivex.Observable

class ReviewOutBoxListModel : BaseModel(), ReviewOutBoxListContract.IReviewOutBoxListModel {

  private val api = RetrofitCreateHelper.createApi(ApiOutManagerService::class.java)

  override fun getReviewOutBoxList(map: Map<String, String?>): Observable<BaseResponseBean<ReviewOutListBean>> {
    return api.getReviewOutList(map).compose(RxHelper.rxSchedulerHelper())
  }

  override fun getAllBuildingCode(): Observable<BaseResponseBean<MutableList<Building>>> {
    return api.allBuildingCode.compose(RxHelper.rxSchedulerHelper())
  }

  companion object {
    fun newInstance(): ReviewOutBoxListModel {
      return ReviewOutBoxListModel()
    }
  }
}
