package com.xyy.wms.pda.bean.inmanager.check;

/**
 * Created by zcj on 2018/11/9 10
 * 库别
 */
public class CheckStorageAreaBean {
//    id		            number	库别id
//    storageTypeCode		string	库别编码
//    storageTypeName		string	库别名称

    private String id;//number	库别id
    private String storageTypeCode;//string	库别编码
    private String storageTypeName;//string	库别名称
    private String storageAreaCode;//string 库区编码

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStorageTypeCode() {
        return storageTypeCode;
    }

    public void setStorageTypeCode(String storageTypeCode) {
        this.storageTypeCode = storageTypeCode;
    }

    public String getStorageTypeName() {
        return storageTypeName;
    }

    public void setStorageTypeName(String storageTypeName) {
        this.storageTypeName = storageTypeName;
    }

    public String getStorageAreaCode() {
        return storageAreaCode;
    }

    public void setStorageAreaCode(String storageAreaCode) {
        this.storageAreaCode = storageAreaCode;
    }

    @Override
    public String toString() {
        return "CheckStorageAreaBean{" +
                "id='" + id + '\'' +
                ", storageTypeCode='" + storageTypeCode + '\'' +
                ", storageTypeName='" + storageTypeName + '\'' +
                ", storageAreaCode='" + storageAreaCode + '\'' +
                '}';
    }
}
