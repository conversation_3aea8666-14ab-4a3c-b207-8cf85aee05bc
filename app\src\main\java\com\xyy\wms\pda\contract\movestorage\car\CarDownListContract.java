package com.xyy.wms.pda.contract.movestorage.car;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.common.MoveStorageFinishedBean;
import com.xyy.wms.pda.bean.common.RunningTaskResult;
import com.xyy.wms.pda.bean.moveStorage.AddTaskCarDownDetailBean;
import com.xyy.wms.pda.bean.moveStorage.AddTaskDetailBean;
import com.xyy.wms.pda.ui.activity.movestorage.car.CarDownListActivity;

import io.reactivex.Observable;
/**
 * 下车-列表
 */
public interface CarDownListContract {
    interface CarDownListModel extends IBaseModel {
        /**
         * 进行中的任务
         */
        Observable<BaseResponseBean<RunningTaskResult>> getRunningTask();
        /**
         * 下架完成
         */
        Observable<BaseResponseBean<Boolean>> setFinished(MoveStorageFinishedBean finishedBean);
        /**
         * 添加任务明细
         */
        Observable<BaseResponseBean<Boolean>> addTaskDetail(AddTaskCarDownDetailBean taskDetailBean);
    }

    interface CarDownListView extends IBaseActivity {
        /**
         * 进行中任务
         */
        void getRunningTaskViewSuccess(BaseResponseBean<RunningTaskResult> requestBaseBean);

        /**
         * 添加任务明细
         */
        void addTaskDetailViewSuccess(BaseResponseBean<Boolean> requestBaseBean);

        /**
         * 下架完成
         */
        void setFinishedViewSuccess(BaseResponseBean<Boolean> requestBaseBean);

    }
    abstract class CarDownListPresenter extends BasePresenter<CarDownListModel, CarDownListActivity> {
        /**
         * 进行中的任务
         */
        public abstract void getRunningTask();
        /**
         * 添加任务明细
         */
        public abstract void addTaskDetail(AddTaskCarDownDetailBean taskDetailBean);
        /**
         * 下架完成
         */
        public abstract void setFinished(MoveStorageFinishedBean finishedBean);
    }
}
