package com.xyy.wms.pda.bean.inmanager.search

/**
 * 库内查询 bean
 */
data class SearchGoodPositionBean(
  var goodsAllocation: String? = null,// 货位编码
  var batchNumber: String? = null,// 批号
  var sterilizingBatchNumber: String? = null,// 灭菌批号
  var amount: String? = null,// 库存数量
  val warehouseWhole: Long? = 0,         // 件数
  val warehouseScattered: Long? = 0,     // 零散数
  val smallPackageBarCode: String? = null,    // 小包装条码
  var specifications: String? = null,// 规格
  var packingUnit: String? = null,// 单位
  var storageTypeCode: String? = null,// 库别编号
  var productionDate: String? = null,// 生产日期
  var productionDateStr: String? = null,// 生产日期
  var specification: String? = null,// 包装规格
  var validityDate: String? = null,// 有效期
  var validityDateStr: String? = null,// 有效期
  var occupyAmount: String? = null,// 出库预占
  var amountWait: String? = null,// 入库预占
  var channelName: String? = null,// 业务类型
  var channelCode:String?=null, //业务类型-新
  var buildingName: String?,// 建筑物
  var occupyOutAmount: String?=null, //出库已分配数量
  var storageNumber:String?=null, //入库已分配数量
  var amountInWait:String?=null, //入库已分配数量
  var warehouseAmountOut:String?=null,//库内出库已分配数量
  var warehouseAmountIn:String?=null,//库内入库已分配数量
  var pickAmount:String?=null, //预扣数量
  var replenishAmount:String?=null//补货已分配数量
  )
