package com.xyy.wms.pda.model.instorage.cagecar;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.api.ApiService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.cageCar.CageCarShelfResult;
import com.xyy.wms.pda.bean.productCode.ProductCode;
import com.xyy.wms.pda.contract.instorage.cagecar.CageCarShelfContract;
import com.xyy.wms.pda.model.productCode.GetProductCodeModel;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.List;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 02/25/2020 12:00
 */
public class CageCarShelfModel extends BaseModel implements CageCarShelfContract.ICageCarShelfModel , GetProductCodeModel {

    public static CageCarShelfModel newInstance() {
        return new CageCarShelfModel();
    }
    @Override
    public Observable<BaseResponseBean<CageCarShelfResult>> getRollContainerShelfList(String rollContainerCode) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).getRollContainerShelfList(rollContainerCode)
                .compose(RxHelper.rxSchedulerHelper());
    }
    @Override
    public Observable<BaseResponseBean<List<ProductCode>>> getProductBarCode(String ownerCode, String packageBarCode) {
        return RetrofitCreateHelper.createApi(ApiService.class).selectProductPda(ownerCode, packageBarCode)
                .compose(RxHelper.rxSchedulerHelper());
    }
}