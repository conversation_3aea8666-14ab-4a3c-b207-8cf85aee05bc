package com.xyy.wms.pda.bean.moveStorage;

import java.io.Serializable;

public class StartTaskBean implements Serializable {
    private Number taskType;  //1整件下架，2零货下架，3装车，4下车,5整件上架，6零货上架
    private String palletNo = "";  //托盘号
    private String carNo = "";      //车牌号
    private String logicalRegionName = ""; //逻辑区域名称
    private String logicalRegion = ""; //逻辑区域编码
    private String sourceAllocation = ""; //原货位
    public StartTaskBean(String taskType,String palletNo){
        this.taskType =Integer.parseInt(taskType);
        this.palletNo=palletNo;
    }

    public void setTaskType(Number taskType){
        this.taskType = taskType;
    }

    public void setSourceAllocation(String sourceAllocation) {
        this.sourceAllocation = sourceAllocation;
    }
    public String getSourceAllocation(){
        return sourceAllocation;
    }
    public Number getTaskType(){
        return this.taskType;
    }

    public void setPalletNo(String palletNo){
        this.palletNo = palletNo;
    }

    public String getPalletNo(){
        return this.palletNo;
    }

    public void setCarNo(String carNo){
        this.carNo = carNo;
    }

    public String getCarNo(){
        return this.carNo;
    }

    public void setLogicalRegionName(String logicalRegionName){
        this.logicalRegionName = logicalRegionName;
    }

    public String getLogicalRegionName(){
        return this.logicalRegionName;
    }

    public void setLogicalRegion(String logicalRegion){this.logicalRegion = logicalRegion;}

    public String getLogicalRegion(){return this.logicalRegion;}
}
