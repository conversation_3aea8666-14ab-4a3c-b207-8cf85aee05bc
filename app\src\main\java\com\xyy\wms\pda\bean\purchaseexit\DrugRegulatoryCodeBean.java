package com.xyy.wms.pda.bean.purchaseexit;
public class DrugRegulatoryCodeBean {
    //此商品已扫描件包装数量
    private String scannedNumberLarge;
    //此商品已扫描中包装数量
    private String scannedNumberMiddle;
    //商品已扫描小包装数量
    private String scannedNumberSmall;

    public String getScannedNumberLarge() {
        return scannedNumberLarge;
    }

    public void setScannedNumberLarge(String scannedNumberLarge) {
        this.scannedNumberLarge = scannedNumberLarge;
    }

    public String getScannedNumberMiddle() {
        return scannedNumberMiddle;
    }

    public void setScannedNumberMiddle(String scannedNumberMiddle) {
        this.scannedNumberMiddle = scannedNumberMiddle;
    }

    public String getScannedNumberSmall() {
        return scannedNumberSmall;
    }

    public void setScannedNumberSmall(String scannedNumberSmall) {
        this.scannedNumberSmall = scannedNumberSmall;
    }
    @Override
    public String toString() {
        return "DrugRegulatoryCodeBean{" +
                "scannedNumberLarge=" + scannedNumberLarge +
                ", scannedNumberMiddle=" + scannedNumberMiddle +
                ", scannedNumberSmall=" + scannedNumberSmall +
                '}';
    }
}
