package com.xyy.wms.pda.presenter.instorage.cagecar;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.cageCar.ContainerProductDetailBean;
import com.xyy.wms.pda.contract.instorage.cagecar.ContainerCommodityDetailContract;
import com.xyy.wms.pda.helper.SimpleErrorConsumer;
import com.xyy.wms.pda.helper.SimpleSuccessConsumer;
import com.xyy.wms.pda.model.instorage.cagecar.ContainerCommodityDetailModel;

import java.util.List;

/**
 * Created by XyyMvpPdaTemplate on 02/18/2020 14:01
 */
public class ContainerCommodityDetailPresenter extends BasePresenter<ContainerCommodityDetailContract.IContainerCommodityDetailModel, ContainerCommodityDetailContract.IContainerCommodityDetailView> {

    public static ContainerCommodityDetailPresenter newInstance() {
        return new ContainerCommodityDetailPresenter();
    }

    @Override
    protected ContainerCommodityDetailModel getModel() {
        return ContainerCommodityDetailModel.newInstance();
    }
    public void getDetailsByContainer(String containerCode) {
        mRxManager.register(mIModel.getDetailsByContainer(containerCode).subscribe(new SimpleSuccessConsumer<BaseResponseBean<List<ContainerProductDetailBean>>>(mIView) {

            @Override
            public void onSuccess(BaseResponseBean<List<ContainerProductDetailBean>> baseResponseBean) {
                mIView.getDetailsByContainerSuccess(baseResponseBean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }
}
