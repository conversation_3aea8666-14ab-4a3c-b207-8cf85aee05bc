package com.xyy.wms.pda.model.instorage.cagecar;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.cageCar.ScanCageCar;
import com.xyy.wms.pda.contract.instorage.cagecar.ScanningCageCarContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import io.reactivex.Observable;
/**
 * Created by XyyMvpPdaTemplate on 02/25/2020 11:58
 */
public class ScanningCageCarModel extends BaseModel implements ScanningCageCarContract.IScanningCageCarModel {

    public static ScanningCageCarModel newInstance() {
        return new ScanningCageCarModel();
    }

    @Override
    public Observable<BaseResponseBean<ScanCageCar>> getStorageContainersInfo(String containerCode, String rollContainerCode) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).getStorageBoundContainersInfo(containerCode,rollContainerCode)
                .compose(RxHelper.rxSchedulerHelper());
    }
}