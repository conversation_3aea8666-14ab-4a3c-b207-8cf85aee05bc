package com.xyy.wms.pda.bean.instorage.cageCar

import java.io.Serializable

/**
 * 二级bean
 */
 data class CageCarOrderBean(
        val containerCode: String?,    //容器编号	string	@mock=100061
        val onShelfCount: Int?,//已上架条目数	number	例 2/3 中的2
        val orgCode: String?,//	机构编号	string	@mock=D547
        val pdaProductLineId: String?,    //pda商品列表页唯一标识(原始行号)	string	@mock=1
        val productBatchCode: String?,    //	string	@mock=20190212
        val productCode: String?,    //商品编号	string	@mock=Y1000017
        val productName: String?,//商品名称	string	@mock=少林风湿跌打膏
        val purchaseStorageOrderDetailVoList: List<CageCarProductBean>?,//该原始行中的明细列表	array<object>
        val rollContainerLocationCode: String?,    //笼车位置号	string	@mock=01
        val shelfCount: Int?,//	明细条目数	number	总数（例 2/3 中的3）
        val shelfUser: String?,//		string	@mock=781

        val storageOrderCode: String?,//	上架单编号	string	@mock=SJDD547202002210037

        val storageOrderStatus: Int=1,//	上架状态值	number	(1:未上架;2:已上架;) 数字
        val storageOrderStatusDesc: String?,//上架状态描述	string	(1:未上架;2:已上架;) 汉字
        val unShelfCount: Int?//	未上架条目数	number	@mock=1
) : Serializable