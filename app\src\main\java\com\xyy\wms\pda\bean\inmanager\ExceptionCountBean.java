package com.xyy.wms.pda.bean.inmanager;

/**
 * Created by zcj on 2018/11/15 17
 */
public class ExceptionCountBean {
    private String count;//异常数量	number	@mock=10
    private String exceptionCause;//异常原因 （1.少货 2.多货）	number	@mock=1
    private String exceptionType;//异常类型 (1.零货拣货 2.内复核)	number	@mock=1

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public String getExceptionCause() {
        return exceptionCause;
    }

    public void setExceptionCause(String exceptionCause) {
        this.exceptionCause = exceptionCause;
    }

    public String getExceptionType() {
        return exceptionType;
    }

    public void setExceptionType(String exceptionType) {
        this.exceptionType = exceptionType;
    }

    @Override
    public String toString() {
        return "ExceptionCountBean{" +
                "count='" + count + '\'' +
                ", exceptionCause='" + exceptionCause + '\'' +
                ", exceptionType='" + exceptionType + '\'' +
                '}';
    }
}
