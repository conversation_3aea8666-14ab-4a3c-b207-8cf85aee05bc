package com.xyy.wms.pda.bean.inmanager;

import java.io.Serializable;
import java.util.List;

/**
 * 移库单
 */
public class MovementBillBean implements Serializable {

    private List<DetailVOList> detailVOList;
    /**
     * 主键
     */
    private String id;
    /**
     * 移库单号
     */
    private String movementNo;
    /**
     * 已上架/下架商品数量
     */
    private String operateCnt;
    /**
     * 商品总数量
     */
    private String totalCnt;

    private String ownerName;

    private String buildingName;


    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getBuildingName() {
        return buildingName;
    }

    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }


    public static class DetailVOList implements Serializable {
        Number id; //主键
        String batchNumber;//商品批号
        String manufacturer;//生产厂家
        String movementNo;//移库单号
        String produceTime;//生产时间
        String productCode;//商品编码
        String productName;//商品名称
        String standard;
        Number submit;//是否关联	0，初始状态（未关联） 1，待下架 2，已下架 3.已上架
        String submitMsg;
        String validDate;//有效日期
        private String outGoodsPositionCode;    // 出库货位编码
        private String downGoodsPositionCode;   // 下架货位编码
        private String inGoodsPositionCode;     // 入库货位编码
        private String outStorageTypeCode;
        private String inStorageTypeCode;
        private String unit;
        private Number movePackNum;
        private Number moveScatteredNum;
        private Number moveTotalNum;

        public String getStandard(){return standard;}
        public void setStandard(String standard){this.standard = standard;}
        public String getSubmitMsg(){return submitMsg;}
        public void setSubmitMsg(String submitMsg){this.submitMsg = submitMsg;}
        public String getOutStorageTypeCode(){return outStorageTypeCode;}
        public void setOutStorageTypeCode(String outStorageTypeCode){this.outStorageTypeCode = outStorageTypeCode;}
        public String getInStorageTypeCode(){return inStorageTypeCode;}
        public void setInStorageTypeCode(String inStorageTypeCode){this.inStorageTypeCode = inStorageTypeCode;}
        public String getUnit(){return unit;}
        public void setUnit(String unit){this.unit = unit;}
        public Number getMovePackNum(){return movePackNum;}
        public void setMovePackNum(Number movePackNum){this.movePackNum = movePackNum;}
        public Number getmoveScatteredNum(){return moveScatteredNum;}
        public void setMoveScatteredNum(Number moveScatteredNum){this.moveScatteredNum = moveScatteredNum;}
        public Number getMoveTotalNum(){return moveTotalNum;}
        public void setMoveTotalNum(Number moveTotalNum){this.moveTotalNum = moveTotalNum;}


        public Number getId() {
            return id;
        }

        public void setId(Number id) {
            this.id = id;
        }

        public String getBatchNumber() {
            return batchNumber;
        }

        public void setBatchNumber(String batchNumber) {
            this.batchNumber = batchNumber;
        }

        public String getManufacturer() {
            return manufacturer;
        }

        public void setManufacturer(String manufacturer) {
            this.manufacturer = manufacturer;
        }

        public String getMovementNo() {
            return movementNo;
        }

        public void setMovementNo(String movementNo) {
            this.movementNo = movementNo;
        }

        public String getProduceTime() {
            return produceTime;
        }

        public void setProduceTime(String produceTime) {
            this.produceTime = produceTime;
        }

        public String getProductCode() {
            return productCode;
        }

        public void setProductCode(String productCode) {
            this.productCode = productCode;
        }

        public String getProductName() {
            return productName;
        }

        public void setProductName(String productName) {
            this.productName = productName;
        }

        public Number getSubmit() {
            return submit;
        }

        public void setSubmit(Number submit) {
            this.submit = submit;
        }

        public String getValidDate() {
            return validDate;
        }

        public void setValidDate(String validDate) {
            this.validDate = validDate;
        }

        @Override
        public String toString() {
            return "DetailVOList{" +
                    "id='" + id + '\'' +
                    ", batchNumber='" + batchNumber + '\'' +
                    ", manufacturer='" + manufacturer + '\'' +
                    ", movementNo='" + movementNo + '\'' +
                    ", produceTime='" + produceTime + '\'' +
                    ", productCode='" + productCode + '\'' +
                    ", productName='" + productName + '\'' +
                    ", submit='" + submit + '\'' +
                    ", validDate='" + validDate + '\'' +
                    '}';
        }


        public String getOutGoodsPositionCode() {
            return outGoodsPositionCode;
        }

        public void setOutGoodsPositionCode(String outGoodsPositionCode) {
            this.outGoodsPositionCode = outGoodsPositionCode;
        }

        public String getDownGoodsPositionCode() {
            return downGoodsPositionCode;
        }

        public void setDownGoodsPositionCode(String downGoodsPositionCode) {
            this.downGoodsPositionCode = downGoodsPositionCode;
        }

        public String getInGoodsPositionCode() {
            return inGoodsPositionCode;
        }

        public void setInGoodsPositionCode(String inGoodsPositionCode) {
            this.inGoodsPositionCode = inGoodsPositionCode;
        }
    }

    public List<DetailVOList> getDetailVOList() {
        return detailVOList;
    }

    public void setDetailVOList(List<DetailVOList> detailVOList) {
        this.detailVOList = detailVOList;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMovementNo() {
        return movementNo;
    }

    public void setMovementNo(String movementNo) {
        this.movementNo = movementNo;
    }

    public String getOperateCnt() {
        return operateCnt;
    }

    public void setOperateCnt(String operateCnt) {
        this.operateCnt = operateCnt;
    }

    public String getTotalCnt() {
        return totalCnt;
    }

    public void setTotalCnt(String totalCnt) {
        this.totalCnt = totalCnt;
    }

    @Override
    public String toString() {
        return "ResultBean{" +
                "detailVOList=" + detailVOList +
                ", id='" + id + '\'' +
                ", movementNo='" + movementNo + '\'' +
                '}';
    }

}
