package com.xyy.wms.pda.bean.instorage.checkaccept

import java.io.Serializable

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/14
 */
data class SelectContainerBean (
    var id : Int,
    var containerCode: String,
    var containerType: Int,
    var busiType: Int,
    var locked: Int,
    var createUser: String,
    var lockedName: String,
    var createTime: Long,
    var updateUser: String,
    var updateTime: Long,
    var yn: Int,
    var containerNumber: Int,
    var containerName: String,
    var busiTypeName: String,
    var containerStatus: Int,
    var containerCodes: String,
    var dictName: String,
    var applicationCode: String,
    var saleNo: String,
    var orgCode: String,
    var orgName: String,
    var applicationType: Int,
    var applicationTypeName: String,
    var containerCodeList: String,
    var minBarCode: String,
    var maxBarCode: String,
    var colName: String,
    var colNameDesc: String,
    var warehouseCode: String,
    var buildingCode: String,
    var dictBaseVOList: String,
    var userName: String
) : Serializable
