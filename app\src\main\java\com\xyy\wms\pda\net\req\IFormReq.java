package com.xyy.wms.pda.net.req;


import com.xyy.wms.pda.utils.ParamsConvertUtil;

import java.util.Map;
import java.util.Set;

import okhttp3.FormBody;
import okhttp3.RequestBody;

/**
 * Created by lwj on 2020/3/31
 * <EMAIL>
 */

public interface IFormReq extends IPostReq {


    @Override
    default RequestBody getRequestBody() {
        Map<String, String> params = ParamsConvertUtil.convert(this);
        FormBody.Builder builder = new FormBody.Builder();
        Set<String> keySet = params.keySet();
        for (String key : keySet) {
            String value = params.get(key);
            if (value == null) {
                builder.addEncoded(key, "");
            } else {
                builder.addEncoded(key, value);
            }
        }
        return builder.build();
    }


}



