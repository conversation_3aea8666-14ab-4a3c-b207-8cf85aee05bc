package com.xyy.wms.pda.bean.purchaseexit;

import java.io.Serializable;

/**
 * created by  liangxing  on 2020-01-13.
 */
public class PurchaseExitBillBean implements Serializable {

    private String pickUpOrder;		//string	@mock=JHG201912130015 拣货单编号

    private int refundOrderStatus;		// number	@mock=2 拣货单状态 2 未执行 4 执行中

    private String buildingName; //建筑物


    public String getPickUpOrder() {
        return pickUpOrder;
    }

    public void setPickUpOrder(String pickUpOrder) {
        this.pickUpOrder = pickUpOrder;
    }

    public int getRefundOrderStatus() {
        return refundOrderStatus;
    }

    public void setRefundOrderStatus(int refundOrderStatus) {
        this.refundOrderStatus = refundOrderStatus;
    }

    public String getBuildingName() {
        return buildingName;
    }

    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }
}
