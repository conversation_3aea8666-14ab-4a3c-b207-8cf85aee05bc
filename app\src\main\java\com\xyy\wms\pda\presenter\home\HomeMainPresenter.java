package com.xyy.wms.pda.presenter.home;

import androidx.annotation.NonNull;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.home.PendingOrder;
import com.xyy.wms.pda.bean.user.UserInfoBean;
import com.xyy.wms.pda.bean.user.WarehouseResult;
import com.xyy.wms.pda.contract.home.HomeMainContract;
import com.xyy.wms.pda.helper.SimpleErrorConsumer;
import com.xyy.wms.pda.helper.SimpleSuccessConsumer;
import com.xyy.wms.pda.model.home.HomeMainModel;
import java.util.List;
import java.util.Map;
/**
 * 主页Presenter
 */
public class HomeMainPresenter extends BasePresenter<HomeMainContract.IHomeMainModel, HomeMainContract.IHomeMainView> {

    @NonNull
    public static HomeMainPresenter newInstance() {
        return new HomeMainPresenter();
    }

    @Override
    protected HomeMainContract.IHomeMainModel getModel() {
        return HomeMainModel.newInstance();
    }

    public void getPendingOrderList(Map<String, String> map) {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.getPendingOrderList(map).subscribe(
                new SimpleSuccessConsumer<BaseResponseBean<PendingOrder>>(mIView, "加载中...") {
                    @Override
                    public void onSuccess(BaseResponseBean<PendingOrder> bean) {
                        mIView.showPendingOrderList(bean.getResult());
                    }
                }, new SimpleErrorConsumer(mIView)));
    }

    public void getChooseStoreList() {
        if (mIView == null || mIModel == null)
            return;
        mRxManager.register(mIModel.getChooseStoreList().subscribe(
            new SimpleSuccessConsumer<BaseResponseBean<List<WarehouseResult>>>(mIView) {
                    @Override
                    public void onSuccess(BaseResponseBean<List<WarehouseResult>> baseResponseBean) {
                        mIView.getChooseStoreListSuccess(baseResponseBean.getResult());
                    }
                }, new SimpleErrorConsumer(mIView)));
    }

    public void commitChooseStore(String storageCode) {
        if (mIView == null || mIModel == null)
            return;
        mRxManager.register(mIModel.commitChooseStore(storageCode).subscribe(
            new SimpleSuccessConsumer<BaseResponseBean<Object>>(mIView) {
                    @Override
                    public void onSuccess(BaseResponseBean<Object> baseResponseBean) {
                        mIView.commitChooseStoreSuccess(baseResponseBean);
                    }
                }, new SimpleErrorConsumer(mIView)));
    }
}
