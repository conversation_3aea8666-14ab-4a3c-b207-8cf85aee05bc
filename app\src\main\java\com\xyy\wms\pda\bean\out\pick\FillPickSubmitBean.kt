package com.xyy.wms.pda.bean.out.pick

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

/**
 * 补拣提交参数
 */
@Parcelize
data class FillPickSubmitBean(
        // 原货位信息
        var originalAllocationCode: String?,//原货位分配单号
        var originalBatchNumber: String?,//原货位批号        ++
        var originalSterilizingBatchNumber: String?,//原货位灭菌批号
        var originalBuildingCode: String?,//原货位建筑物     +
        var originalChannelCode: String?,//原货位业务类型编码
        var originalOrderCode: String?,//原货位出库单号      +
        var originalOrgCode: String?,//原货位机构编码        +
        var originalOwnerCode: String?,//原货位业主编码       +
        var originalProductionDate: String?,//原货位生产日期  改
        var originalProductCode: String?,//原货位商品编码
        var originalSoldOut: String?,//原货位编码
        var originalStorageAreaCode: String?,//原货位库区    +
        var originalStorageRoomCode: String?,//原货位库房    +
        var originalStorageTypeCode: String?,//原货位库别    +
        var originalWarehouseCode: String?,//原货位仓库编码   +
        var originalValidityDate: String?,//原货位有效期至
        var batchInspectionCode: String?,//批拣单号
        var passBoxCode: String?,//周转箱号
        var pickingNumber: Int,//计划数量
        var createTime: String?,//波次创建时间                +
        var filllogId: Long = 0,// 补拣日志表id               +
        var pid: Long = 0,// 任务明细表id                     +

        // 新货位信息
        var amountUse: Int,// 可用库存
        var batchNumber: String?,//	批号
        var sterilizingBatchNumber: String?,//	灭菌批号
        var buildingCode: String?,//建筑物
        var channelCode: String?,//	业务类型	string	@mock=2
        var goodsAllocation: String?,//	货位	string	@mock=LDZ02-05
        var orgCode: String?,//	机构	string	@mock=D1375
        var ownerCode: String?,//	业主	string	@mock=009
        var productCode: String?,//	商品编码	string	@mock=Y3001006
        var productName: String?,//	商品名称	string	@mock=午时茶颗粒
        var productionDate: String?,//	生产日期	string	@mock=2019-01-15
        var specification: String?,//	件包装	string	@mock=60
        var storageAreaCode: String?,//	库区	string	@mock=LDZ
        var storageRoomCode: String?,//	库房	string	@mock=LDK
        var storageStatus: String?,//		number	@mock=1
        var storageTypeCode: String?,//	库别	string	@mock=ZJK
        var validityDate: String?,//	有效期	string	@mock=2022-01-15
        var warehouseCode: String?,//	仓库

        // 补拣信息
        var fillPickNumber: Int // 补拣数量
) : Parcelable
