package com.xyy.wms.pda.model.moveStorage.wholeshelfdown;

import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.common.CheckContainerCodeBean;
import com.xyy.wms.pda.bean.common.CheckContainerCodeResult;

import java.util.List;

import io.reactivex.Observable;

/**
 * 通过托盘位获取数据 || 检查托盘位
 */
public interface CheckContainerCodeModel {

    Observable<BaseResponseBean<Boolean>> checkContainerCode(CheckContainerCodeBean containerCode);
}
