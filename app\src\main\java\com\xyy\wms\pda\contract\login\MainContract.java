package com.xyy.wms.pda.contract.login;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.home.HomeListBean;
import com.xyy.wms.pda.bean.home.HomeListBeanNew;
import com.xyy.wms.pda.bean.update.VersionBeanInfo;
import com.xyy.wms.pda.bean.update.VersionInfo;

import java.util.List;

import io.reactivex.Observable;

public interface MainContract {

    interface IMainModel extends IBaseModel {
        /**
         * 请求模块列表
         * @return
         */
        Observable<BaseResponseBean<HomeListBeanNew>> getModules();
        /**
         * 更新请求
         *
         * @return
         */
        Observable<BaseResponseBean<VersionInfo>> checkUpdate(VersionBeanInfo versionBeanInfo);
    }

    interface IMainView extends IBaseActivity {
        /**
         * 显示模块列表
         */
        void showModuleList(HomeListBeanNew list);
        /**
         * 隐藏菜单
         */
        void dismissMenuLayout();
        /**
         * 检测更新成功
         */
        void checkUpdateSuccess(VersionInfo versionInfo);
    }
}
