<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@color/white">

    <!-- 标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?attr/colorPrimary"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:src="@drawable/ic_arrow_back_white"
            android:contentDescription="@string/app_name" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="异常图片"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_submit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:text="提交"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:padding="8dp" />

    </RelativeLayout>

    <!-- 业务信息区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@color/background_gray">

        <TextView
            android:id="@+id/tv_business_code"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="业务单号：XXX-2024-001"
            android:textColor="@color/text_color_333333"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_related_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="相关信息：商品编码等"
            android:textColor="@color/text_color_666666"
            android:textSize="12sp" />

    </LinearLayout>

    <!-- 图片上传区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="异常图片（最多可以传五张）"
            android:textColor="@color/text_color_333333"
            android:textSize="14sp"
            android:layout_marginBottom="16dp" />

        <!-- 图片网格 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_pictures"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp" />

    </LinearLayout>

</LinearLayout>
