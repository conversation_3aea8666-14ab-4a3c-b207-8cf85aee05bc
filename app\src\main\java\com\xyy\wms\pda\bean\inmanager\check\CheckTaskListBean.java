package com.xyy.wms.pda.bean.inmanager.check;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zcj on 2018/11/9 16
 * 盘点任务列表 初盘|复盘|终盘通用
 */
public class CheckTaskListBean implements Serializable {

//    alreadyCheckSum		number	@mock=0 已盘点数量
//    checkTaskNo		    string	盘点任务单号
//    detailVOList		array<object>	盘点商品列表
//    batchNumber		    string	商品批号
//    firstCheckNum		number	初盘数量
//    goodsPositionCode	string	商品货位
//    id		            number	商品id
//    isCheck		        number	是否已盘点 0 未盘点1已盘点
//    manufacturer		string	厂家名称
//    overflowingDesc		string	损溢原因
//    overflowingNum		number	损溢数量
//    packageNum		    number	包装规格
//    produceTime		    number	生存日期
//    productCode		    string	商品编码
//    productName		    string	商品名称
//    secondCheckNum		number	复盘数量
//    specifications		string	规格
//    stockNum		    number	货位数量
//    thirdCheckNum		number	终盘数量
//    validDate		    number	有效期至
//    notCheckSum		    number	@mock=6 未盘点数量

    private int alreadyCheckSum;
    private String checkTaskNo;
    private int notCheckSum;
    private String checkWay;
    private List<DetailVoList> detailVOList;

    public static class DetailVoList implements Serializable {
        private String batchNumber;
        private String firstCheckNum;
        private String goodsPositionCode;
        private String id;
        private String isCheck;
        private String manufacturer;
        private String overflowingDesc;
        private String overflowingNum;
        private int packageNum;
        private String produceTime;
        private String productCode;
        private String productName;
        private String secondCheckNum;
        private String specifications;
        private int stockNum;
        private String thirdCheckNum;
        private String validDate;
        private String packingUnit;
        private String storageTypeCode;

        public String getPackingUnit() {
            return packingUnit;
        }

        public void setPackingUnit(String packingUnit) {
            this.packingUnit = packingUnit;
        }

        public String getStorageTypeCode() {
            return storageTypeCode;
        }

        public void setStorageTypeCode(String storageTypeCode) {
            this.storageTypeCode = storageTypeCode;
        }

        public String getBatchNumber() {
            return batchNumber;
        }

        public void setBatchNumber(String batchNumber) {
            this.batchNumber = batchNumber;
        }

        public String getFirstCheckNum() {
            return firstCheckNum;
        }

        public void setFirstCheckNum(String firstCheckNum) {
            this.firstCheckNum = firstCheckNum;
        }

        public String getGoodsPositionCode() {
            return goodsPositionCode;
        }

        public void setGoodsPositionCode(String goodsPositionCode) {
            this.goodsPositionCode = goodsPositionCode;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getIsCheck() {
            return isCheck;
        }

        public void setIsCheck(String isCheck) {
            this.isCheck = isCheck;
        }

        public String getManufacturer() {
            return manufacturer;
        }

        public void setManufacturer(String manufacturer) {
            this.manufacturer = manufacturer;
        }

        public String getOverflowingDesc() {
            return overflowingDesc;
        }

        public void setOverflowingDesc(String overflowingDesc) {
            this.overflowingDesc = overflowingDesc;
        }

        public String getOverflowingNum() {
            return overflowingNum;
        }

        public void setOverflowingNum(String overflowingNum) {
            this.overflowingNum = overflowingNum;
        }

        public int getPackageNum() {
            return packageNum;
        }

        public void setPackageNum(int packageNum) {
            this.packageNum = packageNum;
        }

        public String getProduceTime() {
            return produceTime;
        }

        public void setProduceTime(String produceTime) {
            this.produceTime = produceTime;
        }

        public String getProductCode() {
            return productCode;
        }

        public void setProductCode(String productCode) {
            this.productCode = productCode;
        }

        public String getProductName() {
            return productName;
        }

        public void setProductName(String productName) {
            this.productName = productName;
        }

        public String getSecondCheckNum() {
            return secondCheckNum;
        }

        public void setSecondCheckNum(String secondCheckNum) {
            this.secondCheckNum = secondCheckNum;
        }

        public String getSpecifications() {
            return specifications;
        }

        public void setSpecifications(String specifications) {
            this.specifications = specifications;
        }

        public int getStockNum() {
            return stockNum;
        }

        public void setStockNum(int stockNum) {
            this.stockNum = stockNum;
        }

        public String getThirdCheckNum() {
            return thirdCheckNum;
        }

        public void setThirdCheckNum(String thirdCheckNum) {
            this.thirdCheckNum = thirdCheckNum;
        }

        public String getValidDate() {
            return validDate;
        }

        public void setValidDate(String validDate) {
            this.validDate = validDate;
        }

        @Override
        public String toString() {
            return "DetailVoList{" +
                    "batchNumber='" + batchNumber + '\'' +
                    ", firstCheckNum='" + firstCheckNum + '\'' +
                    ", goodsPositionCode='" + goodsPositionCode + '\'' +
                    ", id='" + id + '\'' +
                    ", isCheck='" + isCheck + '\'' +
                    ", manufacturer='" + manufacturer + '\'' +
                    ", overflowingDesc='" + overflowingDesc + '\'' +
                    ", overflowingNum='" + overflowingNum + '\'' +
                    ", packageNum='" + packageNum + '\'' +
                    ", produceTime='" + produceTime + '\'' +
                    ", productCode='" + productCode + '\'' +
                    ", productName='" + productName + '\'' +
                    ", secondCheckNum='" + secondCheckNum + '\'' +
                    ", specifications='" + specifications + '\'' +
                    ", stockNum='" + stockNum + '\'' +
                    ", thirdCheckNum='" + thirdCheckNum + '\'' +
                    ", validDate='" + validDate + '\'' +
                    ", packingUnit='" + packingUnit + '\'' +
                    ", storageTypeCode='" + storageTypeCode + '\'' +
                    '}';
        }
    }


    public String getCheckTaskNo() {
        return checkTaskNo;
    }

    public void setCheckTaskNo(String checkTaskNo) {
        this.checkTaskNo = checkTaskNo;
    }

    public int getAlreadyCheckSum() {
        return alreadyCheckSum;
    }

    public void setAlreadyCheckSum(int alreadyCheckSum) {
        this.alreadyCheckSum = alreadyCheckSum;
    }

    public int getNotCheckSum() {
        return notCheckSum;
    }

    public void setNotCheckSum(int notCheckSum) {
        this.notCheckSum = notCheckSum;
    }

    public List<DetailVoList> getDetailVOList() {
        return detailVOList;
    }

    public void setDetailVOList(List<DetailVoList> detailVOList) {
        this.detailVOList = detailVOList;
    }

    public String getCheckWay() {
        return checkWay;
    }

    public void setCheckWay(String checkWay) {
        this.checkWay = checkWay;
    }

    @Override
    public String toString() {
        return "CheckTaskListBean{" +
                "alreadyCheckSum=" + alreadyCheckSum +
                ", checkTaskNo='" + checkTaskNo + '\'' +
                ", notCheckSum=" + notCheckSum +
                ", checkWay='" + checkWay + '\'' +
                ", detailVOList=" + detailVOList +
                '}';
    }
}
