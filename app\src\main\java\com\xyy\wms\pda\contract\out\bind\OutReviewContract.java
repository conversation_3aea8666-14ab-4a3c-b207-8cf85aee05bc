package com.xyy.wms.pda.contract.out.bind;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.presenter.out.bind.OutReviewPresenter;

import java.util.Map;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 04/10/2019 14:59
 */
public interface OutReviewContract {

    interface IOutReviewModel extends IBaseModel {
        Observable<BaseResponseBean<String>> saveBoxCode(Map<String, Object> params);

    }

    interface IOutReviewView extends IBaseActivity {
        void saveBoxCodeSuccess(OutReviewPresenter.RequestType type);

        void saveBoxCodeFail(OutReviewPresenter.RequestType type, int code, String msg);
    }

}
