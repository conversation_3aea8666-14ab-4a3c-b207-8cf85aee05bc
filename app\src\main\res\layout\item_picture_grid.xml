<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="80dp"
    android:layout_height="80dp"
    android:layout_margin="5dp">

    <!-- 图片显示区域 -->
    <ImageView
        android:id="@+id/iv_picture"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_picture_placeholder"
        android:scaleType="centerCrop"
        android:contentDescription="@string/app_name" />

    <!-- 添加图片的加号图标 -->
    <ImageView
        android:id="@+id/iv_add_icon"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_centerInParent="true"
        android:src="@drawable/ic_add_picture"
        android:visibility="visible"
        android:contentDescription="@string/app_name" />

    <!-- 删除按钮 -->
    <ImageView
        android:id="@+id/iv_delete"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="-5dp"
        android:layout_marginEnd="-5dp"
        android:background="@drawable/shape_delete_circle"
        android:src="@drawable/ic_delete_white"
        android:visibility="gone"
        android:contentDescription="@string/app_name" />

    <!-- 上传进度指示器 -->
    <ProgressBar
        android:id="@+id/pb_upload"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_centerInParent="true"
        android:visibility="gone" />

    <!-- 上传状态图标 -->
    <ImageView
        android:id="@+id/iv_upload_status"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_alignParentBottom="true"
        android:layout_alignParentEnd="true"
        android:layout_marginBottom="5dp"
        android:layout_marginEnd="5dp"
        android:visibility="gone"
        android:contentDescription="@string/app_name" />

</RelativeLayout>
