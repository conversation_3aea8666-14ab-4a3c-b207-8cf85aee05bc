package com.xyy.wms.pda.model.out.exception;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.utilslibrary.utils.JsonUtils;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.exception.MoreGoods;
import com.xyy.wms.pda.bean.exception.RushRed;
import com.xyy.wms.pda.bean.out.DictTypeBean;
import com.xyy.wms.pda.bean.out.RushRedBean;
import com.xyy.wms.pda.contract.out.exception.ExceptionTaskBillContract;
import com.xyy.wms.pda.model.ServiceModel;

import java.util.List;
import java.util.Map;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 04/12/2019 18:02
 */
public class ExceptionTaskBillModel extends ServiceModel implements ExceptionTaskBillContract.IExceptionTaskBillModel {

    public static ExceptionTaskBillModel newInstance() {
        return new ExceptionTaskBillModel();
    }

    @Override
    public Observable<BaseResponseBean> verifyPicking(Map<String, String> map) {
        return getApiOutManagerService().verifyPicking(map).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean> confirmRushRed(RushRedBean rushRed) {
        return getApiOutManagerService().confirmRushRed(rushRed).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean> reviewMoreGoods(Map<String, String> map) {
        return getApiOutManagerService().reviewMoreGoods(map).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<List<DictTypeBean>>> getByDictType(Map<String, String> map) {
        return getApiOutManagerService().getByDictType(map).compose(RxHelper.rxSchedulerHelper());
    }
}
