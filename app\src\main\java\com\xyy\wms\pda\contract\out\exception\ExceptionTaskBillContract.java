package com.xyy.wms.pda.contract.out.exception;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.exception.MoreGoods;
import com.xyy.wms.pda.bean.exception.RushRed;
import com.xyy.wms.pda.bean.out.DictTypeBean;
import com.xyy.wms.pda.bean.out.RushRedBean;

import java.util.List;
import java.util.Map;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 04/12/2019 18:02
 */
public interface ExceptionTaskBillContract {

    interface IExceptionTaskBillModel extends IBaseModel {
        Observable<BaseResponseBean> verifyPicking(Map<String, String> map);
        Observable<BaseResponseBean> confirmRushRed(RushRedBean rushRed);
        Observable<BaseResponseBean> reviewMoreGoods(Map<String, String> map);
        Observable<BaseResponseBean<List<DictTypeBean>>> getByDictType(Map<String, String> map);
    }

    interface IExceptionTaskBillView extends IBaseActivity {
        void verifyPickingSuccess(BaseResponseBean baseResponseBean, int processType);
        void confirmRushRedSuccess(BaseResponseBean baseResponseBean);
        void reviewMoreGoodsSuccess(BaseResponseBean baseResponseBean);
        void getByDictTypeSuccess(BaseResponseBean<List<DictTypeBean>> dictTypeBean);
    }

}
