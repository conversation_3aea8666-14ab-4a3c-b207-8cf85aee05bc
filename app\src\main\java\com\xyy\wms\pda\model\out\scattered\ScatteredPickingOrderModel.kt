package com.xyy.wms.pda.model.out.scattered

import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.out.ScatteredPickingListBean
import com.xyy.wms.pda.bean.out.SubmitResult
import com.xyy.wms.pda.bean.out.pick.PickDetailSubmitBean
import com.xyy.wms.pda.bean.out.pick.RemindTask
import com.xyy.wms.pda.bean.out.pick.ScatteredPickingOrderBean
import com.xyy.wms.pda.contract.out.scattered.ScatteredPickingOrderContract
import com.xyy.wms.pda.model.ServiceModel
import io.reactivex.Observable

/**
 * 出库——拆零拣货明细提交
 */
class ScatteredPickingOrderModel : ServiceModel(), ScatteredPickingOrderContract.IScatteredPickingOrderModel {

    override fun getScatteredPickingList(map: Map<String, String?>): Observable<BaseResponseBean<List<ScatteredPickingListBean>>> {
        return apiOutManagerService.getScatteredPickingList(map).compose(RxHelper.rxSchedulerHelper())
    }

    override fun getScatteredPickingOrder(map: Map<String, Any?>): Observable<BaseResponseBean<List<ScatteredPickingOrderBean>>> {
        return apiOutManagerService.getScatteredPickingOrder(map).compose(RxHelper.rxSchedulerHelper())
    }

    override fun submitScatteredPickingOrder(body: PickDetailSubmitBean): Observable<BaseResponseBean<SubmitResult>> {
        return apiOutManagerService.submitScatteredPickingOrder(body).compose(RxHelper.rxSchedulerHelper())
    }

    override fun getReminderTaskList(map: Map<String, String>): Observable<BaseResponseBean<List<RemindTask>>> {
        return apiOutManagerService.getReminderTaskList(map).compose(RxHelper.rxSchedulerHelper())
    }

    companion object {
        @JvmStatic
        fun newInstance(): ScatteredPickingOrderModel {
            return ScatteredPickingOrderModel()
        }
    }
}
