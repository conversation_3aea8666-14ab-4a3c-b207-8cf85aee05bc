package com.xyy.wms.pda.bean.inmanager.post;

/**
 * Created by zcj on 2018/11/12 17
 */
public class MovementDownPostBean {

    private String containerCode;// 容器编号
    private String id;// 主键
    private String movePackNum;// 移库件数
    private String moveScatteredNum;// 移库零散数
    private String packageNum;// 包装数量
    private String storageTypeCode;// 库别编码
    private String downGoodsPositionCode;//扫码下架货位

    private String buildingCode;    //建筑物编码	string
    private String channelCode;    //业务类型编码	string
    private String orgCode;    //机构码	string
    private String ownerCode;//	业主编码	string
    private String storageAreaCode;//	库区编码	string
    private String storageRoomCode;    //库房编码	string
    private String warehouseCode;    //仓库编码	string

    public String getBuildingCode() {
        return buildingCode;
    }

    public void setBuildingCode(String buildingCode) {
        this.buildingCode = buildingCode;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getStorageAreaCode() {
        return storageAreaCode;
    }

    public void setStorageAreaCode(String storageAreaCode) {
        this.storageAreaCode = storageAreaCode;
    }

    public String getStorageRoomCode() {
        return storageRoomCode;
    }

    public void setStorageRoomCode(String storageRoomCode) {
        this.storageRoomCode = storageRoomCode;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public void setContainerCode(String containerCode) {
        this.containerCode = containerCode;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMovePackNum() {
        return movePackNum;
    }

    public void setMovePackNum(String movePackNum) {
        this.movePackNum = movePackNum;
    }

    public String getMoveScatteredNum() {
        return moveScatteredNum;
    }

    public void setMoveScatteredNum(String moveScatteredNum) {
        this.moveScatteredNum = moveScatteredNum;
    }

    public String getPackageNum() {
        return packageNum;
    }

    public void setPackageNum(String packageNum) {
        this.packageNum = packageNum;
    }

    public String getStorageTypeCode() {
        return storageTypeCode;
    }

    public void setStorageTypeCode(String storageTypeCode) {
        this.storageTypeCode = storageTypeCode;
    }

    public String getDownGoodsPositionCode() {
        return downGoodsPositionCode;
    }

    public void setDownGoodsPositionCode(String downGoodsPositionCode) {
        this.downGoodsPositionCode = downGoodsPositionCode;
    }

    @Override
    public String toString() {
        return "MovementDownPostBean{" +
                "containerCode='" + containerCode + '\'' +
                ", id='" + id + '\'' +
                ", movePackNum='" + movePackNum + '\'' +
                ", moveScatteredNum='" + moveScatteredNum + '\'' +
                ", packageNum='" + packageNum + '\'' +
                ", storageTypeCode='" + storageTypeCode + '\'' +
                '}';
    }
}
