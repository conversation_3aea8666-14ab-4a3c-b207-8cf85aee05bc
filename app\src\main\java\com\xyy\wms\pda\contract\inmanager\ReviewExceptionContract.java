package com.xyy.wms.pda.contract.inmanager;


import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.inmanager.ExceptionCountBean;

import java.util.List;

import io.reactivex.Observable;

public interface ReviewExceptionContract {

    abstract class ReviewExceptionPresenter extends BasePresenter<ReviewExceptionContract.IReviewExceptionModel, ReviewExceptionContract.IReviewExceptionView> {

        /**
         * 获取异常处理 内复核异常界面 少货、多货、错货 数量
         */
        public abstract void exceptionCount(String exceptionType);
    }

    interface IReviewExceptionModel extends IBaseModel {

        Observable<BaseResponseBean<List<ExceptionCountBean>>> exceptionCount(String exceptionType);

    }

    interface IReviewExceptionView extends IBaseActivity {
        /**
         * 获取数据成功
         */
        void exceptionCountSuccess(BaseResponseBean<List<ExceptionCountBean>> bean);

        /**
         * 显示网络错误
         */
        void showNetworkError();

    }
}
