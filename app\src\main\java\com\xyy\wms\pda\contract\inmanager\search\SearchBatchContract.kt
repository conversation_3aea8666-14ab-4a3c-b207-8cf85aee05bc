package com.xyy.wms.pda.contract.inmanager.search

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.inmanager.search.SearchGoodPositionBean
import io.reactivex.Observable

/**
 * Created by XyyMvpSportTemplate on 03/26/2019 19:11
 */
interface SearchBatchContract {
  interface ISearchBatchModel : IBaseModel {
    fun findGoodsPositionCode(batchNumber: String?, productCode: String?): Observable<BaseResponseBean<List<SearchGoodPositionBean>>>
  }

  interface ISearchBatchView : IBaseActivity {
    fun findGoodsPositionCodeSuccess(baseBean: List<SearchGoodPositionBean>)
  }
}
