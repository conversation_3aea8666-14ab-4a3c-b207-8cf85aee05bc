package com.xyy.wms.pda.presenter.instorage.scatteredwholeshelf

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.common.DictParam
import com.xyy.wms.pda.bean.common.DictParamResult
import com.xyy.wms.pda.bean.instorage.BatchCodeResultBean
import com.xyy.wms.pda.bean.instorage.shelf.CheckSelectGoodsPositionBean
import com.xyy.wms.pda.bean.instorage.shelf.CommitShelfResult
import com.xyy.wms.pda.bean.instorage.shelf.ShelfDetailPost
import com.xyy.wms.pda.helper.SimpleErrorConsumer
import com.xyy.wms.pda.helper.SimpleSuccessConsumer
import com.xyy.wms.pda.model.instorage.scatteredshelf.ScatteredShelfProductModel
import com.xyy.wms.pda.presenter.checkSelectGoodsPosition.instorage.CheckSelectGoodsPositionPresenter
import com.xyy.wms.pda.bean.req.CheckGoodsPositionReq
import com.xyy.wms.pda.ui.activity.instorage.scatteredwholeshelf.InScatteredWholeProductActivity

/**
 * 库内  零散商品上架
 */
class ScatteredWholeShelfProductPresenter : BasePresenter<ScatteredShelfProductModel, InScatteredWholeProductActivity>(), CheckSelectGoodsPositionPresenter {

    override fun getModel(): ScatteredShelfProductModel {
        return ScatteredShelfProductModel.newInstance()
    }

    override fun checkSelectGoodsPosition(req: CheckGoodsPositionReq) {
        mRxManager.register(mIModel.checkSelectGoodsPosition(req).subscribe(object : SimpleSuccessConsumer<BaseResponseBean<CheckSelectGoodsPositionBean>>(mIView) {
            override fun onSuccess(baseResponseBean: BaseResponseBean<CheckSelectGoodsPositionBean>) {
                mIView.checkSelectGoodsPositionSuccess(baseResponseBean)
            }
        }, SimpleErrorConsumer(mIView)))
    }

    /**
     * 单个明细提交
     */
    fun commitStorageOrderDetail(shelfDetailPost: ShelfDetailPost) {
        mRxManager.register(mIModel.commitStorageOrderDetail(shelfDetailPost)
                .subscribe(object : SimpleSuccessConsumer<BaseResponseBean<CommitShelfResult>>(mIView) {

                    override fun onSuccess(baseResponseBean: BaseResponseBean<CommitShelfResult>) {
                        mIView.commitStorageOrderDetailSuccess(baseResponseBean)
                    }
                }, SimpleErrorConsumer(mIView)))

    }

//  public void getBatchCodes(String productCode){
//    mRxManager.register(mIModel.getBatchCodes(productCode).subscribe(new com.xyy.wms.pda.net_helper.SimpleSuccessConsumer<BaseResponseBean<String[]>>() {
//      @Override
//      public void onSuccess(@Nullable BaseResponseBean<String[]> baseResponseBean) {
//        mIView.getBatchCodesSuccess(baseResponseBean.getResult());
//      }
//    }, new SimpleErrorConsumer(mIView)));
//  }
  /**
   * 获取商品批号
   */
    fun getBatchCodes(productCode:String){
      mRxManager.register(mIModel.getBatchCodes(productCode)
        .subscribe(object : SimpleSuccessConsumer<BaseResponseBean<List<BatchCodeResultBean>>>(mIView) {

          override fun onSuccess(baseResponseBean: BaseResponseBean<List<BatchCodeResultBean>>) {
            mIView.getBatchCodesSuccess(baseResponseBean.getResult())
          }
        }, SimpleErrorConsumer(mIView)))
    }
    companion object {
        fun newInstance(): ScatteredWholeShelfProductPresenter {
            return ScatteredWholeShelfProductPresenter()
        }
    }
    //批号开关
    fun getDictParamList(dictParam: DictParam) {
      mRxManager.register(mIModel.getDictParamList(dictParam)
        .subscribe(object : SimpleSuccessConsumer<BaseResponseBean<DictParamResult>>(mIView) {

          override fun onSuccess(baseResponseBean: BaseResponseBean<DictParamResult>) {
            mIView.getDictParamListSuccess(baseResponseBean.getResult())
          }
        }, SimpleErrorConsumer(mIView)))
    }
}
