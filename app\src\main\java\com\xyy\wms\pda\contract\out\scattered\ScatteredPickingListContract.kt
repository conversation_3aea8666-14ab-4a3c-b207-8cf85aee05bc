package com.xyy.wms.pda.contract.out.scattered

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.out.PickingTaskListConfirmBean
import com.xyy.wms.pda.bean.out.ScatteredPickingListBean
import com.xyy.wms.pda.bean.out.SubmitResult
import com.xyy.wms.pda.bean.out.pick.ReviewStageListBean
import io.reactivex.Observable

/**
 * 出库——拆零拣货列表
 */
interface ScatteredPickingListContract {
  interface IScatteredPickingListModel : IBaseModel {
    /**
     * 获取拆零拣货列表
     */
    fun getScatteredPickingList(map: Map<String, String?>): Observable<BaseResponseBean<List<ScatteredPickingListBean>>>

    /**
     * 提交拆零拣货列表
     *
     * @param bean
     * @return
     */
    fun submitPickingList(bean: PickingTaskListConfirmBean?): Observable<BaseResponseBean<SubmitResult>>

    /**
     * 复核台信息
     */
    fun getReviewStageInfo(map: Map<String, String>): Observable<BaseResponseBean<ReviewStageListBean>>
  }

  interface IScatteredPickingListView : IBaseActivity {
    /**
     * 获取拆零拣货列表成功
     */
    fun getScatteredPickingListSuccess(bean: List<ScatteredPickingListBean>)

    /**
     * 提交拆零拣货列表成功
     */
    fun submitPickingListSuccess(submitResult: SubmitResult)
    /**
     * 复核台信息成功
     */
    fun getReviewStageInfoSuccess(reviewStageListBean: ReviewStageListBean?, allComplete: Boolean)
  }
}
