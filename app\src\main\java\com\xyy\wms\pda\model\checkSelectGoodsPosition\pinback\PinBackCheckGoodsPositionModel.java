package com.xyy.wms.pda.model.checkSelectGoodsPosition.pinback;

import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.pinback.ProductPosition;
import com.xyy.wms.pda.bean.pinback.ProductPositionListBean;

import io.reactivex.Observable;

/**
 * author :lx
 * date 2018/12/10.
 * email： <EMAIL>
 * 销退的货位校验
 */
public interface PinBackCheckGoodsPositionModel {

    Observable<BaseResponseBean> checkSelectGoodsPosition(ProductPositionListBean<ProductPosition> productPositionListBean);


}
