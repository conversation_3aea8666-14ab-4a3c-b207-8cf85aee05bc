package com.xyy.wms.pda.ui.adapter.instorage.checkaccept

import android.widget.ImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.xyy.wms.pda.R
import com.xyy.wms.pda.bean.instorage.checkaccept.LogicalRegionProductInfoMaintainBean


class CommodityMaintenanceAdapter : BaseQuickAdapter<LogicalRegionProductInfoMaintainBean, BaseViewHolder>(R.layout.item_commodity_maintenance) {
    override fun convert(helper: BaseViewHolder?, item: LogicalRegionProductInfoMaintainBean?) {
        val ivChooseItem = helper?.getView<ImageView>(R.id.iv_choose_item)
        if (item?.isSelected!!) {
            ivChooseItem?.setImageResource(R.mipmap.icon_create_selected)
        } else {
            ivChooseItem?.setImageResource(R.mipmap.icon_create_un_selected)
        }

        helper?.setText(R.id.tv_commodity_name, item.dictName)
    }
}
