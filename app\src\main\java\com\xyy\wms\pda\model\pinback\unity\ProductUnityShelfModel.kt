package com.xyy.wms.pda.model.pinback.unity

import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.api.ApiPinBackService
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.pinback.*
import com.xyy.wms.pda.model.checkSelectGoodsPosition.pinback.PinBackCheckGoodsPositionModel
import com.xyy.wms.pda.model.pinback.scatteredShelf.ProductScatteredShelfModel
import com.xyy.wms.pda.net.RetrofitCreateHelper
import io.reactivex.Observable

class ProductUnityShelfModel :  PinBackCheckGoodsPositionModel {

    fun newInstance(): ProductScatteredShelfModel {
        return ProductScatteredShelfModel()
    }

    override fun checkSelectGoodsPosition(productPositionListBean: ProductPositionListBean<ProductPosition>): Observable<BaseResponseBean<*>> {
        return RetrofitCreateHelper.createApi(ApiPinBackService::class.java).checkSelectGoodsPosition(productPositionListBean)
                .compose(RxHelper.rxSchedulerHelper<BaseResponseBean<*>>())
    }

     fun commitProductShelfList(wholeShelfListBean: PostShelfListBean): Observable<BaseResponseBean<ShelfResult>> {
        return RetrofitCreateHelper.createApi(ApiPinBackService::class.java).commitShelfList(wholeShelfListBean)
                .compose(RxHelper.rxSchedulerHelper())
    }
}