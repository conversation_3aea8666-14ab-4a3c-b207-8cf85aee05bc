package com.xyy.wms.pda.api;

import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.exception.ExceptionHandleListBean;
import com.xyy.wms.pda.bean.exception.PickUseStorageBean;
import com.xyy.wms.pda.bean.exception.PickUseStorageBeanNew;
import com.xyy.wms.pda.bean.mine.WorkloadRankingBean;
import com.xyy.wms.pda.bean.out.DictTypeBean;
import com.xyy.wms.pda.bean.out.ExceptionBean;
import com.xyy.wms.pda.bean.out.PDAPartsTaskStatus;
import com.xyy.wms.pda.bean.out.PickingTaskListConfirmBean;
import com.xyy.wms.pda.bean.out.ReviewOutListBean;
import com.xyy.wms.pda.bean.out.ReviewOutOrderBean;
import com.xyy.wms.pda.bean.out.ReviewOutScanBean;
import com.xyy.wms.pda.bean.out.ReviewScanElectronicBean;
import com.xyy.wms.pda.bean.out.ReviewSupervisionCodeBean;
import com.xyy.wms.pda.bean.out.ReviewSupervisionProductBean;
import com.xyy.wms.pda.bean.out.RushRedBean;
import com.xyy.wms.pda.bean.out.ScatteredPickingListBean;
import com.xyy.wms.pda.bean.out.SubmitResult;
import com.xyy.wms.pda.bean.out.bind.CheckResult;
import com.xyy.wms.pda.bean.out.bind.ConsolidationResult;
import com.xyy.wms.pda.bean.out.bind.ContainerCheckResult;
import com.xyy.wms.pda.bean.out.outsideReview.Building;
import com.xyy.wms.pda.bean.out.pick.FillPickSubmitBean;
import com.xyy.wms.pda.bean.out.pick.GetTaskBean;
import com.xyy.wms.pda.bean.out.pick.GoodsStockBean;
import com.xyy.wms.pda.bean.out.pick.IsHasTask;
import com.xyy.wms.pda.bean.out.pick.ItemTaskBean;
import com.xyy.wms.pda.bean.out.pick.PickDetailSubmitBean;
import com.xyy.wms.pda.bean.out.pick.PickTaskAfterBoxResult;
import com.xyy.wms.pda.bean.out.pick.RemindTask;
import com.xyy.wms.pda.bean.out.pick.ReviewStageListBean;
import com.xyy.wms.pda.bean.out.pick.ScatteredPickingOrderBean;
import com.xyy.wms.pda.bean.out.pick.VerifyStatusPassBoxBean;
import com.xyy.wms.pda.bean.out.storegoods.StoreGoodsInfo;
import com.xyy.wms.pda.bean.out.storegoods.StoreGoodsPost;
import com.xyy.wms.pda.net.RequestParams;

import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
import retrofit2.http.Body;
import retrofit2.http.Field;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;

/**
 * 出库相关请求
 */
public interface ApiOutManagerService {

    // 出库——拆零拣货

    /**
     * 领取任务 —— 领取任务和温馨提醒
     */
    @GET("PickingTaskPdaController/getcontinfo")
    Observable<BaseResponseBean<ItemTaskBean>> getItemTaskNumber();

    /**
     * 领取任务 —— 任务索取
     */
    @GET("PickingTaskPdaController/HandlerPartsGoodsTaskAllPad")
    Observable<BaseResponseBean<GetTaskBean>> getTask(@Query("jobNumber") String jobNumber);
    /**
     * 出库 —— 领取任务是否有拣货任务
     */
    @GET("PickingTaskPdaController/isforTask")
    Observable<BaseResponseBean<IsHasTask>> isHasPickTask(@Query("jobNumber") String jobNumber);

    /**
     * 出库 —— 周转箱合法性校验
     */
    @GET("PickingTaskPdaController/findStatusByPassBox")
    Observable<BaseResponseBean<VerifyStatusPassBoxBean>> verifyStatusPassBox(@QueryMap Map<String, String> map);

    /**
     * 继续拣货和明细提交按钮前判断
     */
    @GET("PartsPickingTaskPdaController/isPDAPartsTaskForFinished")
    Observable<BaseResponseBean<PDAPartsTaskStatus>> isPDAPartsTaskForFinished(@Query("batchInspectionCode") String batchInspectionCode);

    /**
     * 出库 —— 只要有任务均需要获取箱子数据
     */
    @GET("PickingTaskPdaController/findPartPickingTaskList")
    Observable<BaseResponseBean<PickTaskAfterBoxResult>> getPickTaskAfterRelationBox(@QueryMap Map<String, Object> map);


    /**
     * 出库 —— 拆零拣货列表
     */
    @FormUrlEncoded
    @POST("PickingTaskPdaController/getPartsPickPdalist")
    Observable<BaseResponseBean<List<ScatteredPickingListBean>>> getScatteredPickingList(@FieldMap Map<String, String> map);

    /**
     * 出库 —— 拆零拣货全部拣货完成确认
     */
    @POST("PartsPickingTaskPdaController/getJudgeState")
    Observable<BaseResponseBean<SubmitResult>> submitScatteredPickingList(@Body PickingTaskListConfirmBean bean);

    /**
     * 出库 —— 拆零拣货明细
     */
    @FormUrlEncoded
    @POST("PartsPickingTaskPdaController/getPartsPickTaskDetail")
    Observable<BaseResponseBean<List<ScatteredPickingOrderBean>>> getScatteredPickingOrder(@FieldMap Map<String, Object> map);

    /**
     * 出库 —— 拆零拣货明细提交
     */
    @Headers(RequestParams.CONTENT_TYPE_JSON)
    @POST("PartsPickingTaskPdaController/updatePartsPickTaskDetail")
    Observable<BaseResponseBean<SubmitResult>> submitScatteredPickingOrder(@Body PickDetailSubmitBean params);

    /**
     * 出库 —— 获取复核台信息
     */
    @GET("PickingTaskPdaController/findReviewStageInfo")
    Observable<BaseResponseBean<ReviewStageListBean>> getReviewStageInfo(@QueryMap Map<String, String> map);

    /**
     * 拣货执行 —— 点击周转箱记录落位时间
     */
    @GET("PickingTaskPdaController/updateArriveTimeAndStatus")
    Observable<BaseResponseBean> recordSettingTime(@QueryMap Map<String, String> map);

    /**
     * 拣货执行 —— 催单任务
     */
    @FormUrlEncoded
    @POST("PartsPickingTaskPdaController/padReminderTask")
    Observable<BaseResponseBean<List<RemindTask>>> getReminderTaskList(@FieldMap Map<String, String> map);

    /**
     * 补拣 —— 获取补拣商品库存信息
     */
    @GET("storagePdaController/getGoodsReplenishPick")
    Observable<BaseResponseBean<List<GoodsStockBean>>> getGoodsStock(@QueryMap Map<String, Object> map);

    /**
     * 补拣 —— 提交补拣商品信息
     */
    @POST("PartsPickingTaskPdaController/padDealPartsException")
    Observable<BaseResponseBean> submitFillPickGoods(@Body FillPickSubmitBean bean);






    // 出库——外复合
    /**
     * 出库——外复合任务列表
     */
    @FormUrlEncoded
    @POST("partsOutReviewPdaController/getReviewOutListPda")
    Observable<BaseResponseBean<ReviewOutListBean>> getReviewOutList(@FieldMap Map<String, String> map);

    /**
     * 出库——外复合任务 - 扫描标签条码
     */
    @FormUrlEncoded
    @POST("partsOutReviewPdaController/scanningBoxPda")
    Observable<BaseResponseBean<ReviewOutScanBean>> reviewOutScan(@FieldMap Map<String, String> map);

    /**
     * 出库——外复合任务 - 扫描电子追溯码
     */
    @FormUrlEncoded
    @POST("partsOutReviewPdaController/scanningSupervisionCode")
    Observable<BaseResponseBean<ReviewScanElectronicBean>> reviewOutScanElectronic(@FieldMap Map<String, String> map);

    /**
     * 出库——外复合任务 - 扫描电子追溯码(强制复核)
     */
    @FormUrlEncoded
    @POST("partsOutReviewPdaController/updateOutReceiveDetail")
    Observable<BaseResponseBean<ReviewScanElectronicBean>> forcedReview(@FieldMap Map<String, String> map);

    /**
     * 出库——外复合 - 索取任务 并获取订单详情
     */
    @FormUrlEncoded
    @POST("partsOutReviewPdaController/outReceiveOrderPad")
    Observable<BaseResponseBean<ReviewOutOrderBean>> reviewOutOrder(@FieldMap Map<String, String> map);

    /**
     * 出库——外复合 - 获取电子追溯码
     */
    @FormUrlEncoded
    @POST("partsOutReviewPdaController/queryDocumentScanned")
    Observable<BaseResponseBean<ReviewSupervisionCodeBean>> reviewOutSupervision(@FieldMap Map<String, Object> map);

    /**
     * 出库——外复合 - 获取电子追溯码对应的商品信息
     */
    @FormUrlEncoded
    @POST("partsOutReviewPdaController/queryAliBaseInfo")
    Observable<BaseResponseBean<ReviewSupervisionProductBean>> reviewOutSupervisionProduct(@FieldMap Map<String, String> map);

    /**
     * 出库——外复合提交
     */
    @FormUrlEncoded
    @POST("partsOutReviewPdaController/outReceiveConfirmPad")
    Observable<BaseResponseBean> reviewOutConfirm(
            @Field("orderCode") String orderCode,
            @Field("buildingCode") String buildingCode,
            @Field("taskOutReviewCode") String taskOutReviewCode);

    /**
     * 外复核取消出库订单
     */
    @GET("partsOutReviewPdaController/cancelOutReceiveOrderPda")
    Observable<BaseResponseBean> cancelOutReceiveOrder(@QueryMap Map<String, String> map);
    /**
     * 出库——外复合 - 获取当前仓库下所有建筑物
     */
    @POST("partsOutReviewPdaController/getBuildingCode")
    Observable<BaseResponseBean<List<Building>>> getAllBuildingCode();




    // 异常处理
    /**
     * 异常问题单列表
     */
    @POST("outstock/web/outstock/exception/task/queryExceptionTaskList")
    Observable<BaseResponseBean<ExceptionHandleListBean>> queryExceptionTaskList(@Body ExceptionBean exceptionBean);


    /**
     * 异常问题单 列表通过 容器 编号查
     */
    @POST("outstock/web/outstock/exception/task/queryExceptionTaskList")
    Observable<BaseResponseBean<ExceptionHandleListBean>> queryExceptionTaskListByContainerId(@Body ExceptionBean exceptionBean);

    /**
     * 异常任务处理前验证是否拣货-废弃
     */
    @GET("PartsInReviewException/PartsInReviewExceptioncontroller/verifyPicking")
    Observable<BaseResponseBean> verifyPicking(@QueryMap Map<String, String> map);

    /**
     * 异常任务冲红
     */
    @POST("outstock/web/outstock/exception/task/confirmRushRed")
    Observable<BaseResponseBean<ExceptionHandleListBean>> confirmRushRed(@Body RushRedBean rushRedBean);

    /**
     * 异常任务  FillPick 补拣
     */
    @FormUrlEncoded
    @POST("PartsInReviewException/PartsInReviewExceptioncontroller/confirmationCorrection")
    Observable<BaseResponseBean> confirmFillPick(@Field("correctionType") String correctionType, @Field("tableData") String tableData);

    /**
     * 异常任务内复核-多货
     */
    @POST("outstock/web/outstock/exception/task/updateStatus")
    Observable<BaseResponseBean> reviewMoreGoods(@QueryMap Map<String, String> map);

    /**
     * 异常任务 获取其他货位的补拣任务
     */
//    @FormUrlEncoded
//    @POST("PartsInReviewException/PartsInReviewExceptioncontroller/getExceptionHandleUseStorage")
//    Observable<BaseResponseBean<List<PickUseStorageBean>>> getExceptionHandleUseStorage(@Field("tableData") String tableData);

    @GET("outstock/web/outstock/exception/task/getExceptionHandleUseStorage")
    Observable<BaseResponseBean<List<PickUseStorageBean>>> getExceptionHandleUseStorage(@Query("id") String id);

    /**
     * 异常任务  补拣  提交
     */
    @POST("outstock/web/outstock/exception/task/exceptionHandlingReplenishment")
    Observable<BaseResponseBean<BaseResponseBean>> commitPickUseStorage(@Body PickUseStorageBeanNew pickUseStorageBean);





    /**
     * 获取集货扫码 的数据
     */
    @FormUrlEncoded
    @POST("storeGoodsPdaController/getStoreGoodsInfo")
    Observable<BaseResponseBean<StoreGoodsInfo>> getStoreGoodsInfo(@Field("tagCode") String tagCode);

    /**
     * 提交 集货扫码
     */
    @POST("storeGoodsPdaController/storeGoodsConfirmPad")
    Observable<BaseResponseBean> storeGoodsConfirmCommit(@Body StoreGoodsPost storeGoodsPost);


    /**
     * 工作量排行榜 —— 请求排行榜
     */
    @GET("PartsPickingTaskPdaController/padPickFinishTaskSum")
    Observable<BaseResponseBean<List<WorkloadRankingBean>>> getWorkloadRankingList(@QueryMap Map<String, Object> map);


    /**
     * 拣货-pda单据墙容器号校验
     */
    @GET("outstock/pda/outstock/wave/container/checkWallCode")
    Observable<BaseResponseBean<CheckResult>> checkWallCode(@QueryMap Map<String, Object> map);

    /**
     *  出库复核 -- 提总保存箱码
     */
    @POST("partsInReviewGroup/saveBoxCodeForMentionTotal")
    Observable<BaseResponseBean<String>> saveBoxCode(@Body Map<String, Object> map);

    /**
     * 拣货-pda销售单号校验
     */
    @GET("outstock/pda/outstock/wave/container/checkErpOrderCode")
    Observable<BaseResponseBean<CheckResult>> checkErpOrderCode(@QueryMap Map<String, Object> map);

    /**
     * 拣货-pda随货同行单绑定按钮
     */
    @GET("outstock/pda/outstock/wave/container/bindWall")
    Observable<BaseResponseBean<CheckResult>> bindErpOrderCode(@QueryMap Map<String, Object> map);


    /**
     * 拣货-pda集货区绑定容器号校验
     */
    @GET("storeGoodsPdaController/checkContainer")
    Observable<BaseResponseBean<ContainerCheckResult>> checkContainerCode(@QueryMap Map<String, Object> map);

    /**
     * 拣货-pda集货区绑定
     */
    @GET("storeGoodsPdaController/bindStoreArea")
    Observable<BaseResponseBean<String>> bindCollecionArea(@QueryMap Map<String, Object> map);

    /**
     * 验证集货货位
     */
    @GET("stockout/alongGoodsOrder/checkConsolidation")
    Observable<BaseResponseBean<String>> checkStoreArea(@QueryMap Map<String, Object> map);

    /**
     * 验证周转箱号
     */
    @GET("stockout/alongGoodsOrder/checkPassBoxCode")
    Observable<BaseResponseBean<String>> checkBoxCode(@QueryMap Map<String, Object> map);

    /**
     * 由周转箱号
     * 获取pda集货信息
     */
    @GET("PickingTaskPdaController/pdaGoodsConsolidation")
    Observable<BaseResponseBean<ConsolidationResult>> consolidationByBoxCode(@QueryMap Map<String, Object> map);

    /**
     * pda集货
     * 提交绑定
     */
    @GET("stockout/alongGoodsOrder/bindGoodsOrderCode")
    Observable<BaseResponseBean<String>> bindSubmit(@QueryMap Map<String, Object> map);

    /**
     * 字典
     */
    @GET("basicdata/dictBases/getByDictType")
    Observable<BaseResponseBean<List<DictTypeBean>>> getByDictType(@QueryMap Map<String, String> map);
}
