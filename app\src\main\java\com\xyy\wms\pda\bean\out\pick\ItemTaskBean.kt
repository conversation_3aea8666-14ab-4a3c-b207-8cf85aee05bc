package com.xyy.wms.pda.bean.out.pick

/**
 * 待领取任务
 */
data class ItemTaskBean(
        var partsGoodPicking: String?,// 待领取任务
        var Replenishmentlist: String?, // 待补货任务
        var unAskforTime: String?, // 任务未索取超长时间
        var allocationCodes: String?, // 未索取的分配单号列表
        var unConfirmTime: String?, // 任务未确定超长时间
        var userNames: String? ,// 未确认人员名字列表
        var bindPickTask: String?,// flag=1 展示待领取的已绑定随货同行单零货任务数
        var unBindPickTask: String?, //flag=1展示待领取的未绑定随货同行单零货任务数
        var flag: String?// 模板展示 ：flag=1 展示随货同行单绑定关系 ；flag =0原有模板不变

)
