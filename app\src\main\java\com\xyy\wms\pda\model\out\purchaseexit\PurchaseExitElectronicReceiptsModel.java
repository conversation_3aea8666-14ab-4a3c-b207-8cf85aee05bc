package com.xyy.wms.pda.model.out.purchaseexit;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitElectronicReceiptsBean;
import com.xyy.wms.pda.contract.out.purchaseexit.PurchaseExitElectronicReceiptsContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.List;

import io.reactivex.Observable;

/**
 * Created by lwj on 2020-02-24.
 * <EMAIL>
 */
public class PurchaseExitElectronicReceiptsModel implements PurchaseExitElectronicReceiptsContract.IPurchaseExitElectronicReceiptsModel {

    public static PurchaseExitElectronicReceiptsModel newInstance() {
        return new PurchaseExitElectronicReceiptsModel();
    }


    @Override
    public Observable<BaseResponseBean<List<PurchaseExitElectronicReceiptsBean>>> queryElectronicReceipts(String lineNumber, String pickUpOrder, String productBatchCode, String productName) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).queryElectronicReceipts(lineNumber, pickUpOrder, productBatchCode, productName)
                .compose(RxHelper.rxSchedulerHelper());
    }
}
