package com.xyy.wms.pda.contract.out.scattered

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.out.ScatteredPickingListBean
import com.xyy.wms.pda.bean.out.SubmitResult
import com.xyy.wms.pda.bean.out.pick.PickDetailSubmitBean
import com.xyy.wms.pda.bean.out.pick.RemindTask
import com.xyy.wms.pda.bean.out.pick.ScatteredPickingOrderBean
import io.reactivex.Observable

/**
 * 出库——拆零拣货明细
 */
interface ScatteredPickingOrderContract {
    interface IScatteredPickingOrderModel : IBaseModel {
        /**
         * 获取拆零拣货列表
         */
        fun getScatteredPickingList(map: Map<String, String?>): Observable<BaseResponseBean<List<ScatteredPickingListBean>>>
        /**
         * 获取拆零拣货任务明细
         *
         * @param map
         * @return
         */
        fun getScatteredPickingOrder(map: Map<String, Any?>): Observable<BaseResponseBean<List<ScatteredPickingOrderBean>>>

        /**
         * 提交拆零拣货订单明细
         *
         * @param body
         */
        fun submitScatteredPickingOrder(body: PickDetailSubmitBean): Observable<BaseResponseBean<SubmitResult>>

        /**
         * 催单
         */
        fun getReminderTaskList(map: Map<String, String>): Observable<BaseResponseBean<List<RemindTask>>>
    }

    interface IScatteredPickingOrderView : IBaseActivity {
        fun getScatteredPickingListSuccess(pickList: List<ScatteredPickingListBean>, lastSelectPosition: String?)
        fun getScatteredPickingOrderSuccess(orderList: List<ScatteredPickingOrderBean>)
        fun submitScatteredPickingOrderSuccess(submitResult: SubmitResult)
        fun getReminderTaskListSuccess(remindList: List<RemindTask>, orderList: List<ScatteredPickingOrderBean>)
    }
}