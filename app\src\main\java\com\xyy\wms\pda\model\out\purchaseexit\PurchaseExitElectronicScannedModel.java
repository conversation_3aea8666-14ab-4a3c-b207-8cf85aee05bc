package com.xyy.wms.pda.model.out.purchaseexit;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseCodeScannedResultBean;
import com.xyy.wms.pda.contract.out.purchaseexit.PurchaseExitElectronicScanedContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.List;

import io.reactivex.Observable;

/**
 * Created by lwj on 2020-02-28.
 * <EMAIL>
 */
public class PurchaseExitElectronicScannedModel implements PurchaseExitElectronicScanedContract.IPurchaseExitElectronicScannedModel {

    public static PurchaseExitElectronicScannedModel newInstance() {
        return new PurchaseExitElectronicScannedModel();
    }

    @Override
    public Observable<BaseResponseBean<List<PurchaseCodeScannedResultBean>>> queryDrugregulatorycode(String lineNumber, String pickUpOrder) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).queryDrugregulatorycode(lineNumber, pickUpOrder)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean> deleteDrugregulatorycode(String lineNumber, String pickUpOrder, String regulatoryCode) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).deleteDrugregulatorycode(lineNumber, pickUpOrder, regulatoryCode)
                .compose(RxHelper.rxSchedulerHelper());
    }
}
