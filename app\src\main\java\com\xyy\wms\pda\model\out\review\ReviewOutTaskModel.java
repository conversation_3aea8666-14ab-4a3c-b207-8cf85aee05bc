package com.xyy.wms.pda.model.out.review;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiOutManagerService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.out.ReviewOutScanBean;
import com.xyy.wms.pda.bean.out.ReviewScanElectronicBean;
import com.xyy.wms.pda.contract.out.review.ReviewOutTaskContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.Map;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 02/18/2019 17:23
 */
public class ReviewOutTaskModel extends BaseModel implements ReviewOutTaskContract.IReviewOutTaskModel {

    public static ReviewOutTaskModel newInstance() {
        return new ReviewOutTaskModel();
    }

    @Override
    public Observable<BaseResponseBean<ReviewOutScanBean>> reviewOutScan(Map<String, String> map) {
        return RetrofitCreateHelper.createApi(ApiOutManagerService.class).reviewOutScan(map)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<ReviewScanElectronicBean>> reviewOutScanElectronic(Map<String, String> map) {
        return RetrofitCreateHelper.createApi(ApiOutManagerService.class).reviewOutScanElectronic(map)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<ReviewScanElectronicBean>> forcedReview(Map<String, String> map) {
        return RetrofitCreateHelper.createApi(ApiOutManagerService.class).forcedReview(map)
                .compose(RxHelper.rxSchedulerHelper());
    }

}