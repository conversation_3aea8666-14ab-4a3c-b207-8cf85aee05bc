package com.xyy.wms.pda.contract.instorage.wholeshelf;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.shelf.CommitShelfResult;
import com.xyy.wms.pda.model.instorage.wholeshelf.WholeShelfProductModel;
import com.xyy.wms.pda.ui.activity.instorage.wholeshelf.ProductWholeShelfActivity;

/**
 * Created by lx on 2018/11/17 17
 * E-Mail：<EMAIL>
 * 库内的商品整件
 */
public interface WholeShelfProductContract {
    interface IWholeShelfProductModel extends IBaseModel {
    }

    interface IWholeShelfProductView extends IBaseActivity {

        void commitStorageOrderDetailSuccess(BaseResponseBean<CommitShelfResult> baseResponseBean);
    }

    abstract class WholeShelfProductPresenter extends BasePresenter<WholeShelfProductModel, ProductWholeShelfActivity> {
    }
}
