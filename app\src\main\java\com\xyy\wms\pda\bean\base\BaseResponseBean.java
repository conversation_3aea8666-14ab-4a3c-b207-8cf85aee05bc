package com.xyy.wms.pda.bean.base;

/**
 * Created by xyy on 2018/9/27.
 */

public class BaseResponseBean<T> {
    /**
     * 状态码  0 成功， 1 失败
     */
    private int code;
    /**
     * 状态信息
     */
    private String msg;
    /**
     * 结果集
     */
    private T result;

    private boolean ok;

    public boolean isOk() {
        return ok;
    }

    public void setOk(boolean ok) {
        this.ok = ok;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }

    public boolean isSuccess() {
        return code == 0;
    }
    @Override
    public String toString() {
        return "BaseResponseBean{" +
            "result='" + result + '\'' +
            ", msg='" + msg + '\'' +
            ", code='" + code + '\'' +
            '}';
    }
}
