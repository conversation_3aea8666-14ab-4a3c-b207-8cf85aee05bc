package com.xyy.wms.pda.model.out.purchaseexit;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitReViewListBean;
import com.xyy.wms.pda.contract.out.purchaseexit.PurchaseExitElectronicReViewContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.List;

import io.reactivex.Observable;

/**
 * Created by lwj on 2020-03-03.
 * <EMAIL>
 */
public class PurchaseExitElectronicReViewModel implements PurchaseExitElectronicReViewContract.IPurchaseExitElectronicReViewModel {

    public static PurchaseExitElectronicReViewModel newInstance() {
        return new PurchaseExitElectronicReViewModel();
    }

    @Override
    public Observable<BaseResponseBean<List<PurchaseExitReViewListBean>>> queryReviewDocuments(String containerCode, String refundOrderStatus) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).queryReviewDocuments(containerCode, refundOrderStatus)
                .compose(RxHelper.rxSchedulerHelper());
    }
}


