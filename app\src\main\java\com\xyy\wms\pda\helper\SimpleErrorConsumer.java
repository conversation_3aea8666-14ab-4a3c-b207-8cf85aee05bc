package com.xyy.wms.pda.helper;

import android.text.TextUtils;
import android.util.Log;

import com.google.gson.JsonSyntaxException;
import com.scwang.smartrefresh.layout.constant.RefreshState;
import com.xyy.utilslibrary.base.IBaseView;
import com.xyy.utilslibrary.base.activity.BaseCompatActivity;
import com.xyy.utilslibrary.base.fragment.BaseCompatFragment;

import java.net.SocketTimeoutException;

import io.reactivex.functions.Consumer;

public class SimpleErrorConsumer implements Consumer<Throwable> {
    private static final String TAG = "SimpleErrorConsumer";

    private IBaseView iBaseView;
    private String toastText;

    public SimpleErrorConsumer(IBaseView iBaseView) {
        this.iBaseView = iBaseView;
    }

    public SimpleErrorConsumer(IBaseView iBaseView, String toastText) {
        this.iBaseView = iBaseView;
        this.toastText = toastText;
    }

    @Override
    public void accept(Throwable throwable) throws Exception {
        if (iBaseView == null)
            return;
        Log.e(TAG,"accept_throwable:"+throwable.getMessage());
        iBaseView.hideWaitDialog();
        throwable.printStackTrace();
        handlerRefreshLayout(false);
        if (throwable instanceof SocketTimeoutException) {
            iBaseView.showToast("请求超时,请检查网络连接");
            onError(throwable, "请求超时,请检查网络连接");
            return;
        }
        if (throwable instanceof JsonSyntaxException) {
            iBaseView.showToast("数据错误，请重试");
            onError(throwable, "数据错误，请重试");
            return;
        }
        if (TextUtils.isEmpty(toastText)) {
            iBaseView.showToast("网络请求失败，请稍后重试");
            onError(throwable, "网络请求失败，请稍后重试");
        } else {
            iBaseView.showToast(toastText);
            onError(throwable, "网络请求失败，请稍后重试");
        }
    }

    protected void onError(Throwable throwable, String msg) {
        System.out.println(msg);
        throwable.printStackTrace();
        Log.e("ErrorConsumer","throw:"+throwable.getMessage());
        Log.e("ErrorConsumer","msg:"+msg);
      //  onError(throwable, msg);


    }

    private void handlerRefreshLayout(boolean isSuccess) {
        if (iBaseView instanceof BaseCompatFragment) {
            BaseCompatFragment bf = (BaseCompatFragment) iBaseView;
            if (bf.refreshLayout != null) {
                if (bf.refreshLayout.getState() == RefreshState.Refreshing) {
                    bf.refreshLayout.finishRefresh(isSuccess);
                } else if (bf.refreshLayout.getState() == RefreshState.Loading) {
                    bf.refreshLayout.finishLoadMore(isSuccess);
                }
            }
        } else if (iBaseView instanceof BaseCompatActivity) {
            BaseCompatActivity ba = (BaseCompatActivity) iBaseView;
            if (ba.refreshLayout != null) {
                if (ba.refreshLayout.getState() == RefreshState.Refreshing) {
                    ba.refreshLayout.finishRefresh(isSuccess);
                } else if (ba.refreshLayout.getState() == RefreshState.Loading) {
                    ba.refreshLayout.finishLoadMore(isSuccess);
                }
            }
        }
    }
}
