package com.xyy.wms.pda.model.out.purchaseexit;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInAcceptService;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.checkaccept.SecondLoginPost;
import com.xyy.wms.pda.bean.instorage.checkaccept.SecondLoginResult;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitElectronicReceiptsBean;
import com.xyy.wms.pda.contract.out.purchaseexit.PurchaseExitElectronicReViewGoodsContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * Created by lwj on 2020-03-03.
 * <EMAIL>
 */
public class PurchaseExitElectronicReViewGoodsModel implements PurchaseExitElectronicReViewGoodsContract.IPurchaseExitElectronicReViewGoodsModel {




    public static PurchaseExitElectronicReViewGoodsModel newInstance() {
        return new PurchaseExitElectronicReViewGoodsModel();
    }


    @Override
    public Observable<BaseResponseBean<PurchaseExitElectronicReceiptsBean>> updateReviewDetailStatus(String lineNumber, String pickUpOrder) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).updateReviewDetailStatus(lineNumber, pickUpOrder)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<SecondLoginResult>> secondLogin(SecondLoginPost secondLoginPost) {
        return RetrofitCreateHelper.createApi(ApiInAcceptService.class).secondLogin(secondLoginPost)
                .compose(RxHelper.rxSchedulerHelper());
    }
}


