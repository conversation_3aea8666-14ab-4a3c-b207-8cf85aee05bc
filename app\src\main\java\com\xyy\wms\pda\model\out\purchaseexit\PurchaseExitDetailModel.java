package com.xyy.wms.pda.model.out.purchaseexit;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseCommitResult;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitDetail;
import com.xyy.wms.pda.contract.out.purchaseexit.PurchaseExitDetailContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
/**
 * created by  liangxing  on 2020-01-15.
 */
public class PurchaseExitDetailModel extends BaseModel implements PurchaseExitDetailContract.IPurchaseExitOrderModel {

    @Override
    public Observable<BaseResponseBean<List<PurchaseExitDetail>>> queryExecutionDetailList(Map<String, Object> map) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).queryExecutionDetailList(map)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<PurchaseCommitResult>> commitPurchaseExitDetail(PurchaseExitDetail commitPost) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).commitPurchaseExitDetail(commitPost)
                .compose(RxHelper.rxSchedulerHelper());
    }

    public static PurchaseExitDetailModel newInstance() {
        return new PurchaseExitDetailModel();
    }
}

