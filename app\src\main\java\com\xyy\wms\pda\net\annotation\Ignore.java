package com.xyy.wms.pda.net.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * Created by lwj on 2020/3/30
 * <EMAIL>
 * 忽略参数
 */
@Documented
@Target(FIELD)
@Retention(RUNTIME)
public @interface Ignore {
}
