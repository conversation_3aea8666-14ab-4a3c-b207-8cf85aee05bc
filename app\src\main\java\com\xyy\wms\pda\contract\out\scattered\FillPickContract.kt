package com.xyy.wms.pda.contract.out.scattered


import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.wms.pda.bean.out.pick.FillPickSubmitBean
import com.xyy.wms.pda.bean.base.BaseResponseBean
import io.reactivex.Observable

interface FillPickContract {

    interface IFillPickModel : IBaseModel {
        fun submitFillPickGoods(bean: FillPickSubmitBean): Observable<BaseResponseBean<*>>
    }

    interface IFillPickView : IBaseActivity {
        fun submitFillPickGoodsSuccess(noOriginalSoldOut: Boolean)
    }
}