package com.xyy.wms.pda.net;

import com.xyy.utilslibrary.helper.LoggingInterceptor;
import com.xyy.utilslibrary.helper.okhttp.TrustManager;
import com.xyy.wms.pda.api.ApiUrl;
import com.xyy.wms.pda.net.interceptor.CommonInterceptor;
import com.xyy.wms.pda.net.interceptor.MyConverterFactory;
import com.xyy.wms.pda.net.interceptor.SwitchHostInterceptor;

import org.apache.http.conn.ssl.SSLSocketFactory;

import java.util.HashMap;
import java.util.concurrent.TimeUnit;

import okhttp3.ConnectionPool;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;
import android.util.Log;
import java.io.IOException;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 创建网络请求
 */
public class RetrofitCreateHelper {

    private static final int TIMEOUT_READ = 30;
    private static final int TIMEOUT_CONNECTION = 30;

    private RetrofitCreateHelper() {
    }

    private static class SingleTonHolder {
        private static final RetrofitCreateHelper INSTANCE = new RetrofitCreateHelper();
    }

    // 静态内部类形式的单例可保证线程安全，也能保证单例的唯一性，同时也延迟了单例的实例化
    public static RetrofitCreateHelper getInstance() {
        return RetrofitCreateHelper.SingleTonHolder.INSTANCE;
    }

    // 添加重试拦截器
    private static class RetryInterceptor implements Interceptor {
        private static final int MAX_RETRY_COUNT = 3;

        @Override
        public Response intercept(Chain chain) throws IOException {
            Request request = chain.request();
            Response response = null;
            int tryCount = 0;

            while (tryCount < MAX_RETRY_COUNT) {
                try {
                    response = chain.proceed(request);
                    if (response.isSuccessful()) {
                        return response;
                    }
                } catch (Exception e) {
                    Log.e("Network", "Request failed, retry: " + tryCount);
                    if (tryCount == (MAX_RETRY_COUNT - 1)) {
                        throw e; // 最后一次重试失败，抛出异常
                    }
                }
                tryCount++;
                
                // 如果还有重试机会，等待后重试
                if (tryCount < MAX_RETRY_COUNT) {
                    try {
                        Thread.sleep(1000 * tryCount);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
            return response;
        }
    }

    private static final OkHttpClient okHttpClient = new OkHttpClient.Builder()
        //SSL证书
        .sslSocketFactory(TrustManager.getUnsafeOkHttpClient())
        .hostnameVerifier(SSLSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER)
        //打印日志
        .addInterceptor(new SwitchHostInterceptor())
        .addInterceptor(new CommonInterceptor())
        .addInterceptor(new RetryInterceptor())  // 添加重试拦截器
        .addNetworkInterceptor(new LoggingInterceptor())
        //time out
        .connectTimeout(TIMEOUT_CONNECTION, TimeUnit.SECONDS)
        .readTimeout(TIMEOUT_READ, TimeUnit.SECONDS)
        .writeTimeout(TIMEOUT_READ, TimeUnit.SECONDS)
        //失败重连
        .retryOnConnectionFailure(true)
        // 禁用连接池复用
        .connectionPool(new ConnectionPool(0, 1, TimeUnit.MINUTES))
        .build();

    private static final HashMap<Class, Object> cacheService = new HashMap<>();

    /**
     * 方式一：使用静态方法创建API，需要添加缓存否则会每次网络请求创建一个，造成不必要的内存浪费和性能损耗
     *
     * @param clazz
     * @param <T>
     * @return
     */
    @Deprecated
    @SuppressWarnings("unchecked")
    public static <T> T createApi(Class<T> clazz) {
        if (cacheService.get(clazz) != null) {
            return (T) cacheService.get(clazz);
        }
        Retrofit retrofit = new Retrofit.Builder()
            .baseUrl(ApiUrl.BASE_URL)
            .client(okHttpClient)
            .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
            .addConverterFactory(MyConverterFactory.create())
            .build();
        T obj = retrofit.create(clazz);
        cacheService.put(clazz, obj);
        return obj;
    }

    /**
     * 方式二（推荐使用）：使用静态内部类模式实现单例，进行创建API，保证每个页面使用唯一的api进行网络请求，
     * 同缓存方式相比减少了缓存的判断和存取，性能更佳
     *
     * @param clazz
     * @param <T>
     * @return
     */
    public <T> T createRetrofit(Class<T> clazz) {
        Retrofit retrofit = new Retrofit.Builder()
            .baseUrl(ApiUrl.BASE_URL)
            .client(okHttpClient)
            .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
            .addConverterFactory(MyConverterFactory.create())
            .build();
        return retrofit.create(clazz);
    }

    public <T> T createApi(Class<T> clazz, String url) {
        Retrofit retrofit = new Retrofit.Builder()
            .baseUrl(url)
            .client(okHttpClient)
            .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
            .addConverterFactory(MyConverterFactory.create())
            .build();
        return retrofit.create(clazz);
    }

    // 添加重置网络客户端的方法
    public static void resetOkHttpClient() {
        if (okHttpClient != null) {
            okHttpClient.connectionPool().evictAll();
        }
    }

}

