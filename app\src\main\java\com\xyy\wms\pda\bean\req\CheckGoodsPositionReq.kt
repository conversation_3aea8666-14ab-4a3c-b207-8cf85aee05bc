package com.xyy.wms.pda.bean.req

import com.xyy.wms.pda.net.req.IGetReq
import java.io.Serializable

/**
 * 校验货位 req  bean
 *  @param batchNumber 批号
 *  @param buildingCode 批号
 *  @param channelCode 批号
 *  @param goodsPositionCode 批号
 *  @param ownerCode 批号
 *  @param productCode 批号
 *  @param storageTypeCode 批号
 *  @param storageRoomCode 批号
 *  @param productPackingBigNumber 批号
 *  @param showPosition 批号
 *  @param productType 批号
 *  @param productionDate 批号
 *  @param validityDate 批号
 *  @param sterilizingBatchNumber 批号
 */
data class CheckGoodsPositionReq(
        val batchNumber: String? = "",
        val buildingCode: String? = "",
        val channelCode: String? = "",
        val goodsPositionCode: String? = "",
        val ownerCode: String? = "",
        val productCode: String? = "",
        val storageTypeCode: String? = "",
        val storageRoomCode: String? = "",
        val productPackingBigNumber: Int? = 0,
        val showPosition: String? = "",
        val productType: Int? = 0,
        val productionDate: String? = "",
        val validityDate: String? = "",
        val sterilizingBatchNumber: String? = ""
) : Serializable, IGetReq
