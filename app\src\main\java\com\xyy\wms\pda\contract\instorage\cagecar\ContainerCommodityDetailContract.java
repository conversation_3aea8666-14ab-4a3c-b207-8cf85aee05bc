package com.xyy.wms.pda.contract.instorage.cagecar;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.instorage.cageCar.ContainerProductDetailBean;

import java.util.List;

import io.reactivex.Observable;
/**
 * Created by XyyMvpPdaTemplate on 02/18/2020 14:01
 */
public interface ContainerCommodityDetailContract {

    interface IContainerCommodityDetailModel extends IBaseModel {
        Observable<BaseResponseBean<List<ContainerProductDetailBean>>> getDetailsByContainer(String containerCode);
    }

    interface IContainerCommodityDetailView extends IBaseActivity {
        void getDetailsByContainerSuccess(BaseResponseBean<List<ContainerProductDetailBean>> baseResponseBean);
    }

}
