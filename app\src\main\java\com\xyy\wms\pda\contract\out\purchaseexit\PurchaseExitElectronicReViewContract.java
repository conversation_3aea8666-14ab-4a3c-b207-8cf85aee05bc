package com.xyy.wms.pda.contract.out.purchaseexit;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseFragment;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitReViewListBean;

import java.util.List;

import io.reactivex.Observable;

/**
 * Created by lwj on 2020-02-24.
 * <EMAIL>
 * 购进退出复核单列表
 */
public interface PurchaseExitElectronicReViewContract {


    interface IPurchaseExitElectronicReViewModel extends IBaseModel {

        Observable<BaseResponseBean<List<PurchaseExitReViewListBean>>> queryReviewDocuments(
                String containerCode,
                String refundOrderStatus);


    }


    interface IPurchaseExitElectronicReViewView extends IBaseFragment {

        void queryReviewDocumentsSuccess(String containerCode, BaseResponseBean<List<PurchaseExitReViewListBean>> bean);
    }

    abstract class IPurchaseExitElectronicReViewPresenter extends BasePresenter<IPurchaseExitElectronicReViewModel, IPurchaseExitElectronicReViewView> {

        public abstract void queryReviewDocuments(
                String containerCode,
                String refundOrderStatus);
    }


}
