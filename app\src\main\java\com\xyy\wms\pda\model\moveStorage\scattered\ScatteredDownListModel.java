package com.xyy.wms.pda.model.moveStorage.scattered;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiMoveStorageService;
import com.xyy.wms.pda.api.ApiService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.common.CancelTaskBean;
import com.xyy.wms.pda.bean.common.MoveStorageFinishedBean;
import com.xyy.wms.pda.bean.common.RunningTaskResult;
import com.xyy.wms.pda.bean.instorage.shelf.InShelfResult;
import com.xyy.wms.pda.bean.moveStorage.AddTaskDetailBean;
import com.xyy.wms.pda.bean.moveStorage.PalletGetGoodsBean;
import com.xyy.wms.pda.bean.moveStorage.PalletGetGoodsResult;
import com.xyy.wms.pda.bean.moveStorage.ResourcePositionGoodsBean;
import com.xyy.wms.pda.bean.productCode.ProductCode;
import com.xyy.wms.pda.contract.movestorage.scattered.ScatteredDownListContract;
import com.xyy.wms.pda.contract.movestorage.wholeshelf.WholeShelfDownListContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.List;

import io.reactivex.Observable;

/**
 * 零货下架-列表
 */
public class ScatteredDownListModel extends BaseModel implements ScatteredDownListContract.ScatteredDownListModel {

    public static ScatteredDownListModel newInstance() {
        return new ScatteredDownListModel();
    }
    /**
     * 进行中任务
     */
    public Observable<BaseResponseBean<RunningTaskResult>> getRunningTask(){
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).getRunningTask()
            .compose(RxHelper.rxSchedulerHelper());
    }

    /**
     * 添加任务明细
     */
    public Observable<BaseResponseBean<Boolean>> addTaskDetail(AddTaskDetailBean addTaskDetailBean){
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).addTaskDetail(addTaskDetailBean)
            .compose(RxHelper.rxSchedulerHelper());
    }

    /**
     * 移除任务
     */
    public Observable<BaseResponseBean<Boolean>> cancelTask(CancelTaskBean cancelTaskBean){
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).canelTask(cancelTaskBean)
            .compose(RxHelper.rxSchedulerHelper());
    }
    /**
     * 下架完成
     */
    public Observable<BaseResponseBean<Boolean>> setFinished(MoveStorageFinishedBean finishedBean){
        return RetrofitCreateHelper.createApi(ApiMoveStorageService.class).setFinished(finishedBean)
            .compose(RxHelper.rxSchedulerHelper());
    }
}
