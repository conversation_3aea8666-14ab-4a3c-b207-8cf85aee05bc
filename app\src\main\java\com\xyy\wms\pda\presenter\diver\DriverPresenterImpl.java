package com.xyy.wms.pda.presenter.diver;

import androidx.annotation.NonNull;

import com.xyy.common.util.ToastUtils;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.driver.CarrierBean;
import com.xyy.wms.pda.bean.driver.DriverBean;
import com.xyy.wms.pda.bean.driver.DriverEditBean;
import com.xyy.wms.pda.bean.driver.UploadPicBean;
import com.xyy.wms.pda.helper.SimpleErrorConsumer;
import com.xyy.wms.pda.helper.SimpleSuccessConsumer;
import com.xyy.wms.pda.model.driver.DriverModelImpl;
import com.xyy.wms.pda.ui.activity.driver.DriverEditActivity;
import com.xyy.wms.pda.ui.activity.driver.DriverMainActivity;

import java.io.File;
import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.List;

public class DriverPresenterImpl extends BasePresenter<DriverModelImpl, DriverEditActivity> implements DriverEditPresenter{

    public static DriverPresenterImpl newInstance(){
        return new DriverPresenterImpl();
    }


    @Override
    protected DriverModelImpl getModel() {
        return DriverModelImpl.newInstance();
    }

    @Override
    public void getDriverInfo(@NonNull Number id) {
        if (mRxManager == null || mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.getDriverInfo(id)
            .subscribe(new SimpleSuccessConsumer<BaseResponseBean<DriverEditBean>>(mIView) {
                @Override
                public void onSuccess(BaseResponseBean<DriverEditBean> requestBaseBean) {
                    mIView.getDriverInfo(requestBaseBean.getResult());
                }

                @Override
                public void onFailure(int code, String msg) {
                    super.onFailure(code, msg);
                    ToastUtils.showShortSafe(msg);
                }
            }, new SimpleErrorConsumer(mIView)));

    }



    @Override
    public void editDriver(boolean isAdd, @NonNull DriverEditBean driver) {
        if (mRxManager == null || mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.editDriverInfo(isAdd,driver)
            .subscribe(new SimpleSuccessConsumer<BaseResponseBean>(mIView) {
                @Override
                public void onSuccess(BaseResponseBean requestBaseBean) {
                    //  mIView.getDriverList(requestBaseBean.getResult());
                    mIView.editDriver(isAdd,requestBaseBean.getCode(),requestBaseBean.getMsg());
                }

                @Override
                public void onFailure(int code, String msg) {
                    super.onFailure(code, msg);
                    ToastUtils.showShortSafe(msg);
                }
            }, new SimpleErrorConsumer(mIView)));
    }

    @Override
    public void uploadFile(@NonNull File file) {
        if (mRxManager == null || mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.uploadFile(file)
            .subscribe(new SimpleSuccessConsumer<BaseResponseBean>(mIView) {
                @Override
                public void onSuccess(BaseResponseBean requestBaseBean) {
                    if (requestBaseBean.getResult()!=null)
                    mIView.uploadPic(requestBaseBean.getResult().toString());
                }

                @Override
                public void onFailure(int code, String msg) {
                    super.onFailure(code, msg);
                    ToastUtils.showShortSafe(msg);
                }
            }, new SimpleErrorConsumer(mIView)));

    }

    @Override
    public void getCarrier() {
        if (mRxManager == null || mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.getCarrier()
            .subscribe(new SimpleSuccessConsumer<BaseResponseBean<CarrierBean>>(mIView) {
                @Override
                public void onSuccess(BaseResponseBean<CarrierBean> requestBaseBean) {
                    if (requestBaseBean.getResult()!=null)
                        mIView.getCarrier(requestBaseBean.getResult());
                }

                @Override
                public void onFailure(int code, String msg) {
                    super.onFailure(code, msg);
                    ToastUtils.showShortSafe(msg);
                }
            }, new SimpleErrorConsumer(mIView)));
    }
}
