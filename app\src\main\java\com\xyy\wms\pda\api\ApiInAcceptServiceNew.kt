package com.xyy.wms.pda.api

import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.common.DictListByNameAndType
import com.xyy.wms.pda.bean.instorage.InitCheckPageBean
import com.xyy.wms.pda.bean.instorage.PackageBarCodeBean
import com.xyy.wms.pda.bean.instorage.ProductCodePost
import com.xyy.wms.pda.bean.instorage.checkaccept.*
import com.xyy.wms.pda.bean.instorage.SecondLoginPost
import com.xyy.wms.pda.bean.instorage.SecondLoginResult
import com.xyy.wms.pda.bean.instorage.PictureUploadResponse
import com.xyy.wms.pda.bean.instorage.PictureDeleteRequest
import io.reactivex.Observable
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.http.*

/**
 * 验收接口（New，instock/wmsApp 前缀）
 */
interface ApiInAcceptServiceNew {
    /**
     * 扫描供应商-查询待验信息（新）
     */
    @POST("instock/wmsApp/checkOrder/getWaitCheckInfoBySupplier")
    fun getWaitCheckInfoBySupplier(@Body waitCheckInfoBySupplierPost: WaitCheckInfoBySupplierPost): Observable<BaseResponseBean<MutableList<WaitCheckInfoBySupplierBean>>>
    /**
     * APP->入库验收->02->获取待入库验收商品列表
     */
    @POST("instock/wmsApp/checkOrder/getWaitCheckDetailList")
    fun getWaitCheckDetailList(@Body waitCheckDetailListPost : WaitCheckDetailListPost) : Observable<BaseResponseBean<List<WaitCheckDetailListBean>>>
    /**
     * 根据字典类型和字典名称模糊查询
     */
    @GET("basicdata/dictBases/getByDictType")
    fun getDictListByNameAndType(@Query("dictName") dictName: String?, @Query("dictType") dictType: String?): Observable<BaseResponseBean<List<DictListByNameAndType>>>
    /**
     * APP->入库验收->06->根据商品批号等信息返回结果数据
     */
    @POST("instock/wmsApp/checkOrder/selectProductCode")
    fun selectProductCode(@Body productCodePost: ProductCodePost) : Observable<BaseResponseBean<PackageBarCodeBean>>

    /**
     * 是否存在追溯码
     */
    @POST("instock/wmsApp/checkOrder/initCheckPage")
    fun initCheckPage(@Body initCheckPagePost: InitCheckPagePost) : Observable<BaseResponseBean<InitCheckPageBean>>

    /**
     * APP->入库验收->13->验收单驳回
     */
    @POST("instock/wmsApp/checkOrder/turnDown")
    fun turnDownCheckOrder(@Body checkOrderTurnDownPost: CheckOrderTurnDownPost): Observable<BaseResponseBean<CheckOrderTurnDownBean>>

    /**
     * APP->入库验收->14->提交验收单
     */
    @POST("instock/wmsApp/checkOrder/doSubmit")
    fun doSubmitCheckOrder(@Body checkOrderDoSubmitPost: CheckOrderDoSubmitPost): Observable<BaseResponseBean<String>>

    /**
     *  验收特殊药品二次登录接口
     */
    @POST("instock/wmsApp/checkOrder/secondLogin")
    fun secondLogin(@Body secondLoginPost: SecondLoginPost): Observable<BaseResponseBean<SecondLoginResult>>
    /**
     * APP->入库验收->07->根据容器编号返回容器信息
     */
    @POST("instock/wmsApp/checkOrder/selectContainer")
    fun selectContainer(@Body selectContainerPost : SelectContainerPost) : Observable<BaseResponseBean<SelectContainerBean>>
    /**
     * APP->入库验收->12->验收保存
     */
    @POST("instock/wmsApp/checkOrder/save")
    fun saveCheckOrder(@Body checkOrderSavePost: CheckOrderSavePost): Observable<BaseResponseBean<String>>
    /**
     * APP->入库验收->16->追溯码扫描页面
     */
    @POST("instock/wmsApp/checkOrder/findPurchaseCodeScanOrder")
    fun findPurchaseCodeScanOrder(@Body findPurchaseCodeScanOrderPost : FindPurchaseCodeScanOrderPost) : Observable<BaseResponseBean<FindPurchaseCodeScanOrderBean>>
    /**
     * APP->入库验收->17->追溯码扫描-工号密码校验
     */
    @POST("instock/wmsApp/checkOrder/checkPermissionBystaffNum")
    fun checkPermissionBystaffNum(@Body checkPermissionBystaffNumPost : CheckPermissionBystaffNumPost) : Observable<BaseResponseBean<CheckPermissionBystaffNumBean>>

    /**
     * APP->入库验收->19->追溯码扫描确认
     */
    @POST("instock/wmsApp/checkOrder/confirm")
    fun confirmCodeScanOrder(@Body confirmCodeScanOrderPost : ConfirmCodeScanOrderPost) : Observable<BaseResponseBean<String>>

    /**
     * APP->入库验收->18->追溯码扫描解锁
     */
    @POST("instock/wmsApp/checkOrder/unlock")
    fun unlockCodeScanOrder(@Body unlockCodeScanOrder : UnlockCodeScanOrder) : Observable<BaseResponseBean<String>>

    /**
     * 上传异常图片
     * @param businessId 业务单据ID
     * @param businessType 业务类型
     * @param pictures 图片文件列表
     * @return 上传结果
     */
    @POST("api/exception/pictures/upload")
    @Multipart
    fun uploadExceptionPictures(
        @Part("businessId") businessId: RequestBody,
        @Part("businessType") businessType: RequestBody,
        @Part pictures: List<MultipartBody.Part>
    ): Observable<BaseResponseBean<PictureUploadResponse>>

    /**
     * 删除异常图片
     * @param request 删除请求参数
     * @return 删除结果
     */
    @POST("api/exception/pictures/delete")
    fun deleteExceptionPictures(
        @Body request: PictureDeleteRequest
    ): Observable<BaseResponseBean<Boolean>>

    /**
     * 查询异常图片
     * @param businessId 业务单据ID
     * @return 图片URL列表
     */
    @GET("api/exception/pictures/{businessId}")
    fun getExceptionPictures(
        @Path("businessId") businessId: String
    ): Observable<BaseResponseBean<List<String>>>
}
