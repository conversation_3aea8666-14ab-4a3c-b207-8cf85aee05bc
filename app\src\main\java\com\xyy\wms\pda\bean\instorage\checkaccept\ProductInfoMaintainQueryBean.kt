package com.xyy.wms.pda.bean.instorage.checkaccept

import java.io.Serializable

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/17
 */
data class ProductInfoMaintainQueryBean(
    var id : Long,
    var productCode: String,            //	编码编码,
    var largeCategory: String,          //	商品大类,
    var productName: String,            //	商品名称,
    var specifications: String,         //	规格,
    var dosageForm: String,             //	剂型,
    var packingUnit: String,            //	包装单位,
    var storageConditions: String,      //	存储条件（4：冷藏 5：冷冻 6：控温 7：常温 8：阴凉）,
    var storageAttribute: String,       //	存放属性 （1：异性品，2：液体，3：易碎品，4：易差错，5：特殊品 ）,
    var ifErrorProne: Int,           //	是否易收错 0 否 1是,
    var abcTypeList : List<AbcTypeList>,// 货位ABC集合
    var storageAttributeList : List<StorageAttributeList>, // 存放属性下拉 （1：异性品，2：液体，3：易碎品，4：易差错，5：特殊品 ）
    var smallProductPackingVO : SmallProductPackingVO,
    var largeProductPackingVO : LargeProductPackingVO,
    var lhkLogicalregionRelation : LhkLogicalregionRelation,
    var zjkLogicalregionRelation : ZjkLogicalregionRelation
) : Serializable

data class AbcTypeList (
    var dictCode : String,
    var dictName : String
) : Serializable

data class StorageAttributeList (
    var code : String,
    var name : String
) : Serializable
