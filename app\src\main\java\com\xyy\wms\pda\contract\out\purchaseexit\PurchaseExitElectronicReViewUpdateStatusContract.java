package com.xyy.wms.pda.contract.out.purchaseexit;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.CacheAreaPdaBean;
import com.xyy.wms.pda.bean.purchaseexit.UpdateReviewStatusBean;

import io.reactivex.Observable;

/**
 * Created by lwj on 2020-02-24.
 * <EMAIL>
 * 购进退出复核单列表
 */
public interface PurchaseExitElectronicReViewUpdateStatusContract {


    interface IPurchaseExitElectronicReViewUpdateStatusModel extends IBaseModel {

        Observable<BaseResponseBean<UpdateReviewStatusBean>> updateReviewStatus(
                String refundOrderCode,
                String modifyOrder,
                String pickUpOrder,
                String refundShelfTemporary,
                String secondRecheckUser
        );

        Observable<BaseResponseBean<CacheAreaPdaBean>> getCacheAreaPda();
    }


    interface IPurchaseExitElectronicReViewUpdateStatusView extends IBaseActivity {

        void updateReviewStatusSuccess(BaseResponseBean<UpdateReviewStatusBean> bean);

        void getCacheAreaPdaSuccess(BaseResponseBean<CacheAreaPdaBean> bean);
    }

    abstract class IPurchaseExitElectronicReViewUpdateStatusPresenter extends BasePresenter<IPurchaseExitElectronicReViewUpdateStatusModel, IPurchaseExitElectronicReViewUpdateStatusView> {

        public abstract void updateReviewStatus(
                String refundOrderCode,
                String modifyOrder,
                String pickUpOrder,
                String refundShelfTemporary,
                String secondRecheckUser
        );

        public abstract void getCacheAreaPda();

    }


}
