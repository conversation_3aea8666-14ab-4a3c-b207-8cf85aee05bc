package com.xyy.wms.pda.model.driver;

import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.driver.CarrierBean;
import com.xyy.wms.pda.bean.driver.DriverBean;
import com.xyy.wms.pda.bean.driver.DriverEditBean;
import com.xyy.wms.pda.bean.driver.UploadPicBean;

import java.io.File;
import java.util.List;

import io.reactivex.Observable;

public interface DriverModel extends IBaseModel {
    Observable<BaseResponseBean<List<DriverBean>>> getDriverList(String carNo);

    Observable<BaseResponseBean<DriverEditBean>> getDriverInfo(Number id);

    Observable<BaseResponseBean> editDriverInfo(boolean isAdd,DriverEditBean editBean);

    Observable<BaseResponseBean> uploadFile(File file);

    Observable<BaseResponseBean<CarrierBean>> getCarrier();


}
