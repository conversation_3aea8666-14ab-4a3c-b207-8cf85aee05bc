package com.xyy.wms.pda.contract.out.storegoods;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.out.storegoods.StoreGoodsInfo;
import com.xyy.wms.pda.bean.out.storegoods.StoreGoodsPost;

import io.reactivex.Observable;

/**
 * Created by XyyMvpPdaTemplate on 11/18/2019 14:16
 */
public interface CollectionGoodsScanContract {

    interface ICollectionGoodsScanModel extends IBaseModel {

        Observable<BaseResponseBean<StoreGoodsInfo>> getStoreGoodsInfo(String tagCode);

        Observable<BaseResponseBean> storeGoodsConfirmCommit(StoreGoodsPost storeGoodsPost);

    }

    interface ICollectionGoodsScanView extends IBaseActivity {

        void getStoreGoodsInfoSuccess(StoreGoodsInfo storeGoodsInfo);

        void storeGoodsConfirmCommitSuccess();

    }

}
