package com.xyy.wms.pda.bean.instorage.checkaccept

import java.io.Serializable

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/13
 */
data class InitCheckPageBean (
    var checkOrderCode : String,                                                        // 验收单号
    var pageNum : Int,                                                                  // 管理页面
    var buildingCode : String,                                                          // 建筑物编码
    var ownerCode : String,                                                             // 业主编号
    var purchaseCheckOrderDetailVos : List<PurchaseCheckOrderDetailVos>
) : Serializable

data class PurchaseCheckOrderDetailVos (
    var productSupervise : Int,                                                         // 追溯码采集(1:是;0:否)
    var productBatchCode : String,                                                      // 商品批号
    var productManufactureDate : String,                                                // 生产日期
    var productPackUnitBigSpecification : String,                                       // 件包装规格(含小包装数)
    var productPackUnitMediumSpecification : String,                                    // 中包装规格
    var productValidDate : String,                                                      // 有效期至
    var productCode : String,                                                           // 商品编码
    var orgCode : String,                                                               // 机构编号
    var channelCode : String,                                                           // 渠道编码
    var lineNumber: Int                                                               // 行号
) : Serializable
