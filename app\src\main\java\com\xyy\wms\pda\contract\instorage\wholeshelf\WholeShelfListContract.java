package com.xyy.wms.pda.contract.instorage.wholeshelf;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.common.RunningTaskResult;
import com.xyy.wms.pda.bean.instorage.shelf.InShelfResult;
import com.xyy.wms.pda.bean.moveStorage.AddTaskBean;
import com.xyy.wms.pda.bean.moveStorage.LogicBean;
import com.xyy.wms.pda.bean.moveStorage.PalletGoodsBean;
import com.xyy.wms.pda.bean.moveStorage.PalletOnPreviewResult;
import com.xyy.wms.pda.bean.moveStorage.StartTaskResult;
import com.xyy.wms.pda.model.instorage.wholeshelf.WholeShelfListModel;
import com.xyy.wms.pda.ui.activity.instorage.wholeshelf.InWholeShelfListActivity;

import java.util.List;

import io.reactivex.Observable;
/**
 * 整件上架列表
 */
public interface WholeShelfListContract {
    interface IWholeShelfListModel extends IBaseModel {
        Observable<BaseResponseBean<InShelfResult>> getWholeShelfList(String containerCode, int containerType);
        public abstract  Observable<BaseResponseBean<StartTaskResult>> self(Number taskType, String palletNo);
        public abstract Observable<BaseResponseBean<LogicBean>> getShelfLogic(String palletNo);//转运托盘逻辑区域查询

        Observable<BaseResponseBean<List<PalletGoodsBean>>> getPalletGoods(String palletNo);//托盘商品查询

        Observable<BaseResponseBean> addTaskDetail(AddTaskBean taskBean);//托盘商品查询

        Observable<BaseResponseBean> commitTask(StartTaskResult taskBean);//托盘商品查询

        Observable<BaseResponseBean<RunningTaskResult>> getRunningTask();
        //零货上架巷道预览
        Observable<BaseResponseBean<PalletOnPreviewResult>> getPalletOnPreview(String palletNo);
    }

    interface IIWholeShelfListView extends IBaseActivity {
        void getWholeShelfListSuccess(BaseResponseBean<InShelfResult> requestBaseBean);
    }
    abstract class WholeShelfListPresenter extends BasePresenter<WholeShelfListModel, InWholeShelfListActivity> {
        public abstract void getWholeShelfList(String containerCode, int containerType);
       // public abstract void getShelfLogic(String palletNo);//转运托盘逻辑区域查询

        //public abstract void self(int taskType,String palletNo);
    }
}
