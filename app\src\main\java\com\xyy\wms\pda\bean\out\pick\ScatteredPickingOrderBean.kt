package com.xyy.wms.pda.bean.out.pick

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

/**
 * 拣货明细bean
 */
@Parcelize
data class ScatteredPickingOrderBean(
        var soldOut: String? = null, // 拣货货位
        var productName: String? = null, // 商品名称
        var manufacturer: String? = null, // 厂家名称
        var batchNumber: String? = null, // 批号
        var sterilizingBatchNumber: String? = null, // 灭菌批号
        var specifications: String? = null, // 商品规格
        var middlePackingNumber: String? = null, // 中包装规格
        var productionDate: Long = 0, // 生产日期
        var validityDate: Long = 0, // 有效期
        var caseCode: String? = null, // 周转箱代号
        var passBoxCode: String? = null, // 周转箱号
        var realPartsNumber: Int = 0, // 周转箱中商品计划数量
        var realPickingNumber: Int = 0, // 周转箱中商品实际拣货数量
        var batchInspectionCode: String? = null,// 拣货单号
        var productCode: String? = null, // 商品编码
        var productUnit: String? = null, // 包装单位
        var specialAttributes: String? = null, // 特殊属性
        var commonName: String? = null, // 通用名
        var producingArea: String? = null, // 产地
        var orderType: Int = 0, // 订单类型 1 普通订单 2 KA订单
        var isCheckCode: Int = 0, // PDA扫描货位的校验开关  0：不检验，1：校验
        var allocationCode: String? = null, // 分配单号
        var buildingCode: String? = null, // 建筑物编码
        var channelCode: String? = null, // 业务类型编码
        var ownerCode: String? = null, // 业主编码
        var orderCode: String? = null, // 出库单号
        var storageAreaCode: String? = null, // 库区编码
        var storageRoomCode: String? = null, // 库房编码
        var storageTypeCode: String? = null, // 库别编码
        var warehouseCode: String? = null, // 仓库编码
        var orgCode: String? = null, // 机构编码
        var createTime: String? = null, // 波次下发时间
        var filllogId: Long = 0, // 补拣日志表id
        var pid: Long = 0, // 任务明细表id
        var flag: Long = 0, // 补拣不能提交异常标识
        // 自定义的催单信息
        var reminderTaskMsg: String? = null,
        var zdBatchNuber: Boolean? = false, // 是否指定批号单据	boolean	true：指定，false：未指定
        var pickType: String? = null, // 拣货类型。pickType=1  调用原接口展示详情不变，pickType=2 （只展示单据墙格号wallCode和周转箱号passBoxCode，周转箱格号CaseCode,拣货数量realPartsNumber=1）
        var erpOrderCode: String? = null // 销售单号
) : Parcelable
