package com.xyy.wms.pad.instorage.model.newinstorage

import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.api.ApiInAcceptService

import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderDetailListBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderDetailListPost
import com.xyy.wms.pda.contract.instorage.checkaccept.DetailsOfAcceptedGoodsContract
import com.xyy.wms.pda.net.RetrofitCreateHelper
import io.reactivex.Observable


/**
 * <AUTHOR>
 * @Description
 * @Date 2022/4/11
 */
class DetailsOfAcceptedGoodsModel : DetailsOfAcceptedGoodsContract.DetailsOfAcceptedGoodsContractModel {

    companion object {
        fun newInstance() : DetailsOfAcceptedGoodsModel {
            return DetailsOfAcceptedGoodsModel()
        }
    }

    override fun getCheckOrderDetailList(checkOrderDetailListPost: CheckOrderDetailListPost): Observable<BaseResponseBean<List<CheckOrderDetailListBean>>> {
        return RetrofitCreateHelper.createApi(ApiInAcceptService::class.java).getCheckOrderDetailList(checkOrderDetailListPost)
            .compose(RxHelper.rxSchedulerHelper())
    }

}
