package com.xyy.wms.pda.utils

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import androidx.exifinterface.media.ExifInterface
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

/**
 * 图片压缩工具类
 * 用于异常图片上传功能中的图片压缩处理
 */
object ImageCompressUtils {
    
    /**
     * 压缩图片
     * @param imagePath 原图片路径
     * @param quality 压缩质量 (0-100)
     * @return 压缩后的图片路径，失败返回null
     */
    fun compressImage(imagePath: String, quality: Int = 80): String? {
        return try {
            val originalFile = File(imagePath)
            if (!originalFile.exists()) {
                return null
            }
            
            // 获取图片信息
            val options = BitmapFactory.Options()
            options.inJustDecodeBounds = true
            BitmapFactory.decodeFile(imagePath, options)
            
            // 计算压缩比例
            val sampleSize = calculateInSampleSize(options, 1080, 1920)
            options.inSampleSize = sampleSize
            options.inJustDecodeBounds = false
            
            // 解码图片
            val bitmap = BitmapFactory.decodeFile(imagePath, options) ?: return null
            
            // 处理图片旋转
            val rotatedBitmap = rotateImageIfRequired(bitmap, imagePath)
            
            // 创建压缩后的文件
            val compressedFile = File(
                originalFile.parent,
                "compressed_${System.currentTimeMillis()}.jpg"
            )
            
            // 压缩并保存
            val outputStream = FileOutputStream(compressedFile)
            rotatedBitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream)
            outputStream.flush()
            outputStream.close()
            
            // 回收bitmap
            if (rotatedBitmap != bitmap) {
                bitmap.recycle()
            }
            rotatedBitmap.recycle()
            
            compressedFile.absolutePath
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    /**
     * 计算图片压缩比例
     */
    private fun calculateInSampleSize(
        options: BitmapFactory.Options,
        reqWidth: Int,
        reqHeight: Int
    ): Int {
        val height = options.outHeight
        val width = options.outWidth
        var inSampleSize = 1
        
        if (height > reqHeight || width > reqWidth) {
            val halfHeight = height / 2
            val halfWidth = width / 2
            
            while ((halfHeight / inSampleSize) >= reqHeight && 
                   (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2
            }
        }
        
        return inSampleSize
    }
    
    /**
     * 根据EXIF信息旋转图片
     */
    private fun rotateImageIfRequired(bitmap: Bitmap, imagePath: String): Bitmap {
        return try {
            val exif = ExifInterface(imagePath)
            val orientation = exif.getAttributeInt(
                ExifInterface.TAG_ORIENTATION,
                ExifInterface.ORIENTATION_NORMAL
            )
            
            when (orientation) {
                ExifInterface.ORIENTATION_ROTATE_90 -> rotateImage(bitmap, 90f)
                ExifInterface.ORIENTATION_ROTATE_180 -> rotateImage(bitmap, 180f)
                ExifInterface.ORIENTATION_ROTATE_270 -> rotateImage(bitmap, 270f)
                else -> bitmap
            }
        } catch (e: IOException) {
            bitmap
        }
    }
    
    /**
     * 旋转图片
     */
    private fun rotateImage(bitmap: Bitmap, degrees: Float): Bitmap {
        val matrix = Matrix()
        matrix.postRotate(degrees)
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
    }
    
    /**
     * 获取图片文件大小（字节）
     */
    fun getImageFileSize(imagePath: String): Long {
        val file = File(imagePath)
        return if (file.exists()) file.length() else 0
    }
    
    /**
     * 检查图片格式是否支持
     */
    fun isSupportedFormat(imagePath: String): Boolean {
        val extension = imagePath.substringAfterLast('.', "").lowercase()
        return extension in listOf("jpg", "jpeg", "png")
    }
}
