package com.xyy.wms.pda.presenter.instorage.scatteredwholeshelf

import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.shelf.InShelfResult
import com.xyy.wms.pda.bean.productCode.ProductCode
import com.xyy.wms.pda.contract.instorage.scatteredwholeshelf.ScatteredWholeShelfListContract
import com.xyy.wms.pda.helper.SimpleErrorConsumer
import com.xyy.wms.pda.helper.SimpleSuccessConsumer
import com.xyy.wms.pda.model.instorage.scatteredwholeshelf.ScatteredWholeShelfListModel
import com.xyy.wms.pda.presenter.productCode.GetProductCodePresenter
/**
 *  整散合一列表上架
 */
class ScatteredWholeShelfListPresenter : ScatteredWholeShelfListContract.ScatteredWholeShelfListPresenter(), GetProductCodePresenter {
    override fun getModel(): ScatteredWholeShelfListModel {
       return ScatteredWholeShelfListModel.newInstance()
    }
    override fun getScatteredShelfList(containerCode: String, shelfType: Int) {
        mRxManager.register(mIModel.getScatteredShelfList(containerCode, shelfType).subscribe(object : SimpleSuccessConsumer<BaseResponseBean<InShelfResult>>(mIView) {
            override fun onSuccess(baseResponseBean: BaseResponseBean<InShelfResult>) {
                mIView.getScatteredWholeShelfListSuccess(baseResponseBean)
            }
        }, SimpleErrorConsumer(mIView)))
    }

    override fun getProductBarCode(ownerCode: String, packageBarCode: String) {

        mRxManager.register(mIModel.getProductBarCode(ownerCode, packageBarCode).subscribe(object : SimpleSuccessConsumer<BaseResponseBean<List<ProductCode>>>(mIView) {

            override fun onSuccess(baseResponseBean: BaseResponseBean<List<ProductCode>>) {
                mIView.getProductCodeSuccess(baseResponseBean)
            }
        }, SimpleErrorConsumer(mIView)))
    }

    companion object {
        fun newInstance(): ScatteredWholeShelfListPresenter {
            return ScatteredWholeShelfListPresenter()
        }
    }
}
