package com.xyy.wms.pda.contract.common

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseFragment
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.api.ApiService
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.common.DictListByNameAndType
import com.xyy.wms.pda.helper.SimpleErrorConsumer
import com.xyy.wms.pda.helper.SimpleSuccessConsumer
import com.xyy.wms.pda.net.RetrofitCreateHelper
import io.reactivex.Observable

/**
 * Created by lwj on 2020/4/22
 * <EMAIL>
 */

interface IDictListByNameAndTypeModel : IBaseModel {
    fun getDictListByNameAndType(dictName: String, dictType: String): Observable<BaseResponseBean<List<DictListByNameAndType>>>
}

interface IStorageWaitShelfCountViewActivity : IBaseActivity {
    fun getDictListByNameAndTypeSuccess(requestBaseBean: BaseResponseBean<List<DictListByNameAndType>>)
}

interface IStorageWaitShelfCountViewFragment : IBaseFragment {
    fun getDictListByNameAndTypeSuccess(requestBaseBean: BaseResponseBean<List<DictListByNameAndType>>)
}

abstract class IDictListByNameAndTypeActivityPresenter : BasePresenter<IDictListByNameAndTypeModel, IStorageWaitShelfCountViewActivity>() {
    abstract fun getDictListByNameAndType(dictName: String, dictType: String)
}

abstract class IDictListByNameAndTypeFragmentPresenter : BasePresenter<IDictListByNameAndTypeModel, IStorageWaitShelfCountViewActivity>() {
    abstract fun getDictListByNameAndType(dictName: String, dictType: String)
}

class DictListByNameAndTypeModel : IDictListByNameAndTypeModel {
    override fun getDictListByNameAndType(dictName: String, dictType: String): Observable<BaseResponseBean<List<DictListByNameAndType>>> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).getDictListByNameAndType(dictName, dictType)
                .compose(RxHelper.rxSchedulerHelper())
    }
}

class DictListByNameAndTypeActivityPresenter : IDictListByNameAndTypeActivityPresenter() {
    override fun getDictListByNameAndType(dictName: String, dictType: String) {
        mRxManager.register(mIModel.getDictListByNameAndType(dictName, dictType).subscribe(object : SimpleSuccessConsumer<BaseResponseBean<List<DictListByNameAndType>>>(mIView) {
            override fun onSuccess(t: BaseResponseBean<List<DictListByNameAndType>>) {
                mIView.getDictListByNameAndTypeSuccess(t)
            }
        }, SimpleErrorConsumer(mIView)))
    }

    override fun getModel(): IDictListByNameAndTypeModel {
        return DictListByNameAndTypeModel()
    }
}

class DictListByNameAndTypeFragmentPresenter : IDictListByNameAndTypeFragmentPresenter() {

    override fun getDictListByNameAndType(dictName: String, dictType: String) {
        mRxManager.register(mIModel.getDictListByNameAndType(dictName, dictType).subscribe(object : SimpleSuccessConsumer<BaseResponseBean<List<DictListByNameAndType>>>(mIView) {
            override fun onSuccess(t: BaseResponseBean<List<DictListByNameAndType>>) {
                mIView.getDictListByNameAndTypeSuccess(t)
            }
        }, SimpleErrorConsumer(mIView)))
    }

    override fun getModel(): IDictListByNameAndTypeModel {
        return DictListByNameAndTypeModel()
    }
}