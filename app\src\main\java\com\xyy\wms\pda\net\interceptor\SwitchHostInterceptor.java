package com.xyy.wms.pda.net.interceptor;

import android.net.Uri;
import android.text.TextUtils;

import com.xyy.wms.pda.utils.SharedPrefManager;

import java.io.IOException;

import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 切换Host拦截器
 * 注意：如果出现多个baseUrl时及时添加忽略host
 */
public class SwitchHostInterceptor implements Interceptor {
    @Override
    public Response intercept(Interceptor.Chain chain) throws IOException {
        Request oldRequest = chain.request();
        String hostUrl = SharedPrefManager.getInstance().getHostUrl();

        if (!TextUtils.isEmpty(hostUrl)) {
            Uri parse = Uri.parse(hostUrl);
            String host = parse.getHost();
            String scheme = parse.getScheme();
            int port = parse.getPort() > 0 ? parse.getPort() : 80;

            HttpUrl url = oldRequest.url();
            // 忽略测试环境的下载地址（正式环境因为没有使用该拦截器，无需忽略）
            String oldHost = Uri.parse(String.valueOf(url)).getHost();
            if (!TextUtils.isEmpty(oldHost) && oldHost.equals("downloads.test.ybm100.com")) {
                return chain.proceed(oldRequest);
            }
            // 修改请求地址
            HttpUrl.Builder httpUrl = url
                    .newBuilder()
                    .scheme(scheme)
                    .host(host)
                    .port(port);
            Request newRequest = oldRequest.newBuilder()
                    .url(httpUrl.build())
                    .build();
            return chain.proceed(newRequest);
        }
        return chain.proceed(oldRequest);
    }

}
