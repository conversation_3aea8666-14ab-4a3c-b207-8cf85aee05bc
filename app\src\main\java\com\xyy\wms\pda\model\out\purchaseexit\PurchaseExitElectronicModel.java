package com.xyy.wms.pda.model.out.purchaseexit;

import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.wms.pda.api.ApiInExitService;
import com.xyy.wms.pda.api.ApiService;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.productCode.ProductCode;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseRefundOrderDetailsBean;
import com.xyy.wms.pda.contract.out.purchaseexit.PurchaseExitElectronicContract;
import com.xyy.wms.pda.net.RetrofitCreateHelper;

import java.util.List;

import io.reactivex.Observable;


/**
 * Created by zcj on 2018/11/19 21
 */
public class PurchaseExitElectronicModel implements PurchaseExitElectronicContract.IPurchaseExitElectronicModel {
    public static PurchaseExitElectronicModel newInstance() {
        return new PurchaseExitElectronicModel();
    }

    @Override
    public Observable<BaseResponseBean<List<PurchaseRefundOrderDetailsBean>>> queryUnScannedGoods(String containerCode) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).queryUnScannedGoods(containerCode)
                .compose(RxHelper.<BaseResponseBean<List<PurchaseRefundOrderDetailsBean>>>rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean> updateWholeSingleUnlock(String refundOrderCode) {
        return RetrofitCreateHelper.createApi(ApiInExitService.class).updateWholeSingleUnlock(refundOrderCode)
                .compose(RxHelper.<BaseResponseBean>rxSchedulerHelper());
    }

    @Override
    public Observable<BaseResponseBean<List<ProductCode>>> selectProductPda(String ownerCode, String packageBarCode) {
        return RetrofitCreateHelper.createApi(ApiService.class).selectProductPda(ownerCode, packageBarCode)
                .compose(RxHelper.<BaseResponseBean<List<ProductCode>>>rxSchedulerHelper());
    }
}
