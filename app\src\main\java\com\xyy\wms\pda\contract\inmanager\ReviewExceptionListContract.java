package com.xyy.wms.pda.contract.inmanager;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.inmanager.ReviewExceptionListBean;
import com.xyy.wms.pda.bean.inmanager.post.ReviewExceptionPostBean;

import io.reactivex.Observable;

/**
 * Created by zcj on 2018/11/14 10
 * 内复核异常列表
 */
public interface ReviewExceptionListContract {

    abstract class ReviewExceptionListPresenter extends BasePresenter<IReviewExceptionListModel, IReviewExceptionListView> {
        /**
         * exceptionCause 异常原因 （1.少货 2.多货）	number
         * exceptionType  异常类型 (1.零货拣货 2.内复核)	number
         * pageNum        当前页	number
         * pageSize       每页的数量	number
         */
        public abstract void exceptionPage(boolean refresh, ReviewExceptionPostBean bean);

        /**
         * dealUserName 处理人	string
         * id           异常表主键	string
         * isAskFor     是否索取 （0 未索取 1 已索取）	number
         * status       处理状态 1 异常 2 异常处理完成	number
         */
        public abstract void updateException(String dealMode,
                                             String dealUserName,
                                             String id,
                                             String isAskFor,
                                             String status,
                                             String remark);
    }

    interface IReviewExceptionListModel extends IBaseModel {

        Observable<BaseResponseBean<ReviewExceptionListBean>> exceptionPage(ReviewExceptionPostBean bean);

        Observable<BaseResponseBean<String>> updateException(String dealMode,
                                                             String dealUserName,
                                                             String id,
                                                             String isAskFor,
                                                             String status,
                                                             String remark);

    }

    interface IReviewExceptionListView extends IBaseActivity {

        void exceptionPageSuccess(boolean refresh, BaseResponseBean<ReviewExceptionListBean> bean);

        void updateExceptionSuccess(BaseResponseBean<String> bean);

        void showNetworkError();

        void enableLoadMore(boolean b);

        void loadMoreComplete();
    }
}
