package com.xyy.wms.pda.contract.out.purchaseexit;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseCommitResult;
import com.xyy.wms.pda.bean.purchaseexit.PurchaseExitDetail;

import java.util.List;
import java.util.Map;

import io.reactivex.Observable;

/**
 * created by  liangxing  on 2020-01-15.
 */
public interface PurchaseExitDetailContract {

    interface IPurchaseExitOrderModel extends IBaseModel {
        Observable<BaseResponseBean<List<PurchaseExitDetail>>> queryExecutionDetailList(Map<String, Object> map);
        Observable<BaseResponseBean<PurchaseCommitResult>> commitPurchaseExitDetail(PurchaseExitDetail purchaseExitDetail);
    }

    interface IPurchaseExitOrderView extends IBaseActivity {
        void queryExecutionDetailListSuccess(List<PurchaseExitDetail> bean);
        void commitPurchaseExitDetail(PurchaseCommitResult bean);
    }
}
