package com.xyy.wms.pda.model.instorage.checkaccept

import com.xyy.utilslibrary.base.BaseModel
import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.api.ApiInAcceptServiceNew
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckAcceptBillNew
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckAcceptGoodsResult
import com.xyy.wms.pda.bean.instorage.checkaccept.WaitCheckInfoBySupplierPost
import com.xyy.wms.pda.bean.instorage.checkaccept.WaitCheckInfoBySupplierBean
import com.xyy.wms.pda.contract.instorage.checkaccept.CheckAcceptBillContractNew
import com.xyy.wms.pda.net.RetrofitCreateHelper
import io.reactivex.Observable

/**
 * 入库验收单（New）Model：调用新 Api，返回新版 Bean
 */
class CheckAcceptBillModelNew : BaseModel(), CheckAcceptBillContractNew.ICheckAcceptBillModelNew {
    override fun getWaitCheckInfoBySupplier(post: WaitCheckInfoBySupplierPost): Observable<BaseResponseBean<MutableList<WaitCheckInfoBySupplierBean>>> {
        return RetrofitCreateHelper.createApi(ApiInAcceptServiceNew::class.java)
            .getWaitCheckInfoBySupplier(post)
            .compose(RxHelper.rxSchedulerHelper())
    }

    companion object {
        fun newInstance(): CheckAcceptBillModelNew = CheckAcceptBillModelNew()
    }
}
