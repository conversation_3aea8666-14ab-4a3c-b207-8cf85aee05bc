package com.xyy.wms.pda.bean.instorage.checkaccept

import java.io.Serializable

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/17
 */
data class SaveProductInfoMaintainPost (
    var id : Long,
    var productCode : String,                                                   // 编码编码
    var storageAttribute : String,                                              // 存放属性 （1：异性品，2：液体，3：易碎品，4：易差错，5：特殊品 ）
    var ifErrorProne : String,                                                  // 是否易收错 0 否 1是
    var smallProductPackingVO : SmallProductPackingVO,                          // 小包装信息
    var largeProductPackingVO : LargeProductPackingVO,                          // 大包装信息
    var lhkLogicalregionRelation : LhkLogicalregionRelation,                    // 零货库商品和货位逻辑区
    var zjkLogicalregionRelation : ZjkLogicalregionRelation                    // 零货库商品和货位逻辑区
) : Serializable

data class SmallProductPackingVO (
    var id : Long,                            // 主键id
    var packingType : String,                   // 1小，2中，3大
    var packingNumber : String,                 // 包装数量
    var packingLong : Float,                   // 长
    var packingWide : Float,                   // 宽
    var packingHigh : Float,                   // 高
    var packingVolume : Float,                 // 体积
    var packingWeight : Float                 // 重量
) : Serializable

data class LargeProductPackingVO (
    var id : Long,                            // 主键id
    var packingType : String,                   // 1小，2中，3大
    var packingNumber : String,                 // 包装数量
    var packingLong : Float,                   // 长
    var packingWide : Float,                   // 宽
    var packingHigh : Float,                   // 高
    var packingVolume : Float,                 // 体积
    var packingWeight : Float                 // 重量
) : Serializable

data class LhkLogicalregionRelation (
    var id : String,                            // 逻辑区 id id 为空说明是新增 id不为空说明是编辑
    var storageRoomCode : String,               // 库房编号
    var storageRoomName : String,               // 库房
    var goodspositionAbcType : String,          // 货位ABC分类编号(整)
    var goodspositionAbcTypeName : String,      //
    var logicalRegionCode : String,             // 逻辑区域编号字符串(整)(,LHK-FY-01,LHK-FY-02,LHK-FY-03,)
    var logicalRegionName : String,             // 逻辑区域值字符串
    var stockUpper : String,                    // 库存上限
    var stockLower : String                    // 库存下限
) : Serializable

data class ZjkLogicalregionRelation (
    var id : String,                            // 逻辑区 id id 为空说明是新增 id不为空说明是编辑
    var storageRoomCode : String,               // 库房编号
    var storageRoomName : String,               // 库房
    var goodspositionAbcType : String,          // 货位ABC分类编号(整)
    var goodspositionAbcTypeName : String,      //
    var logicalRegionCode : String,             // 逻辑区域编号字符串(整)(,LHK-FY-01,LHK-FY-02,LHK-FY-03,)
    var logicalRegionName : String,             // 逻辑区域值字符串
    var stockUpper : String,                    // 库存上限
    var stockLower : String                    // 库存下限
) : Serializable
