package com.xyy.wms.pda.presenter.login;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.utils.ViewUtils;
import com.xyy.wms.pda.bean.base.BaseResponseBean;
import com.xyy.wms.pda.bean.home.HomeListBean;
import com.xyy.wms.pda.bean.home.MenuBean;
import com.xyy.wms.pda.bean.update.VersionBeanInfo;
import com.xyy.wms.pda.bean.update.VersionInfo;
import com.xyy.wms.pda.contract.login.MainContract;
import com.xyy.wms.pda.bean.home.HomeListBeanNew;
import com.xyy.wms.pda.bean.home.MenuBeanNew;
import com.xyy.wms.pda.helper.SimpleErrorConsumer;
import com.xyy.wms.pda.helper.SimpleSuccessConsumer;
import com.xyy.wms.pda.model.login.MainModel;
import com.xyy.wms.pda.ui.adapter.home.HomeMenuConverter;

import java.util.List;

public class MainPresenter extends BasePresenter<MainContract.IMainModel, MainContract.IMainView> {

    @NonNull
    public static MainPresenter newInstance() {
        return new MainPresenter();
    }

    @Override
    protected MainContract.IMainModel getModel() {
        return MainModel.newInstance();
    }

    public void getModuleList() {
        if (mRxManager == null || mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.getModules().subscribe(new SimpleSuccessConsumer<BaseResponseBean<HomeListBeanNew>>(mIView, "加载中...") {
            @Override
            public void onSuccess(BaseResponseBean<HomeListBeanNew> bean) {
                mIView.showModuleList(bean.getResult());
            }
        }, new SimpleErrorConsumer(mIView)));
    }

    public void onItemClick(int position, MenuBeanNew item) {
        if (!ViewUtils.isClickable()) return;
        if (!TextUtils.isEmpty(item.getCode())) {
            HomeMenuConverter.Converter converter = HomeMenuConverter.getConverter(item.getCode());
            if (converter != null && converter.itemClick != null) {
                converter.itemClick.onClick(mIView, item);
            }
            mIView.dismissMenuLayout();
        }
    }

    public void checkUpdate(VersionBeanInfo versionBeanInfo) {
        if (mRxManager == null || mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.checkUpdate(versionBeanInfo).subscribe(new SimpleSuccessConsumer<BaseResponseBean<VersionInfo>>(mIView) {
            @Override
            public void onSuccess(BaseResponseBean<VersionInfo> bean) {
                mIView.checkUpdateSuccess(bean.getResult());
            }
        }, new SimpleErrorConsumer(mIView)));
    }

}
