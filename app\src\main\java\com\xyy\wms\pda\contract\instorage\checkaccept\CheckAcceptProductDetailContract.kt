package com.xyy.wms.pda.contract.instorage.checkaccept

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckContainerPost
import com.xyy.wms.pda.bean.instorage.checkaccept.TurnDownPost
import io.reactivex.Observable

/**
 * Created by XyyMvpPdaTemplate on 03/04/2020 16:11
 */
interface CheckAcceptProductDetailContract {
    interface ICheckAcceptProductDetailModel : IBaseModel {
        fun turnDown(turnDownPost: TurnDownPost): Observable<BaseResponseBean<Any>>
        fun checkContainer(checkContainerPost: CheckContainerPost): Observable<BaseResponseBean<Int>>
    }

    interface ICheckAcceptProductDetailView : IBaseActivity {
        fun turnDownSuccess(baseResponseBean: BaseResponseBean<Any>)
        fun checkContainerSuccess(baseResponseBean: BaseResponseBean<Int>)
    }
}
